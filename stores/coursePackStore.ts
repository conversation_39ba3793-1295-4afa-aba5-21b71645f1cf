import type {
  AdminCourseDetailData,
  AdminCoursePack,
  CoursePackWithCoursesResponse,
} from '~/types/course'
import { defineStore } from 'pinia'

export enum CoursePackStatusEnum {
  IDLE = 'idle',
  LOADING = 'loading',
  LOADED = 'loaded',
  ERROR = 'error',
}

/**
 * 课程包管理Store
 * 负责管理课程包列表、当前选中的课程包、课程列表等状态
 */
export const useCoursePackStore = defineStore(
  'coursePack',
  () => {
    // State
    const status = ref<CoursePackStatusEnum>(CoursePackStatusEnum.IDLE)
    const error = ref<Error | null>(null)

    // 课程包列表
    const coursePackList = ref<AdminCoursePack[]>([])
    const coursePackListLoading = ref(false)

    // 当前选中的课程包
    const currentCoursePack = ref<CoursePackWithCoursesResponse | null>(null)
    const currentCoursePackLoading = ref(false)

    // 当前选中的课程详情
    const currentCourse = ref<AdminCourseDetailData | null>(null)
    const currentCourseLoading = ref(false)

    // 当前选中的课程包ID和课程ID
    const selectedCoursePackId = ref<string | null>(null)
    const selectedCourseId = ref<string | null>(null)

    // Computed
    const isLoading = computed(() =>
      coursePackListLoading.value
      || currentCoursePackLoading.value
      || currentCourseLoading.value,
    )

    const hasError = computed(() => error.value !== null)

    const currentCourseList = computed(() =>
      currentCoursePack.value?.courses || [],
    )

    const currentCourseIndex = computed(() => {
      if (selectedCourseId.value === null || currentCourseList.value.length === 0) {
        return -1
      }
      return currentCourseList.value.findIndex(course => course.id === selectedCourseId.value)
    })

    const hasNextCourse = computed(() => {
      const index = currentCourseIndex.value
      return index >= 0 && index < currentCourseList.value.length - 1
    })

    const hasPrevCourse = computed(() => {
      const index = currentCourseIndex.value
      return index > 0
    })

    // Actions
    function setError(err: Error | null) {
      error.value = err
      if (err) {
        status.value = CoursePackStatusEnum.ERROR
      }
    }

    function clearError() {
      error.value = null
      if (status.value === CoursePackStatusEnum.ERROR) {
        status.value = CoursePackStatusEnum.IDLE
      }
    }

    async function fetchCoursePackList() {
      if (coursePackListLoading.value)
        return

      coursePackListLoading.value = true
      clearError()

      try {
        const { $trpc } = useNuxtApp()
        const data = await $trpc.adminCoursePack.listMyCoursePacks.query()
        coursePackList.value = data || []
        status.value = CoursePackStatusEnum.LOADED
      }
      catch (err) {
        const error = err instanceof Error ? err : new Error('获取课程包列表失败')
        setError(error)
        coursePackList.value = []
      }
      finally {
        coursePackListLoading.value = false
      }
    }

    async function fetchCoursePackDetail(coursePackId: string) {
      if (currentCoursePackLoading.value)
        return

      currentCoursePackLoading.value = true
      clearError()

      try {
        const { $trpc } = useNuxtApp()
        const data = await $trpc.adminCoursePack.getCoursePackWithCourses.query({
          coursePackId,
        })
        currentCoursePack.value = data || null
        selectedCoursePackId.value = coursePackId
        status.value = CoursePackStatusEnum.LOADED
      }
      catch (err) {
        const error = err instanceof Error ? err : new Error('获取课程包详情失败')
        setError(error)
        currentCoursePack.value = null
      }
      finally {
        currentCoursePackLoading.value = false
      }
    }

    async function fetchCourseDetail(coursePackId: string, courseId: string) {
      if (currentCourseLoading.value)
        return

      currentCourseLoading.value = true
      clearError()

      try {
        const { $trpc } = useNuxtApp()
        const data = await $trpc.adminCourse.getCourseDetail.query({
          coursePackId,
          courseId,
        })
        currentCourse.value = data || null
        selectedCourseId.value = courseId

        // 如果当前课程包不匹配，也获取课程包信息
        if (selectedCoursePackId.value !== coursePackId) {
          await fetchCoursePackDetail(coursePackId)
        }

        status.value = CoursePackStatusEnum.LOADED
      }
      catch (err) {
        const error = err instanceof Error ? err : new Error('获取课程详情失败')
        setError(error)
        currentCourse.value = null
      }
      finally {
        currentCourseLoading.value = false
      }
    }

    function selectCoursePack(coursePackId: string) {
      if (selectedCoursePackId.value !== coursePackId) {
        selectedCourseId.value = null
        currentCourse.value = null
        void fetchCoursePackDetail(coursePackId)
      }
    }

    function selectCourse(courseId: string) {
      if (selectedCourseId.value !== courseId && selectedCoursePackId.value !== null) {
        void fetchCourseDetail(selectedCoursePackId.value, courseId)
      }
    }

    function goToNextCourse() {
      if (!hasNextCourse.value)
        return

      const nextIndex = currentCourseIndex.value + 1
      const nextCourse = currentCourseList.value[nextIndex]
      if (nextCourse !== undefined && selectedCoursePackId.value !== null) {
        selectCourse(nextCourse.id)
      }
    }

    function goToPrevCourse() {
      if (!hasPrevCourse.value)
        return

      const prevIndex = currentCourseIndex.value - 1
      const prevCourse = currentCourseList.value[prevIndex]
      if (prevCourse !== undefined && selectedCoursePackId.value !== null) {
        selectCourse(prevCourse.id)
      }
    }

    function reset() {
      status.value = CoursePackStatusEnum.IDLE
      error.value = null
      coursePackList.value = []
      currentCoursePack.value = null
      currentCourse.value = null
      selectedCoursePackId.value = null
      selectedCourseId.value = null
      coursePackListLoading.value = false
      currentCoursePackLoading.value = false
      currentCourseLoading.value = false
    }

    function setCurrentCourse(course: AdminCourseDetailData | null | undefined) {
      currentCourse.value = course ?? null
    }

    return {
      // State
      status,
      error,
      coursePackList,
      coursePackListLoading,
      currentCoursePack,
      currentCoursePackLoading,
      currentCourse,
      currentCourseLoading,
      selectedCoursePackId,
      selectedCourseId,

      // Computed
      isLoading,
      hasError,
      currentCourseList,
      currentCourseIndex,
      hasNextCourse,
      hasPrevCourse,

      // Actions
      setError,
      clearError,
      fetchCoursePackList,
      fetchCoursePackDetail,
      fetchCourseDetail,
      selectCoursePack,
      selectCourse,
      goToNextCourse,
      goToPrevCourse,
      reset,
      setCurrentCourse,
    }
  },
  {
    persist: false,
  },
)

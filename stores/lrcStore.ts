import type { LrcLine, LrcMetadata, LrcParseResult } from '~/types/lrcTypes'
import { findIndex, isEmpty } from 'lodash-es'
import { defineStore } from 'pinia'
import { parseLrc } from '~/utils/formats/parsers/lrcParser'

/**
 * LRC歌词管理Store
 * 负责管理歌词文件的加载、解析、状态追踪和持久化存储
 * 提供歌词文本、时间轴、元数据等状态的管理，以及当前播放位置的行级和词级定位功能
 */
export const useLrcStore = defineStore(
  'lrc',
  () => {
    // State
    /** 原始LRC文件内容 */
    const rawLrcContent = ref<string | null>(null)
    /** 解析后的增强型LRC行数组，包含时间戳和可能的逐字时间信息 */
    const lrcLines = ref<LrcLine[]>([])
    /** 纯文本歌词行数组 */
    const plainTextLines = ref<string[]>([])
    /** LRC文件元数据，如歌曲标题、艺术家、专辑等信息 */
    const metadata = ref<LrcMetadata>({})
    /** 错误信息，当解析或处理过程中出现问题时设置 */
    const error = ref<string | null>(null)

    // Getters
    /** 检查是否已加载LRC数据 */
    const hasLrc = computed(() => !isEmpty(lrcLines.value))

    /**
     * 根据当前播放时间计算应该高亮显示的歌词行索引
     * @param currentTime 当前播放时间（毫秒）
     * @returns 当前时间对应的歌词行索引，如果没有匹配则返回-1
     */
    const activeLineIndex = computed(() => (currentTime: number) => {
      return findIndex(lrcLines.value, (line, idx) => {
        const nextLine = lrcLines.value[idx + 1]
        return currentTime >= line.time && (!isEmpty(nextLine) ? currentTime < nextLine.time : true)
      })
    })

    /**
     * 获取指定行的上一行索引
     * @param currentIndex 当前行索引
     * @returns 上一行索引，如果当前是第一行则返回-1
     */
    const previousLineIndex = computed(() => (currentIndex: number) => {
      return currentIndex > 0 ? currentIndex - 1 : -1
    })

    /**
     * 获取指定行的下一行索引
     * @param currentIndex 当前行索引
     * @returns 下一行索引，如果当前是最后一行则返回-1
     */
    const nextLineIndex = computed(() => (currentIndex: number) => {
      return currentIndex < lrcLines.value.length - 1 ? currentIndex + 1 : -1
    })

    // Actions
    /**
     * 上传并解析LRC文件
     * @param file 要上传的LRC文件对象
     * @throws 如果文件读取或解析失败，会设置error状态
     */
    async function uploadLrc(file: File) {
      try {
        const content = await file.text()
        console.log('%c AT 🥝 content 🥝-70', 'font-size:13px; background:#545641; color:#989a85;', content)
        parseLrcContent(content)
      }
      catch (e) {
        error.value = e instanceof Error ? e.message : '解析 LRC 文件失败'
        console.error('解析 LRC 文件失败:', e)
      }
    }

    /**
     * 解析LRC文本内容
     * @param content LRC文本内容
     * @throws 如果解析失败，会设置error状态
     */
    function parseLrcContent(content: string) {
      try {
        const result = parseLrc(content)
        updateLrcContent(content, result)
        error.value = null
      }
      catch (e) {
        error.value = e instanceof Error ? e.message : '解析 LRC 内容失败'
        console.error('解析 LRC 内容失败:', e)
      }
    }

    /**
     * 更新 lrc 内容
     */
    function updateLrcContent(content: string, result: LrcParseResult) {
      console.log('%c AT 🥝 content 🥝-97', 'font-size:13px; background:#66638b; color:#aaa7cf;', content)
      console.log('%c AT 🥝 result 🥝-97', 'font-size:13px; background:#9bd443; color:#dfff87;', result)
      rawLrcContent.value = content
      lrcLines.value = result.lrcLines
      plainTextLines.value = result.plainTextLines
      metadata.value = result.metadata
    }

    /**
     * 重置所有LRC相关状态到初始值
     * 清除已加载的歌词内容、行数据、元数据和错误信息
     */
    function resetLrc() {
      rawLrcContent.value = null
      lrcLines.value = []
      plainTextLines.value = []
      metadata.value = {}
      error.value = null
    }

    return {
      // State
      rawLrcContent,
      lrcLines,
      plainTextLines,
      metadata,
      error,
      // Getters
      hasLrc,
      activeLineIndex,
      previousLineIndex,
      nextLineIndex,
      // Actions
      uploadLrc,
      parseLrcContent,
      resetLrc,
      updateLrcContent,
    }
  },
  {
    persist: false, // 启用 Pinia 状态持久化
  },
)

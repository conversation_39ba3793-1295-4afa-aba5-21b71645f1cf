/**
 * 摘要数据验证模块
 */

export interface ValidationResult {
  valid: boolean
  errors: string[]
  status?: 'success' | 'error'
  message?: string
}

/**
 * 验证摘要数据
 */
export function validSummary(summary: unknown): ValidationResult {
  const errors: string[] = []

  if (!summary) {
    return {
      valid: false,
      errors: ['摘要数据不能为空'],
      status: 'error',
      message: 'Invalid response format',
    }
  }

  if (typeof summary !== 'object' || Array.isArray(summary)) {
    return {
      valid: false,
      errors: ['摘要数据必须是对象类型'],
      status: 'error',
      message: 'Invalid response format',
    }
  }

  const summaryObj = summary as Record<string, unknown>

  // 检查terms字段
  if (!summaryObj.terms) {
    return {
      valid: false,
      errors: ['缺少terms字段'],
      status: 'error',
      message: 'Invalid response format',
    }
  }

  if (!Array.isArray(summaryObj.terms)) {
    return {
      valid: false,
      errors: ['terms必须是数组类型'],
      status: 'error',
      message: 'Invalid response format',
    }
  }

  // 检查terms数组中的每一项
  for (const term of summaryObj.terms) {
    if (term === null || typeof term !== 'object' || Array.isArray(term)) {
      return {
        valid: false,
        errors: ['term项必须是对象类型'],
        status: 'error',
        message: 'Invalid response format',
      }
    }

    const termObj = term as Record<string, unknown>
    if (!termObj.src || !termObj.tgt || !termObj.note) {
      return {
        valid: false,
        errors: ['term项缺少必需字段: src, tgt, note'],
        status: 'error',
        message: 'Invalid response format',
      }
    }
  }

  // 检查content字段（可选，向后兼容）
  if (summaryObj.content && typeof summaryObj.content === 'string' && summaryObj.content.length < 10) {
    errors.push('摘要内容过短')
  }

  return {
    valid: errors.length === 0,
    errors,
    status: 'success',
    message: 'Summary completed',
  }
}

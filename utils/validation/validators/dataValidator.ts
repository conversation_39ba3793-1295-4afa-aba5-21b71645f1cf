/**
 * 数据验证模块
 * 处理复杂数据结构的验证
 */

import type { Subtitle } from '~/types/subtitle'

/**
 * 验证字幕对象的基本结构
 */
export function isValidSubtitle(subtitle: unknown): subtitle is Subtitle {
  return !!(
    subtitle &&
    typeof subtitle === 'object' &&
    typeof (subtitle as Record<string, unknown>).uuid === 'string' &&
    typeof (subtitle as Record<string, unknown>).id === 'number' &&
    typeof (subtitle as Record<string, unknown>).startTime === 'string' &&
    typeof (subtitle as Record<string, unknown>).endTime === 'string' &&
    typeof (subtitle as Record<string, unknown>).text === 'string' &&
    typeof (subtitle as Record<string, unknown>).translationText === 'string'
  )
}

/**
 * 验证字幕数组
 */
export function isValidSubtitleArray(subtitles: unknown): subtitles is Subtitle[] {
  return Array.isArray(subtitles) && subtitles.every(isValidSubtitle)
}

/**
 * 验证对象是否具有必需的属性
 */
export function hasRequiredProperties<T extends Record<string, unknown>>(obj: unknown, requiredProps: (keyof T)[]): obj is T {
  if (!obj || typeof obj !== 'object') {
    return false
  }

  return requiredProps.every((prop) => prop in obj)
}

/**
 * 验证数组中的元素是否都符合条件
 */
export function validateArrayElements<T>(arr: unknown[], validator: (item: unknown) => item is T): arr is T[] {
  return Array.isArray(arr) && arr.every(validator)
}

/**
 * 验证嵌套对象结构
 */
export function validateNestedObject(obj: unknown, schema: Record<string, (value: unknown) => boolean>): boolean {
  if (!obj || typeof obj !== 'object') {
    return false
  }

  return Object.entries(schema).every(([key, validator]) => {
    return key in obj && validator((obj as Record<string, unknown>)[key])
  })
}

/**
 * 验证配置对象
 */
export function isValidConfig(config: unknown): boolean {
  return !!(config && typeof config === 'object' && !Array.isArray(config))
}

/**
 * 验证时间戳数据对象
 */
export function isValidTimestampData(data: unknown): boolean {
  if (!data || typeof data !== 'object') {
    return false
  }

  const obj = data as Record<string, unknown>
  return (
    typeof obj.text === 'string' &&
    (obj.start === undefined || typeof obj.start === 'string' || typeof obj.start === 'number') &&
    (obj.end === undefined || typeof obj.end === 'string' || typeof obj.end === 'number')
  )
}

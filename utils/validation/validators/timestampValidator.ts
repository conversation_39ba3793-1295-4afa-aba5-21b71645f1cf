import type { TimestampData, TranslationData, ValidationResult, TimestampDataType } from '~/utils/processing/time/timestampTypes'
import { isValidTimestamp } from '../../processing/time/timeValidator'
import { timestampToSeconds } from '../../processing/time/timeParser'

/**
 * 验证时间戳数据
 */
export function validateTimestampData(data: TimestampData[], expectedType?: TimestampDataType): ValidationResult {
  const errors: string[] = []
  const warnings: string[] = []
  const suggestions: string[] = []

  // 基本验证
  if (!data || data.length === 0) {
    errors.push('数据不能为空')
    return { isValid: false, errors, warnings, suggestions }
  }

  // 检查必要字段
  data.forEach((item, index) => {
    if (!item.text || item.text.trim() === '') {
      errors.push(`第${index + 1}行：缺少文本内容`)
    }

    // 如果预期有时间戳，检查时间戳格式
    if (expectedType && expectedType !== 'plain_text') {
      if (!item.start && !item.end) {
        warnings.push(`第${index + 1}行：缺少时间戳信息`)
      } else {
        // 验证时间戳格式
        if (item.start && !isValidTimestamp(item.start)) {
          errors.push(`第${index + 1}行：开始时间格式无效`)
        }
        if (item.end && !isValidTimestamp(item.end)) {
          errors.push(`第${index + 1}行：结束时间格式无效`)
        }

        // 验证时间逻辑
        if (item.start && item.end) {
          const startTime = timestampToSeconds(item.start)
          const endTime = timestampToSeconds(item.end)
          if (startTime >= endTime) {
            errors.push(`第${index + 1}行：开始时间不能大于或等于结束时间`)
          }
        }
      }
    }
  })

  // 检查时间戳顺序
  if (expectedType !== 'plain_text') {
    validateTimestampSequence(data, errors, warnings)
  }

  // 生成建议
  if (warnings.length > 0) {
    suggestions.push('建议检查数据完整性')
  }
  if (errors.length === 0 && expectedType === 'word_level' && data.length < 50) {
    suggestions.push('数据量较少，可能更适合句子级对齐')
  }

  return {
    isValid: errors.length === 0,
    errors,
    warnings,
    suggestions,
  }
}

/**
 * 验证翻译数据
 */
export function validateTranslationData(data: TranslationData[]): ValidationResult {
  const errors: string[] = []
  const warnings: string[] = []
  const suggestions: string[] = []

  if (!data || data.length === 0) {
    errors.push('翻译数据不能为空')
    return { isValid: false, errors, warnings, suggestions }
  }

  data.forEach((item, index) => {
    if (!item.source || item.source.trim() === '') {
      errors.push(`第${index + 1}行：缺少源文本`)
    }
    if (!item.translation || item.translation.trim() === '') {
      warnings.push(`第${index + 1}行：缺少翻译文本`)
    }
  })

  if (warnings.length > 0) {
    suggestions.push('建议补充缺少的翻译文本')
  }

  return {
    isValid: errors.length === 0,
    errors,
    warnings,
    suggestions,
  }
}

/**
 * 验证时间戳顺序
 */
export function validateTimestampSequence(data: TimestampData[], errors: string[], warnings: string[]): void {
  for (let i = 1; i < data.length; i++) {
    const prevItem = data[i - 1]
    const currentItem = data[i]

    if (prevItem.end && currentItem.start) {
      const prevEnd = timestampToSeconds(prevItem.end)
      const currentStart = timestampToSeconds(currentItem.start)

      if (prevEnd > currentStart) {
        warnings.push(`第${i}行与第${i + 1}行：时间戳重叠`)
      }
    }
  }
}

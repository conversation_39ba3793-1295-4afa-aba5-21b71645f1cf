import { describe, it, expect, vi, beforeEach } from 'vitest'
import { validateTimestampData, validateTranslationData, validateTimestampSequence } from '../timestampValidator'
import type { TimestampData, TranslationData } from '~/utils/processing/time/timestampTypes'

import { isValidTimestamp } from '~/utils/processing/time/timeValidator'
import { timestampToSeconds } from '~/utils/processing/time/timeParser'

// Mock 依赖
vi.mock('~/utils/processing/time/timeValidator', () => ({
  isValidTimestamp: vi.fn(),
}))

vi.mock('~/utils/processing/time/timeParser', () => ({
  timestampToSeconds: vi.fn(),
}))

describe('Timestamp Validator', () => {
  beforeEach(() => {
    vi.clearAllMocks()

    // 默认mock实现
    vi.mocked(isValidTimestamp).mockImplementation((timestamp: string | number) => {
      if (typeof timestamp === 'number') return true
      // 简单的时间戳格式验证逻辑
      return /^\d{2}:\d{2}:\d{2}[,.]\d{3}$/.test(timestamp)
    })

    vi.mocked(timestampToSeconds).mockImplementation((timestamp: string | number) => {
      if (typeof timestamp === 'number') return timestamp
      // 简单的时间戳转换逻辑
      const match = timestamp.match(/(\d{2}):(\d{2}):(\d{2})[,.](\d{3})/)
      if (!match) return 0
      const [, hours, minutes, seconds, milliseconds] = match
      return parseInt(hours) * 3600 + parseInt(minutes) * 60 + parseInt(seconds) + parseInt(milliseconds) / 1000
    })
  })

  describe('validateTimestampData', () => {
    describe('基本验证', () => {
      it('应该在数据为null时返回错误', () => {
        const result = validateTimestampData(null as never)

        expect(result.isValid).toBe(false)
        expect(result.errors).toContain('数据不能为空')
        expect(result.warnings).toEqual([])
        expect(result.suggestions).toEqual([])
      })

      it('应该在数据为undefined时返回错误', () => {
        const result = validateTimestampData(undefined as never)

        expect(result.isValid).toBe(false)
        expect(result.errors).toContain('数据不能为空')
        expect(result.warnings).toEqual([])
        expect(result.suggestions).toEqual([])
      })

      it('应该在数据为空数组时返回错误', () => {
        const result = validateTimestampData([])

        expect(result.isValid).toBe(false)
        expect(result.errors).toContain('数据不能为空')
        expect(result.warnings).toEqual([])
        expect(result.suggestions).toEqual([])
      })
    })

    describe('文本内容验证', () => {
      it('应该在缺少文本内容时返回错误', () => {
        const data: TimestampData[] = [
          { text: '' },
          { text: '   ' }, // 只有空格
          { text: 'valid text' },
        ]

        const result = validateTimestampData(data)

        expect(result.isValid).toBe(false)
        expect(result.errors).toContain('第1行：缺少文本内容')
        expect(result.errors).toContain('第2行：缺少文本内容')
        expect(result.errors).not.toContain('第3行：缺少文本内容')
      })

      it('应该在文本为null或undefined时返回错误', () => {
        const data: TimestampData[] = [{ text: null as never }, { text: undefined as never }, { text: 'valid text' }]

        const result = validateTimestampData(data)

        expect(result.isValid).toBe(false)
        expect(result.errors).toContain('第1行：缺少文本内容')
        expect(result.errors).toContain('第2行：缺少文本内容')
        expect(result.errors).not.toContain('第3行：缺少文本内容')
      })

      it('应该在只有有效文本时通过验证', () => {
        const data: TimestampData[] = [{ text: 'Hello world' }, { text: '你好世界' }, { text: '123456' }]

        const result = validateTimestampData(data)

        expect(result.isValid).toBe(true)
        expect(result.errors).toEqual([])
      })
    })

    describe('plain_text类型验证', () => {
      it('应该在plain_text类型时跳过时间戳验证', () => {
        const data: TimestampData[] = [{ text: 'Hello world' }, { text: 'Second line' }]

        const result = validateTimestampData(data, 'plain_text')

        expect(result.isValid).toBe(true)
        expect(result.errors).toEqual([])
        expect(isValidTimestamp).not.toHaveBeenCalled()
      })

      it('应该在plain_text类型时不检查时间戳顺序', () => {
        const data: TimestampData[] = [
          { text: 'Hello', start: '00:00:05.000', end: '00:00:10.000' },
          { text: 'World', start: '00:00:02.000', end: '00:00:07.000' }, // 重叠时间
        ]

        const result = validateTimestampData(data, 'plain_text')

        expect(result.isValid).toBe(true)
        expect(result.warnings).toEqual([])
      })
    })

    describe('时间戳类型验证', () => {
      it('应该在缺少时间戳时发出警告', () => {
        const data: TimestampData[] = [
          { text: 'Hello world' }, // 没有时间戳
          { text: 'Second line', start: '00:00:01.000' }, // 只有开始时间
        ]

        const result = validateTimestampData(data, 'sentence_level')

        expect(result.isValid).toBe(true)
        expect(result.warnings).toContain('第1行：缺少时间戳信息')
        expect(result.warnings).not.toContain('第2行：缺少时间戳信息')
      })

      it('应该验证时间戳格式', () => {
        vi.mocked(isValidTimestamp).mockImplementation((timestamp: string | number) => {
          if (typeof timestamp === 'number') return true
          return timestamp === '00:00:01.000' || timestamp === '00:00:02.000'
        })

        const data: TimestampData[] = [
          { text: 'Valid', start: '00:00:01.000', end: '00:00:02.000' },
          { text: 'Invalid start', start: 'invalid', end: '00:00:02.000' },
          { text: 'Invalid end', start: '00:00:01.000', end: 'invalid' },
        ]

        const result = validateTimestampData(data, 'sentence_level')

        expect(result.isValid).toBe(false)
        expect(result.errors).toContain('第2行：开始时间格式无效')
        expect(result.errors).toContain('第3行：结束时间格式无效')
        expect(result.errors).not.toContain('第1行：开始时间格式无效')
        expect(result.errors).not.toContain('第1行：结束时间格式无效')
      })

      it('应该验证时间逻辑（开始时间不能大于结束时间）', () => {
        vi.mocked(timestampToSeconds).mockImplementation((timestamp: string | number) => {
          if (typeof timestamp === 'number') return timestamp
          if (timestamp === '00:00:01.000') return 1
          if (timestamp === '00:00:02.000') return 2
          if (timestamp === '00:00:05.000') return 5
          if (timestamp === '00:00:03.000') return 3
          return 0
        })

        const data: TimestampData[] = [
          { text: 'Valid order', start: '00:00:01.000', end: '00:00:02.000' },
          { text: 'Invalid order', start: '00:00:05.000', end: '00:00:03.000' },
          { text: 'Equal times', start: '00:00:02.000', end: '00:00:02.000' },
        ]

        const result = validateTimestampData(data, 'sentence_level')

        expect(result.isValid).toBe(false)
        expect(result.errors).toContain('第2行：开始时间不能大于或等于结束时间')
        expect(result.errors).toContain('第3行：开始时间不能大于或等于结束时间')
        expect(result.errors).not.toContain('第1行：开始时间不能大于或等于结束时间')
      })

      it('应该处理部分时间戳（只有开始或结束时间）', () => {
        const data: TimestampData[] = [
          { text: 'Only start', start: '00:00:01.000' },
          { text: 'Only end', end: '00:00:02.000' },
          { text: 'Both times', start: '00:00:03.000', end: '00:00:04.000' },
        ]

        const result = validateTimestampData(data, 'sentence_level')

        expect(result.isValid).toBe(true)
        expect(result.errors).toEqual([])
        // 部分时间戳不会触发时间逻辑验证
      })
    })

    describe('时间戳顺序验证', () => {
      it('应该检测时间戳重叠', () => {
        vi.mocked(timestampToSeconds).mockImplementation((timestamp: string | number) => {
          if (typeof timestamp === 'number') return timestamp
          const timeMap: Record<string, number> = {
            '00:00:01.000': 1,
            '00:00:03.000': 3,
            '00:00:02.000': 2,
            '00:00:04.000': 4,
          }
          return timeMap[timestamp] || 0
        })

        const data: TimestampData[] = [
          { text: 'First', start: '00:00:01.000', end: '00:00:03.000' },
          { text: 'Second', start: '00:00:02.000', end: '00:00:04.000' }, // 重叠
        ]

        const result = validateTimestampData(data, 'sentence_level')

        expect(result.isValid).toBe(true) // 重叠只是警告，不是错误
        expect(result.warnings).toContain('第1行与第2行：时间戳重叠')
      })

      it('应该在时间戳顺序正确时通过验证', () => {
        vi.mocked(timestampToSeconds).mockImplementation((timestamp: string | number) => {
          if (typeof timestamp === 'number') return timestamp
          const timeMap: Record<string, number> = {
            '00:00:01.000': 1,
            '00:00:02.000': 2,
            '00:00:03.000': 3,
            '00:00:04.000': 4,
          }
          return timeMap[timestamp] || 0
        })

        const data: TimestampData[] = [
          { text: 'First', start: '00:00:01.000', end: '00:00:02.000' },
          { text: 'Second', start: '00:00:03.000', end: '00:00:04.000' },
        ]

        const result = validateTimestampData(data, 'sentence_level')

        expect(result.isValid).toBe(true)
        expect(result.warnings).toEqual([])
      })
    })

    describe('建议生成', () => {
      it('应该在有警告时生成数据完整性建议', () => {
        const data: TimestampData[] = [{ text: 'Missing timestamp' }, { text: 'Has timestamp', start: '00:00:01.000', end: '00:00:02.000' }]

        const result = validateTimestampData(data, 'sentence_level')

        expect(result.suggestions).toContain('建议检查数据完整性')
      })

      it('应该在word_level数据量少时建议使用句子级对齐', () => {
        const data: TimestampData[] = Array.from({ length: 30 }, (_, i) => ({
          text: `Word ${i + 1}`,
          start: `00:00:${String(i).padStart(2, '0')}.000`,
          end: `00:00:${String(i + 1).padStart(2, '0')}.000`,
        }))

        const result = validateTimestampData(data, 'word_level')

        expect(result.isValid).toBe(true)
        expect(result.suggestions).toContain('数据量较少，可能更适合句子级对齐')
      })

      it('应该在word_level数据量足够时不生成建议', () => {
        // 恢复默认mock实现，让验证逻辑正常工作

        const data: TimestampData[] = Array.from({ length: 100 }, (_, i) => {
          const minutes = Math.floor(i / 60)
          const seconds = i % 60
          const nextMinutes = Math.floor((i + 1) / 60)
          const nextSeconds = (i + 1) % 60
          return {
            text: `Word ${i + 1}`,
            start: `00:${String(minutes).padStart(2, '0')}:${String(seconds).padStart(2, '0')}.000`,
            end: `00:${String(nextMinutes).padStart(2, '0')}:${String(nextSeconds).padStart(2, '0')}.000`,
          }
        })

        const result = validateTimestampData(data, 'word_level')

        expect(result.isValid).toBe(true)
        expect(result.suggestions).not.toContain('数据量较少，可能更适合句子级对齐')
      })

      it('应该在sentence_level时不生成数据量建议', () => {
        const data: TimestampData[] = Array.from({ length: 30 }, (_, i) => ({
          text: `Sentence ${i + 1}`,
          start: `00:00:${String(i).padStart(2, '0')}.000`,
          end: `00:00:${String(i + 1).padStart(2, '0')}.000`,
        }))

        const result = validateTimestampData(data, 'sentence_level')

        expect(result.isValid).toBe(true)
        expect(result.suggestions).not.toContain('数据量较少，可能更适合句子级对齐')
      })
    })

    describe('复杂场景', () => {
      it('应该处理包含所有问题的复杂数据', () => {
        vi.mocked(isValidTimestamp).mockImplementation((timestamp: string | number) => {
          if (typeof timestamp === 'number') return true
          return timestamp !== 'invalid_format'
        })

        vi.mocked(timestampToSeconds).mockImplementation((timestamp: string | number) => {
          if (typeof timestamp === 'number') return timestamp
          const timeMap: Record<string, number> = {
            '00:00:01.000': 1,
            '00:00:02.000': 2,
            '00:00:05.000': 5,
            '00:00:03.000': 3,
            '00:00:04.000': 4,
            '00:00:06.000': 6,
          }
          return timeMap[timestamp] || 0
        })

        const data: TimestampData[] = [
          { text: '' }, // 错误：空文本
          { text: 'No timestamp' }, // 警告：缺少时间戳
          { text: 'Valid item', start: '00:00:01.000', end: '00:00:04.000' }, // 正常项，结束时间4秒
          { text: 'Overlap item', start: '00:00:03.000', end: '00:00:05.000' }, // 与前一个重叠（开始3秒 < 结束4秒）
          { text: 'Wrong order', start: '00:00:06.000', end: '00:00:05.000' }, // 错误：时间顺序错误
        ]

        const result = validateTimestampData(data, 'sentence_level')

        expect(result.isValid).toBe(false)
        expect(result.errors).toContain('第1行：缺少文本内容')
        expect(result.errors).toContain('第5行：开始时间不能大于或等于结束时间')
        expect(result.warnings).toContain('第2行：缺少时间戳信息')
        expect(result.warnings).toContain('第3行与第4行：时间戳重叠')
        expect(result.suggestions).toContain('建议检查数据完整性')
      })

      it('应该处理包含可选字段的数据', () => {
        const data: TimestampData[] = [
          {
            id: 1,
            text: 'Hello world',
            start: '00:00:01.000',
            end: '00:00:02.000',
            speaker: 'Speaker 1',
            confidence: 0.95,
          },
          {
            id: 'str-id',
            text: 'Second line',
            start: '00:00:03.000',
            end: '00:00:04.000',
            speaker: 'Speaker 2',
            confidence: 0.88,
          },
        ]

        const result = validateTimestampData(data, 'sentence_level')

        expect(result.isValid).toBe(true)
        expect(result.errors).toEqual([])
        expect(result.warnings).toEqual([])
      })
    })
  })

  describe('validateTranslationData', () => {
    describe('基本验证', () => {
      it('应该在数据为null时返回错误', () => {
        const result = validateTranslationData(null as never)

        expect(result.isValid).toBe(false)
        expect(result.errors).toContain('翻译数据不能为空')
        expect(result.warnings).toEqual([])
        expect(result.suggestions).toEqual([])
      })

      it('应该在数据为undefined时返回错误', () => {
        const result = validateTranslationData(undefined as never)

        expect(result.isValid).toBe(false)
        expect(result.errors).toContain('翻译数据不能为空')
        expect(result.warnings).toEqual([])
        expect(result.suggestions).toEqual([])
      })

      it('应该在数据为空数组时返回错误', () => {
        const result = validateTranslationData([])

        expect(result.isValid).toBe(false)
        expect(result.errors).toContain('翻译数据不能为空')
        expect(result.warnings).toEqual([])
        expect(result.suggestions).toEqual([])
      })
    })

    describe('源文本验证', () => {
      it('应该在缺少源文本时返回错误', () => {
        const data: TranslationData[] = [
          { source: '', translation: 'Translation 1' },
          { source: '   ', translation: 'Translation 2' }, // 只有空格
          { source: 'Valid source', translation: 'Translation 3' },
        ]

        const result = validateTranslationData(data)

        expect(result.isValid).toBe(false)
        expect(result.errors).toContain('第1行：缺少源文本')
        expect(result.errors).toContain('第2行：缺少源文本')
        expect(result.errors).not.toContain('第3行：缺少源文本')
      })

      it('应该在源文本为null或undefined时返回错误', () => {
        const data: TranslationData[] = [
          { source: null as never, translation: 'Translation 1' },
          { source: undefined as never, translation: 'Translation 2' },
          { source: 'Valid source', translation: 'Translation 3' },
        ]

        const result = validateTranslationData(data)

        expect(result.isValid).toBe(false)
        expect(result.errors).toContain('第1行：缺少源文本')
        expect(result.errors).toContain('第2行：缺少源文本')
        expect(result.errors).not.toContain('第3行：缺少源文本')
      })
    })

    describe('翻译文本验证', () => {
      it('应该在缺少翻译文本时发出警告', () => {
        const data: TranslationData[] = [
          { source: 'Source 1', translation: '' },
          { source: 'Source 2', translation: '   ' }, // 只有空格
          { source: 'Source 3', translation: 'Valid translation' },
        ]

        const result = validateTranslationData(data)

        expect(result.isValid).toBe(true) // 翻译缺失只是警告，不是错误
        expect(result.warnings).toContain('第1行：缺少翻译文本')
        expect(result.warnings).toContain('第2行：缺少翻译文本')
        expect(result.warnings).not.toContain('第3行：缺少翻译文本')
      })

      it('应该在翻译文本为null或undefined时发出警告', () => {
        const data: TranslationData[] = [
          { source: 'Source 1', translation: null as never },
          { source: 'Source 2', translation: undefined as never },
          { source: 'Source 3', translation: 'Valid translation' },
        ]

        const result = validateTranslationData(data)

        expect(result.isValid).toBe(true)
        expect(result.warnings).toContain('第1行：缺少翻译文本')
        expect(result.warnings).toContain('第2行：缺少翻译文本')
        expect(result.warnings).not.toContain('第3行：缺少翻译文本')
      })
    })

    describe('建议生成', () => {
      it('应该在有翻译警告时生成补充建议', () => {
        const data: TranslationData[] = [
          { source: 'Source 1', translation: '' },
          { source: 'Source 2', translation: 'Valid translation' },
        ]

        const result = validateTranslationData(data)

        expect(result.suggestions).toContain('建议补充缺少的翻译文本')
      })

      it('应该在没有警告时不生成建议', () => {
        const data: TranslationData[] = [
          { source: 'Source 1', translation: 'Translation 1' },
          { source: 'Source 2', translation: 'Translation 2' },
        ]

        const result = validateTranslationData(data)

        expect(result.isValid).toBe(true)
        expect(result.warnings).toEqual([])
        expect(result.suggestions).toEqual([])
      })
    })

    describe('复杂场景', () => {
      it('应该处理包含所有问题的复杂数据', () => {
        const data: TranslationData[] = [
          { source: '', translation: '' }, // 错误：缺少源文本，警告：缺少翻译
          { source: 'Valid source 1', translation: '' }, // 警告：缺少翻译
          { source: '', translation: 'Valid translation' }, // 错误：缺少源文本
          { source: 'Valid source 2', translation: 'Valid translation' }, // 正常
        ]

        const result = validateTranslationData(data)

        expect(result.isValid).toBe(false)
        expect(result.errors).toContain('第1行：缺少源文本')
        expect(result.errors).toContain('第3行：缺少源文本')
        expect(result.warnings).toContain('第1行：缺少翻译文本')
        expect(result.warnings).toContain('第2行：缺少翻译文本')
        expect(result.suggestions).toContain('建议补充缺少的翻译文本')
      })

      it('应该处理包含可选字段的数据', () => {
        const data: TranslationData[] = [
          {
            id: 1,
            source: 'Hello world',
            translation: '你好世界',
            index: 0,
          },
          {
            id: 'str-id',
            source: 'Goodbye',
            translation: '再见',
            index: 1,
          },
        ]

        const result = validateTranslationData(data)

        expect(result.isValid).toBe(true)
        expect(result.errors).toEqual([])
        expect(result.warnings).toEqual([])
        expect(result.suggestions).toEqual([])
      })
    })
  })

  describe('validateTimestampSequence', () => {
    beforeEach(() => {
      vi.mocked(timestampToSeconds).mockImplementation((timestamp: string | number) => {
        if (typeof timestamp === 'number') return timestamp
        const timeMap: Record<string, number> = {
          '00:00:01.000': 1,
          '00:00:02.000': 2,
          '00:00:03.000': 3,
          '00:00:04.000': 4,
          '00:00:05.000': 5,
          '00:00:06.000': 6,
        }
        return timeMap[timestamp] || 0
      })
    })

    it('应该检测时间戳重叠', () => {
      const data: TimestampData[] = [
        { text: 'First', start: '00:00:01.000', end: '00:00:04.000' },
        { text: 'Second', start: '00:00:03.000', end: '00:00:05.000' }, // 重叠
        { text: 'Third', start: '00:00:05.000', end: '00:00:06.000' }, // 不重叠
      ]

      const errors: string[] = []
      const warnings: string[] = []

      validateTimestampSequence(data, errors, warnings)

      expect(errors).toEqual([])
      expect(warnings).toContain('第1行与第2行：时间戳重叠')
      expect(warnings).not.toContain('第2行与第3行：时间戳重叠')
    })

    it('应该在时间戳不重叠时不生成警告', () => {
      const data: TimestampData[] = [
        { text: 'First', start: '00:00:01.000', end: '00:00:02.000' },
        { text: 'Second', start: '00:00:03.000', end: '00:00:04.000' },
        { text: 'Third', start: '00:00:05.000', end: '00:00:06.000' },
      ]

      const errors: string[] = []
      const warnings: string[] = []

      validateTimestampSequence(data, errors, warnings)

      expect(errors).toEqual([])
      expect(warnings).toEqual([])
    })

    it('应该在边界时间相等时不生成警告', () => {
      const data: TimestampData[] = [
        { text: 'First', start: '00:00:01.000', end: '00:00:03.000' },
        { text: 'Second', start: '00:00:03.000', end: '00:00:05.000' }, // 边界相等
      ]

      const errors: string[] = []
      const warnings: string[] = []

      validateTimestampSequence(data, errors, warnings)

      expect(errors).toEqual([])
      expect(warnings).toEqual([])
    })

    it('应该处理缺少时间戳的情况', () => {
      const data: TimestampData[] = [
        { text: 'First', start: '00:00:01.000', end: '00:00:02.000' },
        { text: 'Second' }, // 没有时间戳
        { text: 'Third', start: '00:00:03.000', end: '00:00:04.000' },
      ]

      const errors: string[] = []
      const warnings: string[] = []

      validateTimestampSequence(data, errors, warnings)

      expect(errors).toEqual([])
      expect(warnings).toEqual([])
      // 缺少时间戳的项不参与顺序检查
    })

    it('应该处理部分时间戳的情况', () => {
      const data: TimestampData[] = [
        { text: 'First', start: '00:00:01.000' }, // 没有end
        { text: 'Second', start: '00:00:02.000', end: '00:00:03.000' },
        { text: 'Third', end: '00:00:04.000' }, // 没有start
      ]

      const errors: string[] = []
      const warnings: string[] = []

      validateTimestampSequence(data, errors, warnings)

      expect(errors).toEqual([])
      expect(warnings).toEqual([])
      // 部分时间戳不参与顺序检查
    })

    it('应该处理单个元素的数组', () => {
      const data: TimestampData[] = [{ text: 'Only one', start: '00:00:01.000', end: '00:00:02.000' }]

      const errors: string[] = []
      const warnings: string[] = []

      validateTimestampSequence(data, errors, warnings)

      expect(errors).toEqual([])
      expect(warnings).toEqual([])
    })

    it('应该处理空数组', () => {
      const data: TimestampData[] = []

      const errors: string[] = []
      const warnings: string[] = []

      validateTimestampSequence(data, errors, warnings)

      expect(errors).toEqual([])
      expect(warnings).toEqual([])
    })

    it('应该检测多个重叠', () => {
      const data: TimestampData[] = [
        { text: 'First', start: '00:00:01.000', end: '00:00:04.000' },
        { text: 'Second', start: '00:00:03.000', end: '00:00:06.000' }, // 与第一个重叠
        { text: 'Third', start: '00:00:05.000', end: '00:00:06.000' }, // 与第二个重叠
      ]

      const errors: string[] = []
      const warnings: string[] = []

      validateTimestampSequence(data, errors, warnings)

      expect(errors).toEqual([])
      expect(warnings).toContain('第1行与第2行：时间戳重叠')
      expect(warnings).toContain('第2行与第3行：时间戳重叠')
    })
  })
})

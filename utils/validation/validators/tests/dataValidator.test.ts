import { describe, it, expect } from 'vitest'
import {
  isValidSubtitle,
  isValidSubtitleArray,
  hasRequiredProperties,
  validateArrayElements,
  validateNestedObject,
  isValidConfig,
  isValidTimestampData,
} from '../dataValidator'
import type { Subtitle } from '~/types/subtitle'

// 测试数据常量
const VALID_SUBTITLE: Subtitle = {
  uuid: 'test-uuid-123',
  id: 1,
  startTime: '00:00:01,000',
  endTime: '00:00:05,000',
  text: '测试文本',
  translationText: '翻译文本',
}

const INVALID_SUBTITLES = {
  missing_uuid: {
    id: 1,
    startTime: '00:00:01,000',
    endTime: '00:00:05,000',
    text: '测试文本',
    translationText: '翻译文本',
  },
  wrong_type_id: {
    uuid: 'test-uuid-123',
    id: '1', // 应该是number
    startTime: '00:00:01,000',
    endTime: '00:00:05,000',
    text: '测试文本',
    translationText: '翻译文本',
  },
  null_values: {
    uuid: null,
    id: 1,
    startTime: '00:00:01,000',
    endTime: '00:00:05,000',
    text: '测试文本',
    translationText: '翻译文本',
  },
}

describe('Data Validator', () => {
  describe('isValidSubtitle', () => {
    it('应该验证有效的字幕对象', () => {
      expect(isValidSubtitle(VALID_SUBTITLE)).toBe(true)
    })

    it('应该拒绝空值', () => {
      expect(isValidSubtitle(null)).toBe(false)
      expect(isValidSubtitle(undefined)).toBe(false)
    })

    it('应该拒绝非对象类型', () => {
      expect(isValidSubtitle('string')).toBe(false)
      expect(isValidSubtitle(123)).toBe(false)
      expect(isValidSubtitle([])).toBe(false)
      expect(isValidSubtitle(true)).toBe(false)
    })

    it('应该拒绝缺少必需属性的对象', () => {
      expect(isValidSubtitle(INVALID_SUBTITLES.missing_uuid)).toBe(false)
    })

    it('应该拒绝属性类型错误的对象', () => {
      expect(isValidSubtitle(INVALID_SUBTITLES.wrong_type_id)).toBe(false)
      expect(isValidSubtitle(INVALID_SUBTITLES.null_values)).toBe(false)
    })

    it('应该拒绝包含额外属性但缺少必需属性的对象', () => {
      const incompleteSubtitle = {
        uuid: 'test-uuid',
        id: 1,
        extraProperty: 'extra',
        // 缺少其他必需属性
      }
      expect(isValidSubtitle(incompleteSubtitle)).toBe(false)
    })
  })

  describe('isValidSubtitleArray', () => {
    it('应该验证有效的字幕数组', () => {
      const validArray = [VALID_SUBTITLE, { ...VALID_SUBTITLE, id: 2 }]
      expect(isValidSubtitleArray(validArray)).toBe(true)
    })

    it('应该验证空数组', () => {
      expect(isValidSubtitleArray([])).toBe(true)
    })

    it('应该拒绝非数组类型', () => {
      expect(isValidSubtitleArray(VALID_SUBTITLE)).toBe(false)
      expect(isValidSubtitleArray('string')).toBe(false)
      expect(isValidSubtitleArray(null)).toBe(false)
    })

    it('应该拒绝包含无效字幕的数组', () => {
      const invalidArray = [VALID_SUBTITLE, INVALID_SUBTITLES.missing_uuid]
      expect(isValidSubtitleArray(invalidArray)).toBe(false)
    })

    it('应该拒绝混合类型的数组', () => {
      const mixedArray = [VALID_SUBTITLE, 'string', 123]
      expect(isValidSubtitleArray(mixedArray)).toBe(false)
    })
  })

  describe('hasRequiredProperties', () => {
    it('应该验证包含所有必需属性的对象', () => {
      const obj = { name: 'test', age: 25, active: true }
      const required = ['name', 'age']
      expect(hasRequiredProperties(obj, required)).toBe(true)
    })

    it('应该拒绝缺少必需属性的对象', () => {
      const obj = { name: 'test' }
      const required = ['name', 'age']
      expect(hasRequiredProperties(obj, required)).toBe(false)
    })

    it('应该拒绝空对象', () => {
      const obj = {}
      const required = ['name']
      expect(hasRequiredProperties(obj, required)).toBe(false)
    })

    it('应该拒绝非对象类型', () => {
      const required = ['name']
      expect(hasRequiredProperties(null, required)).toBe(false)
      expect(hasRequiredProperties('string', required)).toBe(false)
      expect(hasRequiredProperties(123, required)).toBe(false)
    })

    it('应该处理空的必需属性数组', () => {
      const obj = { name: 'test' }
      expect(hasRequiredProperties(obj, [])).toBe(true)
    })

    it('应该验证属性值为falsy的情况', () => {
      const obj = { name: '', age: 0, active: false }
      const required = ['name', 'age', 'active']
      expect(hasRequiredProperties(obj, required)).toBe(true)
    })
  })

  describe('validateArrayElements', () => {
    const isString = (item: unknown): item is string => typeof item === 'string'
    const isNumber = (item: unknown): item is number => typeof item === 'number'

    it('应该验证所有元素都符合条件的数组', () => {
      const stringArray = ['a', 'b', 'c']
      expect(validateArrayElements(stringArray, isString)).toBe(true)
    })

    it('应该验证空数组', () => {
      expect(validateArrayElements([], isString)).toBe(true)
    })

    it('应该拒绝包含不符合条件元素的数组', () => {
      const mixedArray = ['a', 1, 'c']
      expect(validateArrayElements(mixedArray, isString)).toBe(false)
    })

    it('应该拒绝非数组类型', () => {
      expect(validateArrayElements('not array' as unknown as unknown[], isString)).toBe(false)
      expect(validateArrayElements(null as unknown as unknown[], isString)).toBe(false)
      expect(validateArrayElements(undefined as unknown as unknown[], isString)).toBe(false)
    })

    it('应该正确处理复杂验证器', () => {
      const numberArray = [1, 2, 3]
      const stringArray = ['1', '2', '3']

      expect(validateArrayElements(numberArray, isNumber)).toBe(true)
      expect(validateArrayElements(stringArray, isNumber)).toBe(false)
    })
  })

  describe('validateNestedObject', () => {
    it('应该验证符合schema的嵌套对象', () => {
      const obj = {
        user: { name: 'test', age: 25 },
        settings: { theme: 'dark' },
      }
      const schema = {
        user: (value: unknown) => typeof value === 'object' && value !== null,
        settings: (value: unknown) => typeof value === 'object' && value !== null,
      }
      expect(validateNestedObject(obj, schema)).toBe(true)
    })

    it('应该拒绝不符合schema的对象', () => {
      const obj = {
        user: 'not an object',
        settings: { theme: 'dark' },
      }
      const schema = {
        user: (value: unknown) => typeof value === 'object' && value !== null,
        settings: (value: unknown) => typeof value === 'object' && value !== null,
      }
      expect(validateNestedObject(obj, schema)).toBe(false)
    })

    it('应该拒绝缺少schema属性的对象', () => {
      const obj = {
        user: { name: 'test' },
        // 缺少 settings
      }
      const schema = {
        user: (value: unknown) => typeof value === 'object',
        settings: (value: unknown) => typeof value === 'object',
      }
      expect(validateNestedObject(obj, schema)).toBe(false)
    })

    it('应该拒绝非对象类型', () => {
      const schema = { name: (value: unknown) => typeof value === 'string' }
      expect(validateNestedObject(null, schema)).toBe(false)
      expect(validateNestedObject('string', schema)).toBe(false)
      expect(validateNestedObject(123, schema)).toBe(false)
    })

    it('应该处理空schema', () => {
      const obj = { name: 'test' }
      expect(validateNestedObject(obj, {})).toBe(true)
    })

    it('应该处理复杂的验证器', () => {
      const obj = {
        numbers: [1, 2, 3],
        strings: ['a', 'b'],
        config: { enabled: true },
      }
      const schema = {
        numbers: (value: unknown) => Array.isArray(value) && value.every((item) => typeof item === 'number'),
        strings: (value: unknown) => Array.isArray(value) && value.every((item) => typeof item === 'string'),
        config: (value: unknown) => typeof value === 'object' && value !== null,
      }
      expect(validateNestedObject(obj, schema)).toBe(true)
    })
  })

  describe('isValidConfig', () => {
    it('应该验证有效的配置对象', () => {
      const validConfigs = [{ setting1: 'value1', setting2: true }, { timeout: 5000, retries: 3 }, {}]

      validConfigs.forEach((config) => {
        expect(isValidConfig(config)).toBe(true)
      })
    })

    it('应该拒绝非对象类型', () => {
      const invalidConfigs = [null, undefined, 'string', 123, true, Symbol('test')]

      invalidConfigs.forEach((config) => {
        expect(isValidConfig(config)).toBe(false)
      })
    })

    it('应该拒绝数组类型', () => {
      expect(isValidConfig([])).toBe(false)
      expect(isValidConfig([1, 2, 3])).toBe(false)
      expect(isValidConfig(['a', 'b'])).toBe(false)
    })

    it('应该接受复杂的嵌套对象', () => {
      const complexConfig = {
        database: {
          host: 'localhost',
          port: 5432,
          ssl: true,
        },
        cache: {
          ttl: 3600,
          maxSize: 1000,
        },
      }
      expect(isValidConfig(complexConfig)).toBe(true)
    })
  })

  describe('isValidTimestampData', () => {
    it('应该验证有效的时间戳数据', () => {
      const validData = [
        { text: 'Hello', start: '00:01:00', end: '00:01:05' },
        { text: 'World', start: 60, end: 65 },
        { text: 'Test' }, // start和end是可选的
        { text: 'Another', start: '00:02:00' }, // 只有start
        { text: 'Final', end: '00:02:10' }, // 只有end
      ]

      validData.forEach((data) => {
        expect(isValidTimestampData(data)).toBe(true)
      })
    })

    it('应该拒绝缺少text属性的对象', () => {
      const invalidData = [{ start: '00:01:00', end: '00:01:05' }, { start: 60, end: 65 }, {}]

      invalidData.forEach((data) => {
        expect(isValidTimestampData(data)).toBe(false)
      })
    })

    it('应该拒绝text类型错误的对象', () => {
      const invalidData = [
        { text: 123, start: '00:01:00' },
        { text: null, start: 60 },
        { text: [], end: '00:01:05' },
        { text: {}, start: 60, end: 65 },
      ]

      invalidData.forEach((data) => {
        expect(isValidTimestampData(data)).toBe(false)
      })
    })

    it('应该拒绝时间戳类型错误的对象', () => {
      const invalidData = [
        { text: 'Hello', start: [], end: '00:01:05' },
        { text: 'World', start: '00:01:00', end: {} },
        { text: 'Test', start: true, end: false },
        { text: 'Another', start: null, end: 'invalid' },
      ]

      invalidData.forEach((data) => {
        expect(isValidTimestampData(data)).toBe(false)
      })
    })

    it('应该拒绝非对象类型', () => {
      const invalidData = [null, undefined, 'string', 123, [], true]

      invalidData.forEach((data) => {
        expect(isValidTimestampData(data)).toBe(false)
      })
    })

    it('应该接受start和end为0的情况', () => {
      const validData = [
        { text: 'Start at zero', start: 0, end: 5 },
        { text: 'End at zero', start: 5, end: 0 },
        { text: 'Both zero', start: 0, end: 0 },
        { text: 'String zero', start: '0', end: '0' },
      ]

      validData.forEach((data) => {
        expect(isValidTimestampData(data)).toBe(true)
      })
    })
  })
})

import { describe, it, expect } from 'vitest'
import { validSummary } from '../summaryValidator'

describe('validSummary', () => {
  describe('基础验证', () => {
    it('应该在传入null时返回错误', () => {
      const result = validSummary(null)

      expect(result.valid).toBe(false)
      expect(result.status).toBe('error')
      expect(result.message).toBe('Invalid response format')
      expect(result.errors).toContain('摘要数据不能为空')
    })

    it('应该在传入undefined时返回错误', () => {
      const result = validSummary(undefined)

      expect(result.valid).toBe(false)
      expect(result.status).toBe('error')
      expect(result.message).toBe('Invalid response format')
      expect(result.errors).toContain('摘要数据不能为空')
    })

    it('应该在传入空字符串时返回错误', () => {
      const result = validSummary('')

      expect(result.valid).toBe(false)
      expect(result.status).toBe('error')
      expect(result.message).toBe('Invalid response format')
      expect(result.errors).toContain('摘要数据不能为空')
    })

    it('应该在传入0时返回错误', () => {
      const result = validSummary(0)

      expect(result.valid).toBe(false)
      expect(result.status).toBe('error')
      expect(result.message).toBe('Invalid response format')
      expect(result.errors).toContain('摘要数据不能为空')
    })

    it('应该在传入false时返回错误', () => {
      const result = validSummary(false)

      expect(result.valid).toBe(false)
      expect(result.status).toBe('error')
      expect(result.message).toBe('Invalid response format')
      expect(result.errors).toContain('摘要数据不能为空')
    })

    it('应该在传入字符串时返回错误', () => {
      const result = validSummary('not an object')

      expect(result.valid).toBe(false)
      expect(result.status).toBe('error')
      expect(result.message).toBe('Invalid response format')
      expect(result.errors).toContain('摘要数据必须是对象类型')
    })

    it('应该在传入数字时返回错误', () => {
      const result = validSummary(123)

      expect(result.valid).toBe(false)
      expect(result.status).toBe('error')
      expect(result.message).toBe('Invalid response format')
      expect(result.errors).toContain('摘要数据必须是对象类型')
    })

    it('应该在传入布尔值时返回错误', () => {
      const result = validSummary(true)

      expect(result.valid).toBe(false)
      expect(result.status).toBe('error')
      expect(result.message).toBe('Invalid response format')
      expect(result.errors).toContain('摘要数据必须是对象类型')
    })

    it('应该在传入数组时返回错误', () => {
      const result = validSummary([1, 2, 3])

      expect(result.valid).toBe(false)
      expect(result.status).toBe('error')
      expect(result.message).toBe('Invalid response format')
      expect(result.errors).toContain('摘要数据必须是对象类型')
    })
  })

  describe('terms字段验证', () => {
    it('应该在缺少terms字段时返回错误', () => {
      const invalidData = { theme: 'test theme' }
      const result = validSummary(invalidData)

      expect(result.valid).toBe(false)
      expect(result.status).toBe('error')
      expect(result.message).toBe('Invalid response format')
      expect(result.errors).toContain('缺少terms字段')
    })

    it('应该在terms为null时返回错误', () => {
      const invalidData = { terms: null }
      const result = validSummary(invalidData)

      expect(result.valid).toBe(false)
      expect(result.status).toBe('error')
      expect(result.message).toBe('Invalid response format')
      expect(result.errors).toContain('缺少terms字段')
    })

    it('应该在terms为undefined时返回错误', () => {
      const invalidData = { terms: undefined }
      const result = validSummary(invalidData)

      expect(result.valid).toBe(false)
      expect(result.status).toBe('error')
      expect(result.message).toBe('Invalid response format')
      expect(result.errors).toContain('缺少terms字段')
    })

    it('应该在terms为字符串时返回错误', () => {
      const invalidData = { terms: 'not an array' }
      const result = validSummary(invalidData)

      expect(result.valid).toBe(false)
      expect(result.status).toBe('error')
      expect(result.message).toBe('Invalid response format')
      expect(result.errors).toContain('terms必须是数组类型')
    })

    it('应该在terms为对象时返回错误', () => {
      const invalidData = { terms: { src: 'test' } }
      const result = validSummary(invalidData)

      expect(result.valid).toBe(false)
      expect(result.status).toBe('error')
      expect(result.message).toBe('Invalid response format')
      expect(result.errors).toContain('terms必须是数组类型')
    })

    it('应该在terms为数字时返回错误', () => {
      const invalidData = { terms: 123 }
      const result = validSummary(invalidData)

      expect(result.valid).toBe(false)
      expect(result.status).toBe('error')
      expect(result.message).toBe('Invalid response format')
      expect(result.errors).toContain('terms必须是数组类型')
    })
  })

  describe('term项验证', () => {
    it('应该在term为null时返回错误', () => {
      const invalidData = {
        terms: [null],
      }
      const result = validSummary(invalidData)

      expect(result.valid).toBe(false)
      expect(result.status).toBe('error')
      expect(result.message).toBe('Invalid response format')
      expect(result.errors).toContain('term项必须是对象类型')
    })

    it('应该在term为字符串时返回错误', () => {
      const invalidData = {
        terms: ['invalid term'],
      }
      const result = validSummary(invalidData)

      expect(result.valid).toBe(false)
      expect(result.status).toBe('error')
      expect(result.message).toBe('Invalid response format')
      expect(result.errors).toContain('term项必须是对象类型')
    })

    it('应该在term为数字时返回错误', () => {
      const invalidData = {
        terms: [123],
      }
      const result = validSummary(invalidData)

      expect(result.valid).toBe(false)
      expect(result.status).toBe('error')
      expect(result.message).toBe('Invalid response format')
      expect(result.errors).toContain('term项必须是对象类型')
    })

    it('应该在term为数组时返回错误', () => {
      const invalidData = {
        terms: [[1, 2, 3]],
      }
      const result = validSummary(invalidData)

      expect(result.valid).toBe(false)
      expect(result.status).toBe('error')
      expect(result.message).toBe('Invalid response format')
      expect(result.errors).toContain('term项必须是对象类型')
    })

    it('应该在term缺少src字段时返回错误', () => {
      const invalidData = {
        terms: [
          { tgt: 'test', note: 'test note' }, // 缺少src字段
        ],
      }
      const result = validSummary(invalidData)

      expect(result.valid).toBe(false)
      expect(result.status).toBe('error')
      expect(result.message).toBe('Invalid response format')
      expect(result.errors).toContain('term项缺少必需字段: src, tgt, note')
    })

    it('应该在term缺少tgt字段时返回错误', () => {
      const invalidData = {
        terms: [
          { src: 'test', note: 'test note' }, // 缺少tgt字段
        ],
      }
      const result = validSummary(invalidData)

      expect(result.valid).toBe(false)
      expect(result.status).toBe('error')
      expect(result.message).toBe('Invalid response format')
      expect(result.errors).toContain('term项缺少必需字段: src, tgt, note')
    })

    it('应该在term缺少note字段时返回错误', () => {
      const invalidData = {
        terms: [
          { src: 'test', tgt: 'test' }, // 缺少note字段
        ],
      }
      const result = validSummary(invalidData)

      expect(result.valid).toBe(false)
      expect(result.status).toBe('error')
      expect(result.message).toBe('Invalid response format')
      expect(result.errors).toContain('term项缺少必需字段: src, tgt, note')
    })

    it('应该在term缺少多个字段时返回错误', () => {
      const invalidData = {
        terms: [
          { src: 'test' }, // 缺少tgt和note字段
        ],
      }
      const result = validSummary(invalidData)

      expect(result.valid).toBe(false)
      expect(result.status).toBe('error')
      expect(result.message).toBe('Invalid response format')
      expect(result.errors).toContain('term项缺少必需字段: src, tgt, note')
    })

    it('应该在term字段为空字符串时返回错误', () => {
      const invalidData = {
        terms: [
          { src: '', tgt: '', note: '' }, // 空字符串字段
        ],
      }
      const result = validSummary(invalidData)

      expect(result.valid).toBe(false)
      expect(result.status).toBe('error')
      expect(result.message).toBe('Invalid response format')
      expect(result.errors).toContain('term项缺少必需字段: src, tgt, note')
    })

    it('应该在term字段为null时返回错误', () => {
      const invalidData = {
        terms: [
          { src: null, tgt: null, note: null }, // null字段
        ],
      }
      const result = validSummary(invalidData)

      expect(result.valid).toBe(false)
      expect(result.status).toBe('error')
      expect(result.message).toBe('Invalid response format')
      expect(result.errors).toContain('term项缺少必需字段: src, tgt, note')
    })

    it('应该在term字段为undefined时返回错误', () => {
      const invalidData = {
        terms: [
          { src: undefined, tgt: undefined, note: undefined }, // undefined字段
        ],
      }
      const result = validSummary(invalidData)

      expect(result.valid).toBe(false)
      expect(result.status).toBe('error')
      expect(result.message).toBe('Invalid response format')
      expect(result.errors).toContain('term项缺少必需字段: src, tgt, note')
    })

    it('应该在混合有效和无效term时返回错误', () => {
      const invalidData = {
        terms: [
          { src: 'valid', tgt: '有效', note: '有效项' }, // 有效项
          { src: 'invalid', tgt: 'invalid' }, // 无效项，缺少note
        ],
      }
      const result = validSummary(invalidData)

      expect(result.valid).toBe(false)
      expect(result.status).toBe('error')
      expect(result.message).toBe('Invalid response format')
      expect(result.errors).toContain('term项缺少必需字段: src, tgt, note')
    })
  })

  describe('content字段验证（向后兼容）', () => {
    it('应该在content字段过短时返回警告但仍然成功', () => {
      const dataWithShortContent = {
        terms: [
          {
            src: 'Machine Learning',
            tgt: '机器学习',
            note: 'AI的核心技术',
          },
        ],
        content: '短内容', // 少于10个字符
      }
      const result = validSummary(dataWithShortContent)

      expect(result.valid).toBe(false) // 因为有错误
      expect(result.status).toBe('success') // 但状态还是success
      expect(result.message).toBe('Summary completed')
      expect(result.errors).toContain('摘要内容过短')
    })

    it('应该在content字段足够长时成功', () => {
      const dataWithLongContent = {
        terms: [
          {
            src: 'Machine Learning',
            tgt: '机器学习',
            note: 'AI的核心技术',
          },
        ],
        content: '这是一个足够长的摘要内容，超过了10个字符的限制', // 超过10个字符
      }
      const result = validSummary(dataWithLongContent)

      expect(result.valid).toBe(true)
      expect(result.status).toBe('success')
      expect(result.message).toBe('Summary completed')
      expect(result.errors).toHaveLength(0)
    })

    it('应该在content字段为非字符串时忽略长度检查', () => {
      const dataWithNonStringContent = {
        terms: [
          {
            src: 'Machine Learning',
            tgt: '机器学习',
            note: 'AI的核心技术',
          },
        ],
        content: 123, // 非字符串
      }
      const result = validSummary(dataWithNonStringContent)

      expect(result.valid).toBe(true)
      expect(result.status).toBe('success')
      expect(result.message).toBe('Summary completed')
      expect(result.errors).toHaveLength(0)
    })

    it('应该在content字段为null时忽略长度检查', () => {
      const dataWithNullContent = {
        terms: [
          {
            src: 'Machine Learning',
            tgt: '机器学习',
            note: 'AI的核心技术',
          },
        ],
        content: null,
      }
      const result = validSummary(dataWithNullContent)

      expect(result.valid).toBe(true)
      expect(result.status).toBe('success')
      expect(result.message).toBe('Summary completed')
      expect(result.errors).toHaveLength(0)
    })

    it('应该在没有content字段时成功', () => {
      const dataWithoutContent = {
        terms: [
          {
            src: 'Machine Learning',
            tgt: '机器学习',
            note: 'AI的核心技术',
          },
        ],
      }
      const result = validSummary(dataWithoutContent)

      expect(result.valid).toBe(true)
      expect(result.status).toBe('success')
      expect(result.message).toBe('Summary completed')
      expect(result.errors).toHaveLength(0)
    })
  })

  describe('成功场景', () => {
    it('应该在空terms数组时返回成功', () => {
      const validData = {
        theme: 'Test theme',
        terms: [],
      }
      const result = validSummary(validData)

      expect(result.valid).toBe(true)
      expect(result.status).toBe('success')
      expect(result.message).toBe('Summary completed')
      expect(result.errors).toHaveLength(0)
    })

    it('应该在单个有效term时返回成功', () => {
      const validData = {
        terms: [
          {
            src: 'Machine Learning',
            tgt: '机器学习',
            note: 'AI的核心技术',
          },
        ],
      }
      const result = validSummary(validData)

      expect(result.valid).toBe(true)
      expect(result.status).toBe('success')
      expect(result.message).toBe('Summary completed')
      expect(result.errors).toHaveLength(0)
    })

    it('应该在多个有效term时返回成功', () => {
      const validData = {
        theme: 'Test theme',
        terms: [
          {
            src: 'Machine Learning',
            tgt: '机器学习',
            note: 'AI的核心技术',
          },
          {
            src: 'Neural Network',
            tgt: '神经网络',
            note: '模拟大脑神经元的网络',
          },
        ],
      }
      const result = validSummary(validData)

      expect(result.valid).toBe(true)
      expect(result.status).toBe('success')
      expect(result.message).toBe('Summary completed')
      expect(result.errors).toHaveLength(0)
    })

    it('应该在包含额外字段时返回成功', () => {
      const validData = {
        theme: 'Test theme',
        extraField: 'extra value',
        anotherField: 123,
        terms: [
          {
            src: 'Machine Learning',
            tgt: '机器学习',
            note: 'AI的核心技术',
            category: 'tech', // term项也可以有额外字段
          },
        ],
      }
      const result = validSummary(validData)

      expect(result.valid).toBe(true)
      expect(result.status).toBe('success')
      expect(result.message).toBe('Summary completed')
      expect(result.errors).toHaveLength(0)
    })

    it('应该在term字段值为各种类型时返回成功', () => {
      const validData = {
        terms: [
          {
            src: 'String value',
            tgt: 123, // 数字值
            note: true, // 布尔值
          },
          {
            src: ['array', 'value'], // 数组值
            tgt: { nested: 'object' }, // 对象值
            note: 'Normal string',
          },
        ],
      }
      const result = validSummary(validData)

      expect(result.valid).toBe(true)
      expect(result.status).toBe('success')
      expect(result.message).toBe('Summary completed')
      expect(result.errors).toHaveLength(0)
    })
  })

  describe('边界情况', () => {
    it('应该处理大量terms项', () => {
      const terms = Array.from({ length: 1000 }, (_, i) => ({
        src: `Source ${i}`,
        tgt: `Target ${i}`,
        note: `Note ${i}`,
      }))

      const validData = { terms }
      const result = validSummary(validData)

      expect(result.valid).toBe(true)
      expect(result.status).toBe('success')
      expect(result.message).toBe('Summary completed')
      expect(result.errors).toHaveLength(0)
    })

    it('应该处理复杂的嵌套对象', () => {
      const validData = {
        terms: [
          {
            src: {
              english: 'Machine Learning',
              synonyms: ['ML', 'Automated Learning'],
              complexity: 'high',
            },
            tgt: {
              chinese: '机器学习',
              pinyin: 'jīqì xuéxí',
              traditional: '機器學習',
            },
            note: {
              definition: 'AI的核心技术',
              examples: ['推荐系统', '图像识别'],
              references: ['https://example.com'],
            },
          },
        ],
      }
      const result = validSummary(validData)

      expect(result.valid).toBe(true)
      expect(result.status).toBe('success')
      expect(result.message).toBe('Summary completed')
      expect(result.errors).toHaveLength(0)
    })

    it('应该处理Unicode字符', () => {
      const validData = {
        terms: [
          {
            src: '机器学习 🤖',
            tgt: 'Machine Learning 🧠',
            note: '人工智能的核心技术 ⚡',
          },
          {
            src: '神经网络 🔗',
            tgt: 'Neural Network 🕸️',
            note: '模拟大脑神经元的网络结构 🧠',
          },
        ],
      }
      const result = validSummary(validData)

      expect(result.valid).toBe(true)
      expect(result.status).toBe('success')
      expect(result.message).toBe('Summary completed')
      expect(result.errors).toHaveLength(0)
    })
  })
})

import { describe, it, expect } from 'vitest'
import {
  isSrtTimeFormat,
  isVttTimeFormat,
  isLrcTimeFormat,
  isValidMimeType,
  isAudioFile,
  isVideoFile,
  isSubtitleFile,
  isValidHexColor,
  isNumericString,
  isPositiveIntegerString,
} from '../formatValidator'

describe('Format Validator', () => {
  describe('isSrtTimeFormat', () => {
    it('应该验证有效的SRT时间格式', () => {
      const validFormats = ['00:00:00,000', '01:23:45,678', '23:59:59,999', '00:00:01,001', '12:34:56,789']

      validFormats.forEach((format) => {
        expect(isSrtTimeFormat(format)).toBe(true)
      })
    })

    it('应该拒绝无效的SRT时间格式', () => {
      const invalidFormats = [
        '00:00:00.000', // 使用点而非逗号
        '0:00:00,000', // 小时不足两位
        '00:0:00,000', // 分钟不足两位
        '00:00:0,000', // 秒不足两位
        '00:00:00,00', // 毫秒不足三位
        '00:00:00,0000', // 毫秒超过三位
        '24:00:00,000', // 小时超出范围
        '00:60:00,000', // 分钟超出范围
        '00:00:60,000', // 秒超出范围
        '00:00:00', // 缺少毫秒
        '00:00:00,', // 毫秒为空
        'abc:def:ghi,jkl', // 非数字字符
        '', // 空字符串
        '00:00:00,abc', // 毫秒部分非数字
      ]

      invalidFormats.forEach((format) => {
        expect(isSrtTimeFormat(format)).toBe(false)
      })
    })
  })

  describe('isVttTimeFormat', () => {
    it('应该验证有效的VTT时间格式', () => {
      const validFormats = [
        '0:00:00.000',
        '1:23:45.678',
        '23:59:59.999',
        '0:00:01.001',
        '12:34:56.789',
        '99:00:00.000', // VTT允许小时为两位数
      ]

      validFormats.forEach((format) => {
        expect(isVttTimeFormat(format)).toBe(true)
      })
    })

    it('应该拒绝无效的VTT时间格式', () => {
      const invalidFormats = [
        '00:00:00,000', // 使用逗号而非点
        '00:0:00.000', // 分钟不足两位
        '00:00:0.000', // 秒不足两位
        '00:00:00.00', // 毫秒不足三位
        '00:00:00.0000', // 毫秒超过三位
        '00:60:00.000', // 分钟超出范围
        '00:00:60.000', // 秒超出范围
        '100:00:00.000', // 小时超过两位数
        '00:00:00', // 缺少毫秒
        '00:00:00.', // 毫秒为空
        'abc:def:ghi.jkl', // 非数字字符
        '', // 空字符串
        '00:00:00.abc', // 毫秒部分非数字
      ]

      invalidFormats.forEach((format) => {
        expect(isVttTimeFormat(format)).toBe(false)
      })
    })
  })

  describe('isLrcTimeFormat', () => {
    it('应该验证有效的LRC时间格式', () => {
      const validFormats = ['[00:00.00]', '[01:23.45]', '[59:59.99]', '[00:01.01]', '[12:34.56]']

      validFormats.forEach((format) => {
        expect(isLrcTimeFormat(format)).toBe(true)
      })
    })

    it('应该拒绝无效的LRC时间格式', () => {
      const invalidFormats = [
        '00:00.00', // 缺少方括号
        '[0:00.00]', // 分钟不足两位
        '[00:0.00]', // 秒不足两位
        '[00:00.0]', // 毫秒不足两位
        '[00:00.000]', // 毫秒超过两位
        '[60:00.00]', // 分钟超出范围
        '[00:60.00]', // 秒超出范围
        '[00:00]', // 缺少毫秒
        '[00:00.]', // 毫秒为空
        '[abc:def.gh]', // 非数字字符
        '', // 空字符串
        '[00:00.ab]', // 毫秒部分非数字
        '(00:00.00)', // 错误的括号类型
      ]

      invalidFormats.forEach((format) => {
        expect(isLrcTimeFormat(format)).toBe(false)
      })
    })
  })

  describe('isValidMimeType', () => {
    // 创建模拟File对象的辅助函数
    const createMockFile = (type: string): File => {
      return {
        type,
        name: 'test.file',
        size: 1024,
        lastModified: Date.now(),
      } as File
    }

    it('应该验证允许的MIME类型', () => {
      const allowedTypes = ['image/jpeg', 'image/png', 'text/plain']

      allowedTypes.forEach((type) => {
        const file = createMockFile(type)
        expect(isValidMimeType(file, allowedTypes)).toBe(true)
      })
    })

    it('应该拒绝不允许的MIME类型', () => {
      const allowedTypes = ['image/jpeg', 'image/png']
      const disallowedTypes = ['text/plain', 'application/pdf', 'video/mp4']

      disallowedTypes.forEach((type) => {
        const file = createMockFile(type)
        expect(isValidMimeType(file, allowedTypes)).toBe(false)
      })
    })

    it('应该处理空的允许类型列表', () => {
      const file = createMockFile('image/jpeg')
      expect(isValidMimeType(file, [])).toBe(false)
    })

    it('应该处理空的文件类型', () => {
      const file = createMockFile('')
      const allowedTypes = ['image/jpeg']
      expect(isValidMimeType(file, allowedTypes)).toBe(false)
    })
  })

  describe('isAudioFile', () => {
    it('应该验证有效的音频文件扩展名', () => {
      const validAudioFiles = [
        'song.mp3',
        'audio.wav',
        'music.m4a',
        'sound.ogg',
        'track.flac',
        'voice.aac',
        'SONG.MP3', // 大写扩展名
        'audio.WAV',
        'path/to/song.mp3',
        'complex.file.name.mp3',
      ]

      validAudioFiles.forEach((filename) => {
        expect(isAudioFile(filename)).toBe(true)
      })
    })

    it('应该拒绝无效的音频文件扩展名', () => {
      const invalidAudioFiles = [
        'video.mp4',
        'document.pdf',
        'image.jpg',
        'text.txt',
        'subtitle.srt',
        'file.exe',
        'noextension',
        'file.',
        '',
        'file.mp3.txt', // 虽然包含mp3但最终扩展名是txt
      ]

      invalidAudioFiles.forEach((filename) => {
        expect(isAudioFile(filename)).toBe(false)
      })
    })
  })

  describe('isVideoFile', () => {
    it('应该验证有效的视频文件扩展名', () => {
      const validVideoFiles = [
        'movie.mp4',
        'video.mov',
        'clip.avi',
        'film.mkv',
        'web.webm',
        'old.wmv',
        'MOVIE.MP4', // 大写扩展名
        'video.MOV',
        'path/to/movie.mp4',
        'complex.file.name.mp4',
      ]

      validVideoFiles.forEach((filename) => {
        expect(isVideoFile(filename)).toBe(true)
      })
    })

    it('应该拒绝无效的视频文件扩展名', () => {
      const invalidVideoFiles = [
        'audio.mp3',
        'document.pdf',
        'image.jpg',
        'text.txt',
        'subtitle.srt',
        'file.exe',
        'noextension',
        'file.',
        '',
        'file.mp4.txt', // 虽然包含mp4但最终扩展名是txt
      ]

      invalidVideoFiles.forEach((filename) => {
        expect(isVideoFile(filename)).toBe(false)
      })
    })
  })

  describe('isSubtitleFile', () => {
    it('应该验证有效的字幕文件扩展名', () => {
      const validSubtitleFiles = [
        'subtitle.srt',
        'captions.vtt',
        'lyrics.lrc',
        'advanced.ass',
        'simple.ssa',
        'SUBTITLE.SRT', // 大写扩展名
        'captions.VTT',
        'path/to/subtitle.srt',
        'complex.file.name.srt',
      ]

      validSubtitleFiles.forEach((filename) => {
        expect(isSubtitleFile(filename)).toBe(true)
      })
    })

    it('应该拒绝无效的字幕文件扩展名', () => {
      const invalidSubtitleFiles = [
        'audio.mp3',
        'video.mp4',
        'document.pdf',
        'image.jpg',
        'text.txt',
        'file.exe',
        'noextension',
        'file.',
        '',
        'file.srt.txt', // 虽然包含srt但最终扩展名是txt
      ]

      invalidSubtitleFiles.forEach((filename) => {
        expect(isSubtitleFile(filename)).toBe(false)
      })
    })
  })

  describe('isValidHexColor', () => {
    it('应该验证有效的十六进制颜色代码', () => {
      const validColors = [
        '#000000', // 黑色
        '#FFFFFF', // 白色
        '#FF0000', // 红色
        '#00FF00', // 绿色
        '#0000FF', // 蓝色
        '#123abc', // 小写字母
        '#ABC123', // 大写字母
        '#aB1C2d', // 混合大小写
        '#000', // 3位格式
        '#FFF', // 3位白色
        '#f0f', // 3位紫色
        '#123', // 3位自定义颜色
      ]

      validColors.forEach((color) => {
        expect(isValidHexColor(color)).toBe(true)
      })
    })

    it('应该拒绝无效的十六进制颜色代码', () => {
      const invalidColors = [
        '000000', // 缺少#号
        '#00000', // 5位数字
        '#0000000', // 7位数字
        '#GGGGGG', // 非十六进制字符
        '#12345G', // 包含非十六进制字符
        '', // 空字符串
        '#', // 只有#号
        'red', // 颜色名称
        'rgb(255,0,0)', // RGB格式
        '#00 00 00', // 包含空格
        '#00-00-00', // 包含连字符
      ]

      invalidColors.forEach((color) => {
        expect(isValidHexColor(color)).toBe(false)
      })
    })
  })

  describe('isNumericString', () => {
    it('应该验证有效的数字字符串', () => {
      const validNumbers = [
        '0',
        '123',
        '-456',
        '3.14',
        '-2.718',
        '0.001',
        '1000000',
        '1e10', // 科学记数法
        '1.5e-3',
        '-1.23e+5',
        '.5', // 小数点开头
        '5.', // 小数点结尾
        '0.0',
        '00123', // 前导零
      ]

      validNumbers.forEach((numStr) => {
        expect(isNumericString(numStr)).toBe(true)
      })
    })

    it('应该拒绝无效的数字字符串', () => {
      const invalidNumbers = [
        '', // 空字符串
        'abc',
        '12abc',
        'abc123',
        '12.34.56', // 多个小数点
        '1..2',
        '++123',
        '--456',
        '1-2',
        '1+2',
        ' 123', // 前导空格
        '123 ', // 后导空格
        'NaN',
        'Infinity',
        '-Infinity',
        'null',
        'undefined',
      ]

      invalidNumbers.forEach((numStr) => {
        expect(isNumericString(numStr)).toBe(false)
      })
    })
  })

  describe('isPositiveIntegerString', () => {
    it('应该验证有效的正整数字符串', () => {
      const validPositiveIntegers = [
        '1',
        '123',
        '1000000',
        '007', // 前导零也算有效
        '42',
      ]

      validPositiveIntegers.forEach((intStr) => {
        expect(isPositiveIntegerString(intStr)).toBe(true)
      })
    })

    it('应该拒绝无效的正整数字符串', () => {
      const invalidPositiveIntegers = [
        '0', // 零不是正数
        '-1', // 负数
        '-123',
        '3.14', // 小数
        '1.0', // 虽然值是整数但格式是小数
        '', // 空字符串
        'abc',
        '12abc',
        'abc123',
        '1e10', // 科学记数法
        ' 123', // 前导空格
        '123 ', // 后导空格
        '+123', // 正号（虽然逻辑上是正数但格式不对）
      ]

      invalidPositiveIntegers.forEach((intStr) => {
        expect(isPositiveIntegerString(intStr)).toBe(false)
      })
    })
  })
})

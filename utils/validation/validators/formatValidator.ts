/**
 * 格式验证模块
 * 处理各种格式的验证
 */

/**
 * 验证SRT时间格式
 */
export function isSrtTimeFormat(time: string): boolean {
  const srtTimeRegex = /^\d{2}:\d{2}:\d{2},\d{3}$/
  if (!srtTimeRegex.test(time)) {
    return false
  }

  // 验证时间逻辑有效性
  const parts = time.split(/[:,]/)
  const hours = parseInt(parts[0], 10)
  const minutes = parseInt(parts[1], 10)
  const seconds = parseInt(parts[2], 10)
  const milliseconds = parseInt(parts[3], 10)

  return hours <= 23 && minutes <= 59 && seconds <= 59 && milliseconds <= 999
}

/**
 * 验证VTT时间格式
 */
export function isVttTimeFormat(time: string): boolean {
  const vttTimeRegex = /^\d{1,2}:\d{2}:\d{2}\.\d{3}$/
  if (!vttTimeRegex.test(time)) {
    return false
  }

  // 验证时间逻辑有效性（VTT允许小时超过23）
  const parts = time.split(/[:.]/)
  const minutes = parseInt(parts[1], 10)
  const seconds = parseInt(parts[2], 10)
  const milliseconds = parseInt(parts[3], 10)

  return minutes <= 59 && seconds <= 59 && milliseconds <= 999
}

/**
 * 验证LRC时间格式
 */
export function isLrcTimeFormat(time: string): boolean {
  const lrcTimeRegex = /^\[\d{2}:\d{2}\.\d{2}\]$/
  if (!lrcTimeRegex.test(time)) {
    return false
  }

  // 验证时间逻辑有效性
  const content = time.slice(1, -1) // 移除方括号
  const parts = content.split(/[:.]/)
  const minutes = parseInt(parts[0], 10)
  const seconds = parseInt(parts[1], 10)
  const centiseconds = parseInt(parts[2], 10)

  return minutes <= 59 && seconds <= 59 && centiseconds <= 99
}

/**
 * 验证文件MIME类型
 */
export function isValidMimeType(file: File, allowedTypes: string[]): boolean {
  return allowedTypes.includes(file.type)
}

/**
 * 验证音频文件格式
 */
export function isAudioFile(filename: string): boolean {
  const audioExtensions = ['.mp3', '.wav', '.m4a', '.ogg', '.flac', '.aac']
  const extension = filename.toLowerCase().substring(filename.lastIndexOf('.'))
  return audioExtensions.includes(extension)
}

/**
 * 验证视频文件格式
 */
export function isVideoFile(filename: string): boolean {
  const videoExtensions = ['.mp4', '.mov', '.avi', '.mkv', '.webm', '.wmv']
  const extension = filename.toLowerCase().substring(filename.lastIndexOf('.'))
  return videoExtensions.includes(extension)
}

/**
 * 验证字幕文件格式
 */
export function isSubtitleFile(filename: string): boolean {
  const subtitleExtensions = ['.srt', '.vtt', '.lrc', '.ass', '.ssa']
  const extension = filename.toLowerCase().substring(filename.lastIndexOf('.'))
  return subtitleExtensions.includes(extension)
}

/**
 * 验证十六进制颜色代码
 */
export function isValidHexColor(color: string): boolean {
  const hexColorRegex = /^#([A-Fa-f0-9]{6}|[A-Fa-f0-9]{3})$/
  return hexColorRegex.test(color)
}

/**
 * 验证数字字符串
 */
export function isNumericString(value: string): boolean {
  if (value.trim() !== value) return false // 拒绝前后有空格
  if (value === '' || value === 'Infinity' || value === '-Infinity' || value === 'NaN') return false
  return !isNaN(Number(value)) && !isNaN(parseFloat(value))
}

/**
 * 验证正整数字符串
 */
export function isPositiveIntegerString(value: string): boolean {
  if (value.trim() !== value) return false // 拒绝前后有空格
  if (value.startsWith('+')) return false // 拒绝正号
  if (value.includes('.')) return false // 拒绝小数点
  if (value.includes('e') || value.includes('E')) return false // 拒绝科学记数法
  const num = Number(value)
  return Number.isInteger(num) && num > 0
}

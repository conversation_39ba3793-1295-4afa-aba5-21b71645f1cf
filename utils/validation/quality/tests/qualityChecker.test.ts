import { describe, it, expect } from 'vitest'
import {
  detectTranslationErrors,
  checkTranslationQuality,
  hasFormatIssues,
  calculateQualityScore,
  calculateConfidence,
  generateSuggestions,
} from '../qualityChecker'

describe('Quality Checker', () => {
  describe('detectTranslationErrors', () => {
    describe('Missing translation errors', () => {
      it('应检测空翻译文本', () => {
        const originalText = 'Hello world'
        const translatedText = ''

        const errors = detectTranslationErrors(originalText, translatedText)

        expect(errors).toHaveLength(1)
        expect(errors[0]).toEqual({
          type: 'missing',
          message: '翻译文本为空',
          severity: 'high',
        })
      })

      it('应检测只包含空格的翻译文本', () => {
        const originalText = 'Hello world'
        const translatedText = '   '

        const errors = detectTranslationErrors(originalText, translatedText)

        expect(errors).toHaveLength(1)
        expect(errors[0]).toEqual({
          type: 'missing',
          message: '翻译文本为空',
          severity: 'high',
        })
      })

      it('应检测null和undefined翻译文本', () => {
        const originalText = 'Hello world'

        const errorsNull = detectTranslationErrors(originalText, null as unknown as string)
        const errorsUndefined = detectTranslationErrors(originalText, undefined as unknown as string)

        expect(errorsNull).toHaveLength(1)
        expect(errorsNull[0].type).toBe('missing')
        expect(errorsUndefined).toHaveLength(1)
        expect(errorsUndefined[0].type).toBe('missing')
      })
    })

    describe('Length inconsistency errors', () => {
      it('应检测翻译文本过短的情况', () => {
        const originalText = 'This is a very long sentence that contains many words and should be translated properly.'
        const translatedText = '短'

        const errors = detectTranslationErrors(originalText, translatedText)

        const lengthError = errors.find((error) => error.type === 'inconsistent')
        expect(lengthError).toBeDefined()
        expect(lengthError).toEqual({
          type: 'inconsistent',
          message: '翻译长度与原文差异过大',
          severity: 'medium',
        })
      })

      it('应检测翻译文本过长的情况', () => {
        const originalText = '短'
        const translatedText =
          'This is an extremely long translation that is way too verbose and contains far too many words compared to the original short text which should result in a length inconsistency error being detected by our quality checker system.'

        const errors = detectTranslationErrors(originalText, translatedText)

        const lengthError = errors.find((error) => error.type === 'inconsistent')
        expect(lengthError).toBeDefined()
        expect(lengthError).toEqual({
          type: 'inconsistent',
          message: '翻译长度与原文差异过大',
          severity: 'medium',
        })
      })

      it('应接受合理长度比例的翻译', () => {
        const originalText = 'Hello world, how are you today?'
        const translatedText = '你好世界，你今天好吗？'

        const errors = detectTranslationErrors(originalText, translatedText)

        const lengthError = errors.find((error) => error.type === 'inconsistent')
        expect(lengthError).toBeUndefined()
      })

      it('应检测边界长度比例', () => {
        const originalText = 'Hello world!'
        const shortTranslation = 'Hi' // 比例约0.17 < 0.3
        const longTranslation = 'Hello world! Hello world! Hello world! Hello world!' // 比例约4 > 3

        const shortErrors = detectTranslationErrors(originalText, shortTranslation)
        const longErrors = detectTranslationErrors(originalText, longTranslation)

        expect(shortErrors.some((e) => e.type === 'inconsistent')).toBe(true)
        expect(longErrors.some((e) => e.type === 'inconsistent')).toBe(true)
      })
    })

    describe('Format errors', () => {
      it('应检测连续特殊字符格式问题', () => {
        const originalText = 'Hello world'
        const translatedText = '你好###@@@世界'

        const errors = detectTranslationErrors(originalText, translatedText)

        const formatError = errors.find((error) => error.type === 'format')
        expect(formatError).toBeDefined()
        expect(formatError).toEqual({
          type: 'format',
          message: '翻译文本格式异常',
          severity: 'low',
        })
      })

      it('应检测其他格式问题', () => {
        const originalText = 'Hello'
        const translatedText = '你好!@#$%^'

        const errors = detectTranslationErrors(originalText, translatedText)

        const formatError = errors.find((error) => error.type === 'format')
        expect(formatError).toBeDefined()
      })

      it('应接受正常格式的翻译', () => {
        const originalText = 'Hello, world!'
        const translatedText = '你好，世界！'

        const errors = detectTranslationErrors(originalText, translatedText)

        const formatError = errors.find((error) => error.type === 'format')
        expect(formatError).toBeUndefined()
      })

      it('应接受包含少量特殊字符的正常文本', () => {
        const originalText = 'Hello world'
        const translatedText = '你好，世界！'

        const errors = detectTranslationErrors(originalText, translatedText)

        const formatError = errors.find((error) => error.type === 'format')
        expect(formatError).toBeUndefined()
      })
    })

    describe('Multiple errors', () => {
      it('应检测多种错误类型', () => {
        const originalText = 'Hello world'
        const translatedText = '你好###@@@世界' // 既有长度问题又有格式问题（但实际长度比例可能不会触发inconsistent错误）

        const errors = detectTranslationErrors(originalText, translatedText)

        expect(errors.length).toBeGreaterThanOrEqual(1)
        // 至少应该检测到格式错误
        expect(errors.some((e) => e.type === 'format')).toBe(true)
        // 注意：长度检查可能不会触发，因为比例可能在0.3-3之间
      })

      it('当翻译为空时应只返回missing错误', () => {
        const originalText = 'Hello world'
        const translatedText = ''

        const errors = detectTranslationErrors(originalText, translatedText)

        expect(errors).toHaveLength(1)
        expect(errors[0].type).toBe('missing')
      })
    })

    describe('Valid translations', () => {
      it('应对高质量翻译返回空错误数组', () => {
        const originalText = 'Hello, how are you doing today?'
        const translatedText = '你好，你今天过得怎么样？'

        const errors = detectTranslationErrors(originalText, translatedText)

        expect(errors).toHaveLength(0)
      })

      it('应对不同语言对的翻译返回空错误数组', () => {
        const originalText = 'Good morning'
        const translatedText = 'Buenos días'

        const errors = detectTranslationErrors(originalText, translatedText)

        expect(errors).toHaveLength(0)
      })
    })
  })

  describe('checkTranslationQuality', () => {
    it('应返回正确的质量评估结构', () => {
      const source = 'Hello world'
      const translation = '你好世界'

      const quality = checkTranslationQuality(source, translation)

      expect(quality).toHaveProperty('score')
      expect(quality).toHaveProperty('confidence')
      expect(quality).toHaveProperty('suggestions')
      expect(typeof quality.score).toBe('number')
      expect(typeof quality.confidence).toBe('number')
      expect(Array.isArray(quality.suggestions)).toBe(true)
    })

    it('应对正常翻译返回高质量分数', () => {
      const source = 'Hello world'
      const translation = '你好世界'

      const quality = checkTranslationQuality(source, translation)

      expect(quality.score).toBe(0.8)
      expect(quality.confidence).toBe(0.75)
      expect(quality.suggestions).toHaveLength(0)
    })

    it('应对空输入返回低分数', () => {
      const source = 'Hello world'
      const translation = ''

      const quality = checkTranslationQuality(source, translation)

      expect(quality.score).toBe(0)
      expect(quality.confidence).toBe(0)
      expect(quality.suggestions).toContain('缺少翻译内容')
    })

    it('应对空源文本返回低分数', () => {
      const source = ''
      const translation = '你好世界'

      const quality = checkTranslationQuality(source, translation)

      expect(quality.score).toBe(0)
      expect(quality.confidence).toBe(0)
    })

    it('应对长度异常翻译返回低分数', () => {
      const source = 'Hello'
      const translation = 'This is an extremely long translation that is way too verbose'

      const quality = checkTranslationQuality(source, translation)

      expect(quality.score).toBe(0.3)
      expect(quality.confidence).toBe(0.75)
    })

    it('应对过短翻译生成建议', () => {
      const source = 'This is a long sentence that should be translated properly.'
      const translation = '短'

      const quality = checkTranslationQuality(source, translation)

      expect(quality.suggestions).toContain('翻译可能过于简短')
    })

    it('应对both为空返回正确的质量评估', () => {
      const quality = checkTranslationQuality('', '')

      expect(quality.score).toBe(0)
      expect(quality.confidence).toBe(0)
      // 注意：根据源码，空翻译仍然会生成"缺少翻译内容"建议
      expect(quality.suggestions).toContain('缺少翻译内容')
    })
  })

  describe('Edge cases', () => {
    it('应处理包含换行符的文本', () => {
      const originalText = 'Hello\nworld\ntoday'
      const translatedText = '你好\n世界\n今天'

      const errors = detectTranslationErrors(originalText, translatedText)
      const quality = checkTranslationQuality(originalText, translatedText)

      expect(errors).toHaveLength(0)
      expect(quality.score).toBeGreaterThan(0)
    })

    it('应处理包含数字和符号的文本', () => {
      const originalText = 'Price: $19.99 (USD)'
      const translatedText = '价格：$19.99 (美元)'

      const errors = detectTranslationErrors(originalText, translatedText)
      const quality = checkTranslationQuality(originalText, translatedText)

      expect(errors).toHaveLength(0)
      expect(quality.score).toBeGreaterThan(0)
    })

    it('应处理很长的文本', () => {
      const originalText = 'A'.repeat(1000)
      const translatedText = '啊'.repeat(1000)

      const errors = detectTranslationErrors(originalText, translatedText)
      const quality = checkTranslationQuality(originalText, translatedText)

      expect(errors).toHaveLength(0)
      expect(quality.score).toBeGreaterThan(0)
    })

    it('应处理单字符文本', () => {
      const originalText = 'A'
      const translatedText = '啊'

      const errors = detectTranslationErrors(originalText, translatedText)
      const quality = checkTranslationQuality(originalText, translatedText)

      expect(errors).toHaveLength(0)
      expect(quality.score).toBeGreaterThan(0)
    })

    it('应处理包含emoji的文本', () => {
      const originalText = 'Hello 😊 world'
      const translatedText = '你好 😊 世界'

      const errors = detectTranslationErrors(originalText, translatedText)
      const quality = checkTranslationQuality(originalText, translatedText)

      expect(errors).toHaveLength(0)
      expect(quality.score).toBeGreaterThan(0)
    })
  })

  describe('hasFormatIssues', () => {
    it('应检测连续3个以上特殊字符', () => {
      expect(hasFormatIssues('正常文本###特殊字符')).toBe(true)
      expect(hasFormatIssues('文本@@@异常')).toBe(true)
      expect(hasFormatIssues('Hello!@#$%world')).toBe(true)
    })

    it('应检测连续符号组合', () => {
      expect(hasFormatIssues('文本!@#内容')).toBe(true)
      expect(hasFormatIssues('Hello***world')).toBe(true)
      expect(hasFormatIssues('测试$$$文本')).toBe(true)
    })

    it('应接受正常文本格式', () => {
      expect(hasFormatIssues('正常的中文文本')).toBe(false)
      expect(hasFormatIssues('Normal English text')).toBe(false)
      expect(hasFormatIssues('Mixed 中英文 text')).toBe(false)
    })

    it('应接受少量标点符号', () => {
      expect(hasFormatIssues('Hello, world!')).toBe(false)
      expect(hasFormatIssues('你好，世界！')).toBe(false)
      expect(hasFormatIssues('Price: $19.99')).toBe(false)
    })

    it('应接受数字和单个特殊字符', () => {
      expect(hasFormatIssues('Version 1.2.3')).toBe(false)
      expect(hasFormatIssues('Email: <EMAIL>')).toBe(false)
      expect(hasFormatIssues('温度：25°C')).toBe(false)
    })

    it('应正确处理边界情况', () => {
      expect(hasFormatIssues('ab')).toBe(false) // 2个字符，不触发
      expect(hasFormatIssues('!@#')).toBe(true) // 正好3个特殊字符
      expect(hasFormatIssues('')).toBe(false) // 空字符串
      expect(hasFormatIssues('   ')).toBe(false) // 只有空格
    })

    it('应处理包含emoji的文本', () => {
      expect(hasFormatIssues('Hello 😊 world')).toBe(false)
      expect(hasFormatIssues('测试 🚀 文本')).toBe(false)
      expect(hasFormatIssues('😊😊😊')).toBe(true) // emoji被正则表达式认为是特殊字符
    })

    it('应处理换行符和制表符', () => {
      expect(hasFormatIssues('第一行\n第二行')).toBe(false)
      expect(hasFormatIssues('列1\t列2')).toBe(false)
      expect(hasFormatIssues('正常\r\n文本')).toBe(false)
    })
  })

  describe('calculateQualityScore', () => {
    it('应对正常翻译返回高分数', () => {
      const score = calculateQualityScore('Hello world', '你好世界')
      expect(score).toBe(0.8)
    })

    it('应对空输入返回0分', () => {
      expect(calculateQualityScore('', '')).toBe(0)
      expect(calculateQualityScore('Hello', '')).toBe(0)
      expect(calculateQualityScore('', '你好')).toBe(0)
    })

    it('应对长度异常返回低分', () => {
      const shortScore = calculateQualityScore('Very long text here', '短')
      const longScore = calculateQualityScore('短', 'This is a very long translation that exceeds normal ratios')

      expect(shortScore).toBe(0.3)
      expect(longScore).toBe(0.3)
    })

    it('应对边界长度比例正确评分', () => {
      const text = 'Hello world!'
      const shortBoundary = 'Hi!' // 约0.25 < 0.3
      const longBoundary = text.repeat(4) // 约4倍 > 3

      expect(calculateQualityScore(text, shortBoundary)).toBe(0.3)
      expect(calculateQualityScore(text, longBoundary)).toBe(0.3)
    })
  })

  describe('calculateConfidence', () => {
    it('应对有效输入返回固定置信度', () => {
      const confidence = calculateConfidence('Hello world', '你好世界')
      expect(confidence).toBe(0.75)
    })

    it('应对空输入返回0置信度', () => {
      expect(calculateConfidence('', '')).toBe(0)
      expect(calculateConfidence('Hello', '')).toBe(0)
      expect(calculateConfidence('', '你好')).toBe(0)
    })

    it('应对不同长度文本返回一致置信度', () => {
      const short = calculateConfidence('Hi', '你好')
      const long = calculateConfidence('This is a long sentence.', '这是一个长句子。')

      expect(short).toBe(0.75)
      expect(long).toBe(0.75)
    })
  })

  describe('generateSuggestions', () => {
    it('应对空翻译生成缺失建议', () => {
      const suggestions = generateSuggestions('Hello world', '')
      expect(suggestions).toContain('缺少翻译内容')
    })

    it('应对过短翻译生成长度建议', () => {
      const suggestions = generateSuggestions('This is a very long sentence that needs proper translation.', '短')
      expect(suggestions).toContain('翻译可能过于简短')
    })

    it('应对正常翻译不生成建议', () => {
      const suggestions = generateSuggestions('Hello world', '你好世界')
      expect(suggestions).toHaveLength(0)
    })

    it('应对多种问题生成多个建议', () => {
      const suggestions = generateSuggestions('This is a long text that should be translated properly.', '')
      expect(suggestions).toContain('缺少翻译内容')
      // 空翻译不会触发长度建议，因为长度为0
    })

    it('应正确计算长度阈值', () => {
      const sourceText = 'Hello world!' // 12个字符
      const shortText = 'Hi' // 2个字符，< 12 * 0.3 = 3.6

      const suggestions = generateSuggestions(sourceText, shortText)
      expect(suggestions).toContain('翻译可能过于简短')
    })

    it('应对边界长度正确处理', () => {
      const sourceText = 'Hello world!' // 12个字符
      const boundaryText = 'Hi' // 2个字符，< 12 * 0.3 = 3.6，应该触发建议

      const suggestions = generateSuggestions(sourceText, boundaryText)
      expect(suggestions).toContain('翻译可能过于简短')
    })
  })
})

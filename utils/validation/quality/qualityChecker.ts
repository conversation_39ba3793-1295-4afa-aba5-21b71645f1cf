/**
 * 翻译质量检查模块
 */

// 原有的TranslationError接口（来自translationQuality.ts）
export interface TranslationError {
  type: 'missing' | 'extra' | 'inconsistent' | 'format'
  message: string
  severity: 'low' | 'medium' | 'high'
}

export interface TranslationQuality {
  score: number
  confidence: number
  suggestions: string[]
}

/**
 * 检测翻译错误（来自translationQuality.ts）
 */
export function detectTranslationErrors(originalText: string, translatedText: string): TranslationError[] {
  const errors: TranslationError[] = []

  // 检查是否为空
  if (!translatedText || translatedText.trim() === '') {
    errors.push({
      type: 'missing',
      message: '翻译文本为空',
      severity: 'high',
    })
    return errors
  }

  // 检查长度异常
  const lengthRatio = translatedText.length / originalText.length
  if (lengthRatio > 3 || lengthRatio < 0.3) {
    errors.push({
      type: 'inconsistent',
      message: '翻译长度与原文差异过大',
      severity: 'medium',
    })
  }

  // 检查格式问题
  if (hasFormatIssues(translatedText)) {
    errors.push({
      type: 'format',
      message: '翻译文本格式异常',
      severity: 'low',
    })
  }

  return errors
}

/**
 * 检查格式问题（来自translationQuality.ts）
 */
export function hasFormatIssues(text: string): boolean {
  // 检查是否有连续的特殊字符
  return /[^\w\s\u4e00-\u9fff]{3,}/.test(text)
}

export function checkTranslationQuality(source: string, translation: string): TranslationQuality {
  // 基础质量检查逻辑
  const score = calculateQualityScore(source, translation)
  const confidence = calculateConfidence(source, translation)
  const suggestions = generateSuggestions(source, translation)

  return {
    score,
    confidence,
    suggestions,
  }
}

export function calculateQualityScore(source: string, translation: string): number {
  // 简单的质量评分逻辑
  if (!source || !translation) return 0

  // 基于长度比例的简单评分
  const lengthRatio = translation.length / source.length
  if (lengthRatio < 0.3 || lengthRatio > 3) return 0.3

  return 0.8 // 默认较高分数
}

export function calculateConfidence(source: string, translation: string): number {
  // 置信度计算
  if (!source || !translation) return 0
  return 0.75
}

export function generateSuggestions(source: string, translation: string): string[] {
  const suggestions: string[] = []

  if (!translation) {
    suggestions.push('缺少翻译内容')
  }

  if (translation.length < source.length * 0.3) {
    suggestions.push('翻译可能过于简短')
  }

  return suggestions
}

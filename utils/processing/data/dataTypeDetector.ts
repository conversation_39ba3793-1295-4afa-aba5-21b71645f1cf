/**
 * 数据类型检测工具
 * 用于识别音频识别返回数据的格式类型
 */

import Decimal from 'decimal.js'
export type RecognitionDataType = 'word_level' | 'sentence_level' | 'mixed_level'

export interface DataTypeDetectionResult {
  type: RecognitionDataType
  confidence: number
  characteristics: {
    hasWordsArray: boolean
    averageTextLength: number
    utteranceCount: number
    timeUnit: 'seconds' | 'milliseconds'
  }
}

interface UtteranceData {
  start_time?: number
  end_time?: number
  text?: string
  words?: unknown[]
}

/**
 * 检测音频识别数据的类型
 * @param utterances 音频识别结果的utterances数组
 * @returns 检测结果
 */
export function detectRecognitionDataType(utterances: UtteranceData[]): DataTypeDetectionResult {
  if (!utterances || utterances.length === 0) {
    return {
      type: 'word_level',
      confidence: 0,
      characteristics: {
        hasWordsArray: false,
        averageTextLength: 0,
        utteranceCount: 0,
        timeUnit: 'seconds',
      },
    }
  }

  const firstUtterance = utterances[0]
  const hasWordsArray = Boolean(firstUtterance.words && Array.isArray(firstUtterance.words))

  // 计算平均文本长度
  const totalTextLength = utterances.reduce((sum, u) => new Decimal(sum).plus(u.text?.length || 0).toNumber(), 0)
  const averageTextLength = utterances.length > 0 ? new Decimal(totalTextLength).div(utterances.length).toNumber() : 0

  // 检测时间单位（通过时间戳大小判断）
  const firstTime = firstUtterance.start_time || firstUtterance.end_time || 0
  const timeUnit = firstTime > 100 ? 'milliseconds' : 'seconds'

  let type: RecognitionDataType
  let confidence: number

  // 更精细的类型判断逻辑
  if (hasWordsArray && averageTextLength > 10) {
    // 有words数组且文本较长，句子级别
    type = 'sentence_level'
    confidence = 0.9
  } else if (!hasWordsArray && averageTextLength < 5) {
    // 无words数组且文本很短，明确的单词级别
    type = 'word_level'
    confidence = 0.8
  } else if (hasWordsArray && averageTextLength >= 5 && averageTextLength <= 10) {
    // 有words数组但文本长度中等，混合级别
    type = 'mixed_level'
    confidence = 0.5
  } else if (!hasWordsArray && averageTextLength >= 5 && averageTextLength <= 17) {
    // 无words数组但文本长度中等，混合级别
    type = 'mixed_level'
    confidence = 0.5
  } else if (averageTextLength > 20) {
    // 文本较长，倾向于句子级别
    type = 'sentence_level'
    confidence = 0.7
  } else {
    // 其他情况，默认单词级别
    type = 'word_level'
    confidence = 0.6
  }

  return {
    type,
    confidence,
    characteristics: {
      hasWordsArray,
      averageTextLength,
      utteranceCount: utterances.length,
      timeUnit,
    },
  }
}

/**
 * 转换时间单位
 * @param time 时间值
 * @param from 源单位
 * @param to 目标单位
 * @returns 转换后的时间值
 */
export function convertTimeUnit(time: number, from: 'seconds' | 'milliseconds', to: 'seconds' | 'milliseconds'): number {
  if (from === to) return time

  if (from === 'milliseconds' && to === 'seconds') {
    return new Decimal(time).div(1000).toNumber()
  } else if (from === 'seconds' && to === 'milliseconds') {
    return new Decimal(time).times(1000).toNumber()
  }

  return time
}

import { describe, it, expect, vi } from 'vitest'
import { processRecognitionData, extractWordLevelData } from '../recognitionDataProcessor'
import type { RecognitionResult, RecognitionUtterance } from '~/composables/api/useSpeechRecognition'

// 扩展类型用于测试（因为实际API可能返回带words的数据）
interface ExtendedRecognitionUtterance extends RecognitionUtterance {
  words?: Array<{
    start_time?: number
    end_time?: number
    text?: string
    attribute?: {
      speaker?: string
    }
  }>
}

interface ExtendedRecognitionResult extends Omit<RecognitionResult, 'utterances'> {
  utterances: ExtendedRecognitionUtterance[]
}

// Mock the dependencies
vi.mock('~/utils/processing/text/sentenceProcessor', () => ({
  smartSentenceSegmentation: vi.fn((utterances, _config) => {
    // Simple mock implementation that returns utterances as is
    return utterances.map((utterance: RecognitionUtterance, index: number) => ({
      id: index,
      text: utterance.text || '',
      start: utterance.start_time || 0,
      end: utterance.end_time || 0,
      speaker: utterance.attribute?.speaker,
    }))
  }),
}))

describe('音频识别数据处理器', () => {
  describe('processRecognitionData', () => {
    it('应该正确处理空数据', () => {
      const emptyResult: RecognitionResult = {
        code: 0,
        duration: 0,
        utterances: [],
      }

      const result = processRecognitionData(emptyResult)

      expect(result.data).toEqual([])
      expect(result.metadata).toEqual({
        originalDataType: 'word_level',
        processingStrategy: 'empty_data',
        timeUnit: 'seconds',
        itemCount: 0,
        confidence: 0,
      })
    })

    it('应该正确处理句子级别数据（带words数组）', () => {
      const sentenceLevelResult: ExtendedRecognitionResult = {
        code: 0,
        duration: 10,
        utterances: [
          {
            start_time: 1.5,
            end_time: 3.2,
            text: '这是一个完整的句子',
            words: [
              { start_time: 1.5, end_time: 1.8, text: '这是' },
              { start_time: 1.8, end_time: 2.1, text: '一个' },
              { start_time: 2.1, end_time: 2.5, text: '完整的' },
              { start_time: 2.5, end_time: 3.2, text: '句子' },
            ],
          },
          {
            start_time: 4.0,
            end_time: 6.5,
            text: '另一个完整的句子用于测试',
            attribute: { speaker: 'speaker_1' },
          },
        ],
      }

      const result = processRecognitionData(sentenceLevelResult)

      expect(result.data).toHaveLength(2)
      expect(result.data[0]).toEqual({
        id: 0,
        text: '这是一个完整的句子',
        start: 1.5,
        end: 3.2,
        speaker: undefined,
      })
      expect(result.data[1]).toEqual({
        id: 1,
        text: '另一个完整的句子用于测试',
        start: 4.0,
        end: 6.5,
        speaker: 'speaker_1',
      })
      expect(result.metadata.originalDataType).toBe('sentence_level')
      expect(result.metadata.processingStrategy).toBe('direct_sentence_conversion')
      expect(result.metadata.confidence).toBe(0.9)
    })

    it('应该正确处理单词级别数据', () => {
      const wordLevelResult: RecognitionResult = {
        code: 0,
        duration: 10,
        utterances: [
          { start_time: 1.0, end_time: 1.5, text: '这是' },
          { start_time: 1.5, end_time: 2.0, text: '一个' },
          { start_time: 2.0, end_time: 2.5, text: '单词' },
          { start_time: 2.5, end_time: 3.0, text: '级别' },
          { start_time: 3.0, end_time: 3.5, text: '的' },
          { start_time: 3.5, end_time: 4.0, text: '数据' },
        ],
      }

      const result = processRecognitionData(wordLevelResult)

      expect(result.data).toHaveLength(6)
      expect(result.metadata.originalDataType).toBe('word_level')
      expect(result.metadata.processingStrategy).toBe('word_to_sentence_segmentation')
      expect(result.metadata.confidence).toBe(0.8)
    })

    it('应该正确处理毫秒时间戳', () => {
      const millisecondsResult: RecognitionResult = {
        code: 0,
        duration: 10000,
        utterances: [
          { start_time: 1500, end_time: 3200, text: '毫秒时间戳测试' },
          { start_time: 4000, end_time: 6500, text: '另一个毫秒时间戳' },
        ],
      }

      const result = processRecognitionData(millisecondsResult)

      expect(result.data[0].start).toBe(1.5) // 转换为秒
      expect(result.data[0].end).toBe(3.2)
      expect(result.data[1].start).toBe(4.0)
      expect(result.data[1].end).toBe(6.5)
      expect(result.metadata.timeUnit).toBe('milliseconds')
    })

    it('应该正确处理混合级别数据（有words数组的保守处理）', () => {
      const mixedResult: ExtendedRecognitionResult = {
        code: 0,
        duration: 10,
        utterances: [
          {
            start_time: 1.0,
            end_time: 2.0,
            text: '中等长度的文本内容',
            words: [{ start_time: 1.0, end_time: 2.0, text: '中等长度的文本内容' }],
          },
        ],
      }

      const result = processRecognitionData(mixedResult)

      expect(result.metadata.originalDataType).toBe('mixed_level')
      expect(result.metadata.processingStrategy).toBe('conservative_sentence_conversion')
      expect(result.metadata.confidence).toBe(0.5)
    })

    it('应该正确处理混合级别数据（无words数组的保守处理）', () => {
      const mixedResult: RecognitionResult = {
        code: 0,
        duration: 10,
        utterances: [
          { start_time: 1.0, end_time: 2.0, text: '中等长度的文本内容' },
          { start_time: 2.0, end_time: 3.0, text: '另一段中等文本' },
        ],
      }

      const result = processRecognitionData(mixedResult)

      expect(result.metadata.originalDataType).toBe('mixed_level')
      expect(result.metadata.processingStrategy).toBe('conservative_word_segmentation')
      expect(result.metadata.confidence).toBe(0.5)
    })

    it('应该正确传递句子分割配置', () => {
      const wordLevelResult: RecognitionResult = {
        code: 0,
        duration: 10,
        utterances: [
          { start_time: 1.0, end_time: 1.5, text: '测试' },
          { start_time: 1.5, end_time: 2.0, text: '配置' },
        ],
      }

      const segmentationConfig = {
        maxSentenceLength: 50,
        minSentenceLength: 5,
        maxPauseTime: 0.5,
      }

      const result = processRecognitionData(wordLevelResult, segmentationConfig)

      expect(result.data).toHaveLength(2)
      expect(result.metadata.processingStrategy).toBe('word_to_sentence_segmentation')
    })

    it('应该处理缺少必要字段的数据', () => {
      const incompleteResult: RecognitionResult = {
        code: 0,
        duration: 10,
        utterances: [
          { start_time: 1.0, end_time: 2.0, text: '' }, // 缺少text，用空字符串
          { start_time: 0, end_time: 0, text: '只有文本没有时间' }, // 添加默认时间
        ],
      }

      const result = processRecognitionData(incompleteResult)

      expect(result.data).toHaveLength(2)
      expect(result.data[0].text).toBe('')
      expect(result.data[1].start).toBe(0)
      expect(result.data[1].end).toBe(0)
    })
  })

  describe('extractWordLevelData', () => {
    it('应该正确提取单词级数据', () => {
      const utterances = [
        {
          start_time: 1000,
          end_time: 3000,
          text: '这是一个句子',
          words: [
            { start_time: 1000, end_time: 1500, text: '这是' },
            { start_time: 1500, end_time: 2000, text: '一个' },
            { start_time: 2000, end_time: 2500, text: '句子' },
          ],
        },
        {
          start_time: 4000,
          end_time: 6000,
          text: '另一个句子',
          words: [
            { start_time: 4000, end_time: 5000, text: '另一个' },
            { start_time: 5000, end_time: 6000, text: '句子' },
          ],
          attribute: { speaker: 'speaker_1' },
        },
      ]

      const result = extractWordLevelData(utterances)

      expect(result).toHaveLength(5)
      expect(result[0]).toEqual({
        id: 0,
        text: '这是',
        start: 1.0, // 毫秒转秒
        end: 1.5,
        speaker: undefined,
      })
      expect(result[4]).toEqual({
        id: 4,
        text: '句子',
        start: 5.0,
        end: 6.0,
        speaker: 'speaker_1',
      })
    })

    it('应该过滤空格和空文本', () => {
      const utterances = [
        {
          start_time: 1000,
          end_time: 3000,
          text: '测试过滤',
          words: [
            { start_time: 1000, end_time: 1500, text: '测试' },
            { start_time: 1500, end_time: 2000, text: ' ' }, // 应该被过滤
            { start_time: 2000, end_time: 2500, text: '' }, // 应该被过滤
            { start_time: 2500, end_time: 3000, text: '过滤' },
          ],
        },
      ]

      const result = extractWordLevelData(utterances)

      expect(result).toHaveLength(2)
      expect(result[0].text).toBe('测试')
      expect(result[1].text).toBe('过滤')
    })

    it('应该处理没有words数组的utterances', () => {
      const utterances = [
        {
          start_time: 1000,
          end_time: 2000,
          text: '没有words数组',
        },
        {
          start_time: 3000,
          end_time: 4000,
          text: '另一个没有words',
          words: [], // 空数组
        },
      ]

      const result = extractWordLevelData(utterances)

      expect(result).toHaveLength(0)
    })

    it('应该正确处理秒时间单位', () => {
      const utterances = [
        {
          start_time: 1.5,
          end_time: 3.0,
          text: '秒时间戳',
          words: [
            { start_time: 1.5, end_time: 2.0, text: '秒' },
            { start_time: 2.0, end_time: 3.0, text: '时间戳' },
          ],
        },
      ]

      const result = extractWordLevelData(utterances, 'seconds')

      expect(result).toHaveLength(2)
      expect(result[0].start).toBe(1.5)
      expect(result[0].end).toBe(2.0)
      expect(result[1].start).toBe(2.0)
      expect(result[1].end).toBe(3.0)
    })

    it('应该继承utterance的speaker信息', () => {
      const utterances = [
        {
          start_time: 1000,
          end_time: 2000,
          text: '测试speaker',
          words: [
            { start_time: 1000, end_time: 1500, text: '测试' },
            { start_time: 1500, end_time: 2000, text: 'speaker', attribute: { speaker: 'word_speaker' } },
          ],
          attribute: { speaker: 'utterance_speaker' },
        },
      ]

      const result = extractWordLevelData(utterances)

      expect(result).toHaveLength(2)
      expect(result[0].speaker).toBe('utterance_speaker') // 没有单词级speaker，使用utterance级
      expect(result[1].speaker).toBe('word_speaker') // 优先使用单词级speaker
    })

    it('应该处理缺少时间戳的单词', () => {
      const utterances = [
        {
          start_time: 1000,
          end_time: 2000,
          text: '缺少时间戳',
          words: [
            { text: '缺少', start_time: undefined, end_time: undefined },
            { start_time: 1500, end_time: 2000, text: '时间戳' },
          ],
        },
      ]

      const result = extractWordLevelData(utterances)

      expect(result).toHaveLength(2)
      expect(result[0].start).toBe(0)
      expect(result[0].end).toBe(0)
      expect(result[1].start).toBe(1.5)
      expect(result[1].end).toBe(2.0)
    })
  })
})

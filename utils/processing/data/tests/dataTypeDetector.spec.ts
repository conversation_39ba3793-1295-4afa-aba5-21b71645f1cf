import { describe, it, expect } from 'vitest'
import { detectRecognitionDataType, convertTimeUnit } from '../dataTypeDetector'

describe('数据类型检测器', () => {
  describe('detectRecognitionDataType', () => {
    it('应该正确处理空数据', () => {
      const result = detectRecognitionDataType([])

      expect(result.type).toBe('word_level')
      expect(result.confidence).toBe(0)
      expect(result.characteristics).toEqual({
        hasWordsArray: false,
        averageTextLength: 0,
        utteranceCount: 0,
        timeUnit: 'seconds',
      })
    })

    it('应该正确检测句子级别数据（带words数组）', () => {
      const utterances = [
        {
          start_time: 1.5,
          end_time: 3.2,
          text: '这是一个完整的句子',
          words: [
            { start_time: 1.5, end_time: 1.8, text: '这是' },
            { start_time: 1.8, end_time: 2.1, text: '一个' },
            { start_time: 2.1, end_time: 2.5, text: '完整的' },
            { start_time: 2.5, end_time: 3.2, text: '句子' },
          ],
        },
        {
          start_time: 4.0,
          end_time: 6.5,
          text: '另一个完整的句子用于测试',
          words: [
            { start_time: 4.0, end_time: 4.3, text: '另一个' },
            { start_time: 4.3, end_time: 4.6, text: '完整的' },
            { start_time: 4.6, end_time: 5.0, text: '句子' },
            { start_time: 5.0, end_time: 5.5, text: '用于' },
            { start_time: 5.5, end_time: 6.5, text: '测试' },
          ],
        },
      ]

      const result = detectRecognitionDataType(utterances)

      expect(result.type).toBe('sentence_level')
      expect(result.confidence).toBe(0.9)
      expect(result.characteristics.hasWordsArray).toBe(true)
      expect(result.characteristics.utteranceCount).toBe(2)
      expect(result.characteristics.timeUnit).toBe('seconds')
      expect(result.characteristics.averageTextLength).toBeCloseTo(10.5) // (8 + 13) / 2
    })

    it('应该正确检测句子级别数据（基于文本长度）', () => {
      const utterances = [
        {
          start_time: 1.5,
          end_time: 3.2,
          text: '这是一个相对较长的句子，应该被识别为句子级别的数据',
        },
        {
          start_time: 4.0,
          end_time: 6.5,
          text: '另一个比较长的句子，用来测试句子级别检测功能',
        },
      ]

      const result = detectRecognitionDataType(utterances)

      expect(result.type).toBe('sentence_level')
      expect(result.confidence).toBe(0.7)
      expect(result.characteristics.hasWordsArray).toBe(false)
      expect(result.characteristics.averageTextLength).toBeGreaterThan(20)
    })

    it('应该正确检测单词级别数据', () => {
      const utterances = [
        { start_time: 1.0, end_time: 1.5, text: '这是' },
        { start_time: 1.5, end_time: 2.0, text: '一个' },
        { start_time: 2.0, end_time: 2.5, text: '单词' },
        { start_time: 2.5, end_time: 3.0, text: '级别' },
        { start_time: 3.0, end_time: 3.5, text: '的' },
        { start_time: 3.5, end_time: 4.0, text: '数据' },
      ]

      const result = detectRecognitionDataType(utterances)

      expect(result.type).toBe('word_level')
      expect(result.confidence).toBe(0.8)
      expect(result.characteristics.hasWordsArray).toBe(false)
      expect(result.characteristics.averageTextLength).toBeLessThan(10)
      expect(result.characteristics.utteranceCount).toBe(6)
    })

    it('应该正确检测混合级别数据', () => {
      const utterances = [
        { start_time: 1.0, end_time: 1.5, text: '中等长度的文本内容，刚好在判断边界' },
        { start_time: 2.0, end_time: 2.5, text: '另一段中等文本，用于测试混合模式' },
        { start_time: 3.0, end_time: 3.5, text: '第三段中等内容，继续测试边界情况' },
      ]

      const result = detectRecognitionDataType(utterances)

      expect(result.type).toBe('mixed_level')
      expect(result.confidence).toBe(0.5)
      expect(result.characteristics.averageTextLength).toBeGreaterThanOrEqual(10)
      expect(result.characteristics.averageTextLength).toBeLessThanOrEqual(20)
    })

    it('应该正确检测毫秒时间单位', () => {
      const utterances = [
        { start_time: 1500, end_time: 3200, text: '毫秒时间戳' },
        { start_time: 4000, end_time: 6500, text: '另一个毫秒时间戳' },
      ]

      const result = detectRecognitionDataType(utterances)

      expect(result.characteristics.timeUnit).toBe('milliseconds')
    })

    it('应该正确检测秒时间单位', () => {
      const utterances = [
        { start_time: 1.5, end_time: 3.2, text: '秒时间戳' },
        { start_time: 4.0, end_time: 6.5, text: '另一个秒时间戳' },
      ]

      const result = detectRecognitionDataType(utterances)

      expect(result.characteristics.timeUnit).toBe('seconds')
    })

    it('应该处理缺少时间戳的数据', () => {
      const utterances = [{ text: '没有时间戳的文本' }, { start_time: 0, text: '有开始时间但没有结束时间' }]

      const result = detectRecognitionDataType(utterances)

      expect(result.characteristics.timeUnit).toBe('seconds')
      expect(result.characteristics.utteranceCount).toBe(2)
    })

    it('应该处理缺少文本的数据', () => {
      const utterances = [
        { start_time: 1.0, end_time: 2.0 },
        { start_time: 2.0, end_time: 3.0, text: '' },
        { start_time: 3.0, end_time: 4.0, text: '有效文本' },
      ]

      const result = detectRecognitionDataType(utterances)

      expect(result.characteristics.averageTextLength).toBeCloseTo(1.33) // (0 + 0 + 4) / 3
    })
  })

  describe('convertTimeUnit', () => {
    it('应该正确从毫秒转换为秒', () => {
      expect(convertTimeUnit(1000, 'milliseconds', 'seconds')).toBe(1)
      expect(convertTimeUnit(1500, 'milliseconds', 'seconds')).toBe(1.5)
      expect(convertTimeUnit(500, 'milliseconds', 'seconds')).toBe(0.5)
      expect(convertTimeUnit(0, 'milliseconds', 'seconds')).toBe(0)
    })

    it('应该正确从秒转换为毫秒', () => {
      expect(convertTimeUnit(1, 'seconds', 'milliseconds')).toBe(1000)
      expect(convertTimeUnit(1.5, 'seconds', 'milliseconds')).toBe(1500)
      expect(convertTimeUnit(0.5, 'seconds', 'milliseconds')).toBe(500)
      expect(convertTimeUnit(0, 'seconds', 'milliseconds')).toBe(0)
    })

    it('相同单位转换应该返回原值', () => {
      expect(convertTimeUnit(10, 'seconds', 'seconds')).toBe(10)
      expect(convertTimeUnit(1000, 'milliseconds', 'milliseconds')).toBe(1000)
      expect(convertTimeUnit(0, 'seconds', 'seconds')).toBe(0)
    })

    it('应该处理小数精度', () => {
      expect(convertTimeUnit(1234, 'milliseconds', 'seconds')).toBe(1.234)
      expect(convertTimeUnit(1.234, 'seconds', 'milliseconds')).toBe(1234)
    })

    it('应该处理负数', () => {
      expect(convertTimeUnit(-1000, 'milliseconds', 'seconds')).toBe(-1)
      expect(convertTimeUnit(-1, 'seconds', 'milliseconds')).toBe(-1000)
    })

    it('应该处理非常大的数值', () => {
      const largeMillis = 999999999
      const expectedSeconds = 999999.999
      expect(convertTimeUnit(largeMillis, 'milliseconds', 'seconds')).toBe(expectedSeconds)
      expect(convertTimeUnit(expectedSeconds, 'seconds', 'milliseconds')).toBe(largeMillis)
    })
  })
})

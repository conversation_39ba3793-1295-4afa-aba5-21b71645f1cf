/**
 * 音频识别数据智能预处理器
 * 根据数据类型自动选择合适的处理策略
 */

import type { RecognitionResult } from '~/composables/api/useSpeechRecognition'
import type { TimestampData } from '~/utils/processing/time/timestampTypes'
import type { SentenceSegmentationConfig } from '../text/sentenceProcessor'
import { detectRecognitionDataType, convertTimeUnit, type RecognitionDataType } from './dataTypeDetector'
import { smartSentenceSegmentation } from '../text/sentenceProcessor'

export interface ProcessingResult {
  data: TimestampData[]
  metadata: {
    originalDataType: RecognitionDataType
    processingStrategy: string
    timeUnit: 'seconds' | 'milliseconds'
    itemCount: number
    confidence: number
  }
}

/**
 * 智能预处理音频识别数据
 * @param recognitionResult 音频识别结果
 * @param segmentationConfig 句子分割配置（仅在需要时使用）
 * @returns 处理结果
 */
export function processRecognitionData(
  recognitionResult: RecognitionResult,
  segmentationConfig?: Partial<SentenceSegmentationConfig>,
): ProcessingResult {
  if (!recognitionResult?.utterances || recognitionResult.utterances.length === 0) {
    return {
      data: [],
      metadata: {
        originalDataType: 'word_level',
        processingStrategy: 'empty_data',
        timeUnit: 'seconds',
        itemCount: 0,
        confidence: 0,
      },
    }
  }

  // 检测数据类型
  const detection = detectRecognitionDataType(recognitionResult.utterances)

  console.log('🔍 数据类型检测结果:', {
    类型: detection.type,
    置信度: detection.confidence,
    特征: detection.characteristics,
  })

  let processedData: TimestampData[]
  let processingStrategy: string

  switch (detection.type) {
    case 'sentence_level':
      // 句子级别数据，直接转换
      processedData = processSentenceLevelData(recognitionResult.utterances, detection.characteristics.timeUnit)
      processingStrategy = 'direct_sentence_conversion'
      break

    case 'word_level':
      // 单词级别数据，需要进行句子分割
      processedData = processWordLevelData(recognitionResult.utterances, segmentationConfig, detection.characteristics.timeUnit)
      processingStrategy = 'word_to_sentence_segmentation'
      break

    case 'mixed_level':
    default:
      // 混合或不确定，使用保守策略
      if (detection.characteristics.hasWordsArray) {
        // 有words数组，尝试句子级处理
        processedData = processSentenceLevelData(recognitionResult.utterances, detection.characteristics.timeUnit)
        processingStrategy = 'conservative_sentence_conversion'
      } else {
        // 没有words数组，使用单词级处理
        processedData = processWordLevelData(recognitionResult.utterances, segmentationConfig, detection.characteristics.timeUnit)
        processingStrategy = 'conservative_word_segmentation'
      }
      break
  }

  return {
    data: processedData,
    metadata: {
      originalDataType: detection.type,
      processingStrategy,
      timeUnit: detection.characteristics.timeUnit,
      itemCount: processedData.length,
      confidence: detection.confidence,
    },
  }
}

interface UtteranceInput {
  start_time?: number
  end_time?: number
  text?: string
  attribute?: {
    speaker?: string
  }
}

/**
 * 处理句子级别数据
 */
function processSentenceLevelData(utterances: UtteranceInput[], timeUnit: 'seconds' | 'milliseconds'): TimestampData[] {
  return utterances.map((utterance, index) => ({
    id: index,
    text: utterance.text || '',
    start: convertTimeUnit(utterance.start_time || 0, timeUnit, 'seconds'),
    end: convertTimeUnit(utterance.end_time || 0, timeUnit, 'seconds'),
    speaker: utterance.attribute?.speaker,
  }))
}

interface WordData {
  start_time?: number
  end_time?: number
  text?: string
  attribute?: {
    speaker?: string
  }
}

interface UtteranceWithWords extends UtteranceInput {
  words?: WordData[]
}

/**
 * 处理单词级别数据
 */
function processWordLevelData(
  utterances: UtteranceInput[],
  segmentationConfig?: Partial<SentenceSegmentationConfig>,
  timeUnit: 'seconds' | 'milliseconds' = 'seconds',
): TimestampData[] {
  // 如果时间单位是毫秒，需要先转换
  const convertedUtterances = utterances.map((utterance) => ({
    start_time: convertTimeUnit(utterance.start_time || 0, timeUnit, 'seconds'),
    end_time: convertTimeUnit(utterance.end_time || 0, timeUnit, 'seconds'),
    text: utterance.text || '', // 确保text是必需的字符串
    attribute: utterance.attribute,
  }))

  return smartSentenceSegmentation(convertedUtterances, segmentationConfig)
}

/**
 * 从句子级数据中提取单词级数据（如果可用）
 */
export function extractWordLevelData(
  utterances: UtteranceWithWords[],
  timeUnit: 'seconds' | 'milliseconds' = 'milliseconds',
): TimestampData[] {
  const wordData: TimestampData[] = []
  let wordId = 0

  for (const utterance of utterances) {
    if (utterance.words && Array.isArray(utterance.words)) {
      // 过滤掉空格等非实际单词
      const actualWords = utterance.words.filter((word: WordData) => word.text && word.text.trim() && word.text.trim() !== ' ')

      for (const word of actualWords) {
        wordData.push({
          id: wordId++,
          text: word.text?.trim() || '',
          start: convertTimeUnit(word.start_time || 0, timeUnit, 'seconds'),
          end: convertTimeUnit(word.end_time || 0, timeUnit, 'seconds'),
          speaker: word.attribute?.speaker || utterance.attribute?.speaker,
        })
      }
    }
  }

  return wordData
}

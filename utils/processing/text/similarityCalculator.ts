/**
 * 文本相似度计算模块
 */

/**
 * 相似度计算算法类型
 */
export enum SimilarityAlgorithm {
  LEVENSHTEIN = 'levenshtein',
  JACCARD = 'jaccard',
}

/**
 * 相似度计算配置
 */
export interface SimilarityConfig {
  algorithm: SimilarityAlgorithm
  caseSensitive?: boolean
  removePunctuation?: boolean
  normalizeWhitespace?: boolean
}

/**
 * 默认相似度配置
 */
const DEFAULT_CONFIG: SimilarityConfig = {
  algorithm: SimilarityAlgorithm.LEVENSHTEIN,
  caseSensitive: false,
  removePunctuation: true,
  normalizeWhitespace: true,
}

/**
 * 文本预处理函数
 */
export function preprocessText(text: string, config: Partial<SimilarityConfig>): string {
  let processed = text

  if (config.normalizeWhitespace) {
    processed = processed.replace(/\s+/g, ' ').trim()
  }

  if (config.removePunctuation) {
    processed = processed.replace(/[.,!?;:()[\]'"""''。，！？；：（）【】''""]/g, '')
  }

  if (!config.caseSensitive) {
    processed = processed.toLowerCase()
  }

  return processed
}

/**
 * 计算两个文本的相似度
 * @param text1 第一个文本
 * @param text2 第二个文本
 * @param options 计算选项（保持向后兼容）
 * @returns 相似度分数 (0-1)
 */
export function calculateSimilarity(
  text1: string,
  text2: string,
  options: {
    caseSensitive?: boolean
    algorithm?: SimilarityAlgorithm
    removePunctuation?: boolean
    normalizeWhitespace?: boolean
  } = {},
): number {
  // 合并默认配置
  const config = { ...DEFAULT_CONFIG, ...options }

  // 预处理文本
  const processedText1 = preprocessText(text1, config)
  const processedText2 = preprocessText(text2, config)

  // 根据算法选择计算方法
  switch (config.algorithm) {
    case SimilarityAlgorithm.JACCARD: {
      return calculateJaccard(processedText1, processedText2)
    }
    case SimilarityAlgorithm.LEVENSHTEIN:
    default: {
      // 使用现有的编辑距离算法
      if (processedText1 === processedText2) return 1
      if (processedText1.length === 0 || processedText2.length === 0) return 0

      const distance = levenshteinDistance(processedText1, processedText2)
      const maxLength = Math.max(processedText1.length, processedText2.length)
      return 1 - distance / maxLength
    }
  }
}

/**
 * 文本标准化
 */
export function normalizeText(text: string): string {
  return text
    .toLowerCase()
    .trim()
    .replace(/\s+/g, ' ')
    .replace(/[^\w\s\u4e00-\u9fff]/g, '')
}

/**
 * 计算编辑距离
 */
export function levenshteinDistance(str1: string, str2: string): number {
  const m = str1.length
  const n = str2.length
  const dp: number[][] = Array(m + 1)
    .fill(null)
    .map(() => Array(n + 1).fill(0))

  for (let i = 0; i <= m; i++) dp[i][0] = i
  for (let j = 0; j <= n; j++) dp[0][j] = j

  for (let i = 1; i <= m; i++) {
    for (let j = 1; j <= n; j++) {
      if (str1[i - 1] === str2[j - 1]) {
        dp[i][j] = dp[i - 1][j - 1]
      } else {
        dp[i][j] = Math.min(dp[i - 1][j] + 1, dp[i][j - 1] + 1, dp[i - 1][j - 1] + 1)
      }
    }
  }

  return dp[m][n]
}

/**
 * 从候选项中找到最佳匹配
 * @param sourceText 源文本
 * @param candidates 候选项数组
 * @returns 最佳匹配结果
 */
export function findBestMatch(
  sourceText: string,
  candidates: Array<{ index: number; text: string }>,
): { index: number; similarity: number } {
  if (candidates.length === 0) {
    throw new Error('候选项列表不能为空')
  }

  const normalizedSource = normalizeText(sourceText)
  let bestMatch = candidates[0]
  let bestSimilarity = calculateSimilarity(normalizedSource, normalizeText(bestMatch.text))

  for (let i = 1; i < candidates.length; i++) {
    const candidate = candidates[i]
    const similarity = calculateSimilarity(normalizedSource, normalizeText(candidate.text))

    if (similarity > bestSimilarity) {
      bestMatch = candidate
      bestSimilarity = similarity
    }
  }

  return {
    index: bestMatch.index,
    similarity: bestSimilarity,
  }
}

/**
 * 批量计算相似度
 * @param sourceTexts 源文本数组
 * @param targetTexts 目标文本数组
 * @returns 相似度矩阵
 */
export function calculateSimilarityMatrix(sourceTexts: string[], targetTexts: string[]): number[][] {
  const matrix: number[][] = []

  for (let i = 0; i < sourceTexts.length; i++) {
    matrix[i] = []
    const normalizedSource = normalizeText(sourceTexts[i])

    for (let j = 0; j < targetTexts.length; j++) {
      const normalizedTarget = normalizeText(targetTexts[j])
      matrix[i][j] = calculateSimilarity(normalizedSource, normalizedTarget)
    }
  }

  return matrix
}

/**
 * 计算Jaccard相似度（来自TimestampAlignment服务）
 */
export function calculateJaccard(text1: string, text2: string): number {
  const words1 = new Set(text1.split(/\s+/))
  const words2 = new Set(text2.split(/\s+/))

  const intersection = new Set([...words1].filter((x) => words2.has(x)))
  const union = new Set([...words1, ...words2])

  return union.size === 0 ? 0 : intersection.size / union.size
}

/**
 * 使用配置对象计算文本相似度（新API）
 * @param text1 第一个文本
 * @param text2 第二个文本
 * @param config 相似度配置
 * @returns 相似度分数 (0-1)
 */
export function calculateSimilarityWithConfig(text1: string, text2: string, config: SimilarityConfig = DEFAULT_CONFIG): number {
  return calculateSimilarity(text1, text2, config)
}

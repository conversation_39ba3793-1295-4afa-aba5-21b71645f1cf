import { forEach } from 'lodash-es'

/**
 * 按字符数分割文本
 * @param sentences 文本
 * @param chunkSize 每个块的最大字符数
 * @param maxI 每个块的最大句子数
 * @returns 分割后的文本
 */
export function splitChunksByChars(sentences: string[], chunkSize: number = 600, maxI: number = 10): string[] {
  const chunks: string[] = []
  let chunk = ''
  let sentenceCount = 0
  for (const sentence of sentences) {
    if (chunk.length + sentence.length + 1 > chunkSize || sentenceCount === maxI) {
      chunks.push(chunk.trim())
      chunk = sentence + '\n'
      sentenceCount = 1
    } else {
      chunk += sentence + '\n'
      sentenceCount++
    }
  }
  if (chunk.trim().length > 0) {
    chunks.push(chunk.trim())
  }
  return chunks
}

/**
 * 基于标点符号进行语义分割
 * @param sentences 原始句子数组
 * @returns 经过语义分割的句子数组
 */
export function splitByMeaning(sentences: string[]): string[] {
  const result: string[] = []

  forEach(sentences, (sentence) => {
    // 基于中文和英文标点符号进行分割
    const parts = sentence.split(/[.!?。！？；;]\s*/).filter((part) => part.trim())

    forEach(parts, (part) => {
      if (part.trim()) {
        result.push(part.trim())
      }
    })
  })

  return result
}

/**
 * 获取前一块的最后几行内容（上下文信息）
 * @param chunks 所有分块
 * @param chunkIndex 当前分块索引
 * @param lineCount 获取的行数，默认3行
 * @returns 前文内容数组，如果是第一块则返回空数组
 */
export function getPreviousContent(chunks: string[], chunkIndex: number, lineCount: number = 3): string[] {
  if (chunkIndex === 0) {
    return []
  }

  const previousChunk = chunks[chunkIndex - 1]
  const lines = previousChunk.split('\n').filter((line) => line.trim())

  // 返回最后lineCount行
  return lines.slice(-lineCount)
}

/**
 * 获取后一块的前几行内容（上下文信息）
 * @param chunks 所有分块
 * @param chunkIndex 当前分块索引
 * @param lineCount 获取的行数，默认2行
 * @returns 后文内容数组，如果是最后一块则返回空数组
 */
export function getAfterContent(chunks: string[], chunkIndex: number, lineCount: number = 2): string[] {
  if (chunkIndex === chunks.length - 1) {
    return []
  }

  const nextChunk = chunks[chunkIndex + 1]
  const lines = nextChunk.split('\n').filter((line) => line.trim())

  // 返回前lineCount行
  return lines.slice(0, lineCount)
} 
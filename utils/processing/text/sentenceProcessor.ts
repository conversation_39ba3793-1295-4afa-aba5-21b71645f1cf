/**
 * 句子处理模块
 */

import type { RecognitionUtterance } from '~/composables/api/useSpeechRecognition'
import type { TimestampData } from '~/utils/processing/time/timestampTypes'

/**
 * 句子分割配置
 */
export interface SentenceSegmentationConfig {
  /** 最大句子长度（字符数） */
  maxSentenceLength: number
  /** 最小句子长度（字符数） */
  minSentenceLength: number
  /** 单词间最大停顿时间（秒），超过则认为是句子边界 */
  maxPauseTime: number
  /** 是否使用标点符号分割 */
  usePunctuation: boolean
  /** 是否使用时间间隔分割 */
  useTimeGaps: boolean
  /** 是否使用长度限制分割 */
  useLengthLimit: boolean
}

/**
 * 默认句子分割配置（来自sentenceSegmentation.ts）
 */
const DEFAULT_CONFIG: SentenceSegmentationConfig = {
  maxSentenceLength: 150,
  minSentenceLength: 10,
  maxPauseTime: 1.0,
  usePunctuation: true,
  useTimeGaps: true,
  useLengthLimit: true,
}

/**
 * 句子结束标点符号
 */
const SENTENCE_END_PUNCTUATION = /[.!?。！？]/

/**
 * 数据分析结果（来自sentenceSegmentation.ts）
 */
interface UtteranceAnalysis {
  totalWords: number
  averageWordLength: number
  averagePauseTime: number
  hasPunctuation: boolean
  averageWordsPerSecond: number
}

/**
 * 将单词级utterances转换为句子级TimestampData
 */
export function convertWordLevelToSentenceLevel(
  utterances: RecognitionUtterance[],
  config: Partial<SentenceSegmentationConfig> = {},
): TimestampData[] {
  if (!utterances || utterances.length === 0) {
    return []
  }

  const finalConfig = { ...DEFAULT_CONFIG, ...config }
  const sentences: TimestampData[] = []
  let currentSentence: RecognitionUtterance[] = []
  let sentenceId = 0

  for (let i = 0; i < utterances.length; i++) {
    const currentUtterance = utterances[i]
    const nextUtterance = utterances[i + 1]

    currentSentence.push(currentUtterance)

    // 检查是否应该结束当前句子
    const shouldEndSentence = checkSentenceEnd(currentUtterance, nextUtterance, currentSentence, finalConfig)

    if (shouldEndSentence || i === utterances.length - 1) {
      // 创建句子级时间戳数据
      const sentenceData = createSentenceFromWords(currentSentence, sentenceId)
      if (sentenceData) {
        sentences.push(sentenceData)
        sentenceId++
      }
      currentSentence = []
    }
  }

  return sentences
}

/**
 * 智能句子分割，结合多种策略（来自sentenceSegmentation.ts）
 */
export function smartSentenceSegmentation(
  utterances: RecognitionUtterance[],
  customConfig?: Partial<SentenceSegmentationConfig>,
): TimestampData[] {
  // 分析数据特征，自动调整配置
  const analysisResult = analyzeUtteranceData(utterances)
  const adaptiveConfig = adaptConfigBasedOnAnalysis(analysisResult, customConfig)

  return convertWordLevelToSentenceLevel(utterances, adaptiveConfig)
}

/**
 * 验证分割结果（来自sentenceSegmentation.ts）
 */
export function validateSegmentationResult(
  original: RecognitionUtterance[],
  sentences: TimestampData[],
): {
  isValid: boolean
  warnings: string[]
  statistics: {
    originalWords: number
    sentenceCount: number
    averageSentenceLength: number
    timeRangeCoverage: number
  }
} {
  const warnings: string[] = []

  // 基本统计
  const originalWords = original.length
  const sentenceCount = sentences.length
  const averageSentenceLength = sentences.reduce((sum, s) => sum + s.text.length, 0) / sentenceCount

  // 时间覆盖率
  const originalTimeRange = original.length > 0 ? original[original.length - 1].end_time - original[0].start_time : 0
  const sentenceTimeRange = sentences.length > 0 ? (sentences[sentences.length - 1].end as number) - (sentences[0].start as number) : 0
  const timeRangeCoverage = originalTimeRange > 0 ? sentenceTimeRange / originalTimeRange : 0

  // 验证检查
  if (sentenceCount === 0) {
    warnings.push('没有生成任何句子')
  }

  if (averageSentenceLength < 10) {
    warnings.push('平均句子长度过短')
  }

  if (timeRangeCoverage < 0.9) {
    warnings.push('时间覆盖率不足')
  }

  return {
    isValid: warnings.length === 0,
    warnings,
    statistics: {
      originalWords,
      sentenceCount,
      averageSentenceLength,
      timeRangeCoverage,
    },
  }
}

/**
 * 检查是否应该结束当前句子
 */
export function checkSentenceEnd(
  currentUtterance: RecognitionUtterance,
  nextUtterance: RecognitionUtterance | undefined,
  currentSentence: RecognitionUtterance[],
  config: SentenceSegmentationConfig,
): boolean {
  // 1. 标点符号检查
  if (config.usePunctuation && SENTENCE_END_PUNCTUATION.test(currentUtterance.text)) {
    return true
  }

  // 2. 时间间隔检查
  if (config.useTimeGaps && nextUtterance) {
    const pauseTime = nextUtterance.start_time - currentUtterance.end_time
    if (pauseTime > config.maxPauseTime) {
      return true
    }
  }

  // 3. 长度限制检查
  if (config.useLengthLimit) {
    const currentLength = currentSentence.map((u) => u.text).join(' ').length
    if (currentLength > config.maxSentenceLength) {
      return true
    }
  }

  return false
}

/**
 * 从单词列表创建句子级时间戳数据
 */
export function createSentenceFromWords(words: RecognitionUtterance[], id: number): TimestampData | null {
  if (words.length === 0) {
    return null
  }

  const text = words
    .map((w) => w.text)
    .join(' ')
    .trim()
  const startTime = words[0].start_time
  const endTime = words[words.length - 1].end_time

  // 获取说话人信息（使用第一个单词的说话人）
  const speaker = words[0].attribute?.speaker

  return {
    id,
    text,
    start: startTime,
    end: endTime,
    speaker,
  }
}

/**
 * 分析utterance数据特征（来自sentenceSegmentation.ts）
 */
export function analyzeUtteranceData(utterances: RecognitionUtterance[]): UtteranceAnalysis {
  if (utterances.length === 0) {
    return {
      totalWords: 0,
      averageWordLength: 0,
      averagePauseTime: 0,
      hasPunctuation: false,
      averageWordsPerSecond: 0,
    }
  }

  const totalWords = utterances.length
  const totalTextLength = utterances.reduce((sum, u) => sum + u.text.length, 0)
  const averageWordLength = totalTextLength / totalWords

  // 计算平均停顿时间
  let totalPauseTime = 0
  let pauseCount = 0
  for (let i = 0; i < utterances.length - 1; i++) {
    const pause = utterances[i + 1].start_time - utterances[i].end_time
    if (pause > 0) {
      totalPauseTime += pause
      pauseCount++
    }
  }
  const averagePauseTime = pauseCount > 0 ? totalPauseTime / pauseCount : 0

  // 检查是否有标点符号
  const hasPunctuation = utterances.some((u) => SENTENCE_END_PUNCTUATION.test(u.text))

  // 计算语速（单词/秒）
  const totalDuration = utterances[utterances.length - 1].end_time - utterances[0].start_time
  const averageWordsPerSecond = totalDuration > 0 ? totalWords / totalDuration : 0

  return {
    totalWords,
    averageWordLength,
    averagePauseTime,
    hasPunctuation,
    averageWordsPerSecond,
  }
}

/**
 * 基于分析结果调整配置（来自sentenceSegmentation.ts）
 */
export function adaptConfigBasedOnAnalysis(
  analysis: UtteranceAnalysis,
  customConfig?: Partial<SentenceSegmentationConfig>,
): SentenceSegmentationConfig {
  let adaptiveConfig = { ...DEFAULT_CONFIG }

  // 基于数据特征调整配置
  if (analysis.averageWordsPerSecond > 2) {
    // 语速较快，增加停顿时间阈值
    adaptiveConfig.maxPauseTime *= 1.5
  }

  if (!analysis.hasPunctuation) {
    // 没有标点符号，更依赖时间间隔
    adaptiveConfig.usePunctuation = false
    adaptiveConfig.maxPauseTime *= 0.8
  }

  if (analysis.averageWordLength < 3) {
    // 短单词较多，可能需要更长的句子
    adaptiveConfig.maxSentenceLength *= 1.2
  }

  // 应用用户自定义配置
  if (customConfig) {
    adaptiveConfig = { ...adaptiveConfig, ...customConfig }
  }

  return adaptiveConfig
}

import { forEach } from 'lodash-es'
import type { RecognitionResult } from '~/composables/api/useSpeechRecognition'
import { SUMMARY_LENGTH } from '~/common/constants'

/**
 * 检测文本语言类型
 * 基于英文字符比例进行判断：英文字符比例≥50%为英文，否则为中文
 */
export function detectTextLanguage(text: string): 'chinese' | 'english' {
  const englishChars = (text.match(/[a-zA-Z]/g) || []).length
  const chineseChars = (text.match(/[\u4e00-\u9fff\u3400-\u4dbf\u3000-\u303f]/g) || []).length

  const totalLetterChars = englishChars + chineseChars

  // 如果没有字母字符，默认为中文
  if (totalLetterChars === 0) {
    return 'chinese'
  }

  // 计算英文字符比例
  const englishRatio = englishChars / totalLetterChars

  // 英文字符比例≥50%判断为英文，否则为中文
  return englishRatio >= 0.5 ? 'english' : 'chinese'
}

/**
 * 分类字幕行文本
 */
export function classifySubtitleLines(lines: string[]): { text: string; translationText: string } {
  const textLines: string[] = []
  const translationLines: string[] = []

  forEach(lines, (line) => {
    const language = detectTextLanguage(line)
    if (language === 'chinese') {
      translationLines.push(line)
    } else {
      // english 放入 text
      textLines.push(line)
    }
  })

  return {
    text: textLines.join('\n'),
    translationText: translationLines.join('\n'),
  }
}

/**
 * 合并识别结果中的文本片段
 * @param recognitionResult 火山引擎识别结果
 * @returns 合并后的文本，最大长度为SUMMARY_LENGTH
 */
export function mergeRecognitionText(recognitionResult: RecognitionResult): string {
  if (!recognitionResult?.utterances || recognitionResult.utterances.length === 0) {
    return ''
  }

  // 将所有句子用空格拼接成一个长字符串
  const mergedText = recognitionResult.utterances.map((utterance) => utterance.text).join(' ')

  // 返回前SUMMARY_LENGTH个字符
  return mergedText.substring(0, SUMMARY_LENGTH)
}

/**
 * 将识别结果转换为句子数组
 * @param recognitionResult 火山引擎识别结果
 * @returns 句子数组
 */
export function mergeRecognitionToSentences(recognitionResult: RecognitionResult): string[] {
  if (!recognitionResult?.utterances || recognitionResult.utterances.length === 0) {
    return []
  }

  // 将所有识别片段的文本提取出来
  const sentences: string[] = []
  forEach(recognitionResult.utterances, (utterance) => {
    if (utterance.text.trim()) {
      sentences.push(utterance.text.trim())
    }
  })

  return sentences
} 
import { describe, it, expect } from 'vitest'
import { detectTextLanguage, classifySubtitleLines, mergeRecognitionText, mergeRecognitionToSentences } from '../textProcessor'
import type { RecognitionResult } from '~/composables/api/useSpeechRecognition'
import { SUMMARY_LENGTH } from '~/common/constants'

describe('textProcessor', () => {
  describe('detectTextLanguage', () => {
    it('应该检测纯英文文本', () => {
      const result = detectTextLanguage('Hello world this is a test')
      expect(result).toBe('english')
    })

    it('应该检测纯中文文本', () => {
      const result = detectTextLanguage('你好世界这是一个测试')
      expect(result).toBe('chinese')
    })

    it('应该检测混合文本中英文字符占优势的情况', () => {
      const result = detectTextLanguage('Hello 世界 this is a test 测试')
      expect(result).toBe('english')
    })

    it('应该检测混合文本中中文字符占优势的情况', () => {
      const result = detectTextLanguage('你好世界这是一个测试非常长的中文 with English')
      expect(result).toBe('chinese')
    })

    it('应该处理50%英文比例的边界情况', () => {
      const result = detectTextLanguage('Hello你好world世界')
      expect(result).toBe('english') // 50%时返回英文
    })

    it('应该处理只有标点符号的文本', () => {
      const result = detectTextLanguage('!@#$%^&*()')
      expect(result).toBe('chinese') // 默认返回中文
    })

    it('应该处理空字符串', () => {
      const result = detectTextLanguage('')
      expect(result).toBe('chinese') // 默认返回中文
    })

    it('应该处理只有数字的文本', () => {
      const result = detectTextLanguage('123456789')
      expect(result).toBe('chinese') // 默认返回中文
    })

    it('应该处理中文标点符号', () => {
      const result = detectTextLanguage('你好，世界！这是测试。')
      expect(result).toBe('chinese')
    })

    it('应该处理英文标点符号', () => {
      const result = detectTextLanguage('Hello, world! This is a test.')
      expect(result).toBe('english')
    })

    it('应该正确处理Unicode中文字符', () => {
      const result = detectTextLanguage('中国🇨🇳测试')
      expect(result).toBe('chinese')
    })

    it('应该处理只有空格的文本', () => {
      const result = detectTextLanguage('   ')
      expect(result).toBe('chinese') // 默认返回中文
    })
  })

  describe('classifySubtitleLines', () => {
    it('应该正确分类纯英文字幕行', () => {
      const lines = ['Hello world', 'This is a test', 'How are you']
      const result = classifySubtitleLines(lines)

      expect(result.text).toBe('Hello world\nThis is a test\nHow are you')
      expect(result.translationText).toBe('')
    })

    it('应该正确分类纯中文字幕行', () => {
      const lines = ['你好世界', '这是一个测试', '你好吗']
      const result = classifySubtitleLines(lines)

      expect(result.text).toBe('')
      expect(result.translationText).toBe('你好世界\n这是一个测试\n你好吗')
    })

    it('应该正确分类混合字幕行', () => {
      const lines = ['Hello world', '你好世界', 'This is a test', '这是一个测试']
      const result = classifySubtitleLines(lines)

      expect(result.text).toBe('Hello world\nThis is a test')
      expect(result.translationText).toBe('你好世界\n这是一个测试')
    })

    it('应该处理空数组', () => {
      const lines: string[] = []
      const result = classifySubtitleLines(lines)

      expect(result.text).toBe('')
      expect(result.translationText).toBe('')
    })

    it('应该处理包含空字符串的数组', () => {
      const lines = ['', 'Hello world', '', '你好世界', '']
      const result = classifySubtitleLines(lines)

      expect(result.text).toBe('Hello world')
      expect(result.translationText).toBe('\n\n你好世界\n')
    })

    it('应该处理混合语言的单行', () => {
      const lines = ['Hello 你好 world 世界']
      const result = classifySubtitleLines(lines)

      // 这行会被归类为英文，因为英文字符占50%
      expect(result.text).toBe('Hello 你好 world 世界')
      expect(result.translationText).toBe('')
    })

    it('应该处理中文字符占优势的混合行', () => {
      const lines = ['你好世界这是测试非常长的中文 with English']
      const result = classifySubtitleLines(lines)

      expect(result.text).toBe('')
      expect(result.translationText).toBe('你好世界这是测试非常长的中文 with English')
    })

    it('应该处理只有标点符号的行', () => {
      const lines = ['!@#$%', '...', '???']
      const result = classifySubtitleLines(lines)

      expect(result.text).toBe('')
      expect(result.translationText).toBe('!@#$%\n...\n???')
    })
  })

  describe('mergeRecognitionText', () => {
    it('应该合并识别结果的文本', () => {
      const recognitionResult: RecognitionResult = {
        code: 0,
        duration: 3000,
        utterances: [
          { text: 'Hello world', start_time: 0, end_time: 1000 },
          { text: 'This is a test', start_time: 1000, end_time: 2000 },
          { text: 'How are you', start_time: 2000, end_time: 3000 },
        ],
      }

      const result = mergeRecognitionText(recognitionResult)
      expect(result).toBe('Hello world This is a test How are you')
    })

    it('应该处理空的识别结果', () => {
      const recognitionResult: RecognitionResult = {
        code: 0,
        duration: 0,
        utterances: [],
      }

      const result = mergeRecognitionText(recognitionResult)
      expect(result).toBe('')
    })

    it('应该处理undefined的识别结果', () => {
      const result = mergeRecognitionText(undefined as unknown as RecognitionResult)
      expect(result).toBe('')
    })

    it('应该处理null的识别结果', () => {
      const result = mergeRecognitionText(null as unknown as RecognitionResult)
      expect(result).toBe('')
    })

    it('应该处理没有utterances字段的识别结果', () => {
      const recognitionResult = {} as RecognitionResult
      const result = mergeRecognitionText(recognitionResult)
      expect(result).toBe('')
    })

    it('应该截断超长文本到SUMMARY_LENGTH', () => {
      // 创建一个超长的文本
      const longText = 'A'.repeat(10000)
      const recognitionResult: RecognitionResult = {
        code: 0,
        duration: 1000,
        utterances: [{ text: longText, start_time: 0, end_time: 1000 }],
      }

      const result = mergeRecognitionText(recognitionResult)
      // SUMMARY_LENGTH 是 8000 字符
      expect(result.length).toBeLessThanOrEqual(SUMMARY_LENGTH)
      expect(result).toBe(longText.substring(0, SUMMARY_LENGTH))
    })

    it('应该处理包含空文本的utterances', () => {
      const recognitionResult: RecognitionResult = {
        code: 0,
        duration: 3000,
        utterances: [
          { text: 'Hello', start_time: 0, end_time: 1000 },
          { text: '', start_time: 1000, end_time: 2000 },
          { text: 'World', start_time: 2000, end_time: 3000 },
        ],
      }

      const result = mergeRecognitionText(recognitionResult)
      expect(result).toBe('Hello  World')
    })

    it('应该用空格连接文本片段', () => {
      const recognitionResult: RecognitionResult = {
        code: 0,
        duration: 3000,
        utterances: [
          { text: '第一句', start_time: 0, end_time: 1000 },
          { text: '第二句', start_time: 1000, end_time: 2000 },
          { text: '第三句', start_time: 2000, end_time: 3000 },
        ],
      }

      const result = mergeRecognitionText(recognitionResult)
      expect(result).toBe('第一句 第二句 第三句')
    })
  })

  describe('mergeRecognitionToSentences', () => {
    it('应该将识别结果转换为句子数组', () => {
      const recognitionResult: RecognitionResult = {
        code: 0,
        duration: 3000,
        utterances: [
          { text: 'Hello world', start_time: 0, end_time: 1000 },
          { text: 'This is a test', start_time: 1000, end_time: 2000 },
          { text: 'How are you', start_time: 2000, end_time: 3000 },
        ],
      }

      const result = mergeRecognitionToSentences(recognitionResult)
      expect(result).toEqual(['Hello world', 'This is a test', 'How are you'])
    })

    it('应该过滤空白文本', () => {
      const recognitionResult: RecognitionResult = {
        code: 0,
        duration: 3000,
        utterances: [
          { text: 'Hello world', start_time: 0, end_time: 1000 },
          { text: '   ', start_time: 1000, end_time: 2000 },
          { text: '', start_time: 1500, end_time: 2000 },
          { text: 'How are you', start_time: 2000, end_time: 3000 },
        ],
      }

      const result = mergeRecognitionToSentences(recognitionResult)
      expect(result).toEqual(['Hello world', 'How are you'])
    })

    it('应该处理空的识别结果', () => {
      const recognitionResult: RecognitionResult = {
        code: 0,
        duration: 0,
        utterances: [],
      }

      const result = mergeRecognitionToSentences(recognitionResult)
      expect(result).toEqual([])
    })

    it('应该处理undefined的识别结果', () => {
      const result = mergeRecognitionToSentences(undefined as unknown as RecognitionResult)
      expect(result).toEqual([])
    })

    it('应该处理null的识别结果', () => {
      const result = mergeRecognitionToSentences(null as unknown as RecognitionResult)
      expect(result).toEqual([])
    })

    it('应该处理没有utterances字段的识别结果', () => {
      const recognitionResult = {} as RecognitionResult
      const result = mergeRecognitionToSentences(recognitionResult)
      expect(result).toEqual([])
    })

    it('应该去除文本前后空白', () => {
      const recognitionResult: RecognitionResult = {
        code: 0,
        duration: 2000,
        utterances: [
          { text: '  Hello world  ', start_time: 0, end_time: 1000 },
          { text: '\n  This is a test  \t', start_time: 1000, end_time: 2000 },
        ],
      }

      const result = mergeRecognitionToSentences(recognitionResult)
      expect(result).toEqual(['Hello world', 'This is a test'])
    })

    it('应该处理中文文本', () => {
      const recognitionResult: RecognitionResult = {
        code: 0,
        duration: 3000,
        utterances: [
          { text: '你好世界', start_time: 0, end_time: 1000 },
          { text: '这是一个测试', start_time: 1000, end_time: 2000 },
          { text: '你好吗', start_time: 2000, end_time: 3000 },
        ],
      }

      const result = mergeRecognitionToSentences(recognitionResult)
      expect(result).toEqual(['你好世界', '这是一个测试', '你好吗'])
    })

    it('应该处理只包含空白字符的utterances', () => {
      const recognitionResult: RecognitionResult = {
        code: 0,
        duration: 3000,
        utterances: [
          { text: '   ', start_time: 0, end_time: 1000 },
          { text: '\n\t  ', start_time: 1000, end_time: 2000 },
          { text: '', start_time: 2000, end_time: 3000 },
        ],
      }

      const result = mergeRecognitionToSentences(recognitionResult)
      expect(result).toEqual([])
    })
  })

  describe('集成测试', () => {
    it('应该能够配合使用多个函数处理识别结果', () => {
      const recognitionResult: RecognitionResult = {
        code: 0,
        duration: 4000,
        utterances: [
          { text: 'Hello world', start_time: 0, end_time: 1000 },
          { text: '你好世界', start_time: 1000, end_time: 2000 },
          { text: 'This is a test', start_time: 2000, end_time: 3000 },
          { text: '这是一个测试', start_time: 3000, end_time: 4000 },
        ],
      }

      // 转换为句子数组
      const sentences = mergeRecognitionToSentences(recognitionResult)
      expect(sentences).toEqual(['Hello world', '你好世界', 'This is a test', '这是一个测试'])

      // 分类字幕行
      const classified = classifySubtitleLines(sentences)
      expect(classified.text).toBe('Hello world\nThis is a test')
      expect(classified.translationText).toBe('你好世界\n这是一个测试')

      // 合并文本
      const mergedText = mergeRecognitionText(recognitionResult)
      expect(mergedText).toBe('Hello world 你好世界 This is a test 这是一个测试')
    })

    it('应该处理复杂的真实场景', () => {
      const recognitionResult: RecognitionResult = {
        code: 0,
        duration: 10000,
        utterances: [
          { text: 'Welcome to our presentation', start_time: 0, end_time: 2000 },
          { text: '欢迎参加我们的演示', start_time: 2000, end_time: 4000 },
          { text: 'Today we will discuss AI technology', start_time: 4000, end_time: 7000 },
          { text: '今天我们将讨论人工智能技术', start_time: 7000, end_time: 9000 },
          { text: '', start_time: 9000, end_time: 9500 },
          { text: '   ', start_time: 9500, end_time: 10000 },
        ],
      }

      // 测试所有函数
      const sentences = mergeRecognitionToSentences(recognitionResult)
      expect(sentences).toHaveLength(4)

      const classified = classifySubtitleLines(sentences)
      expect(classified.text).toContain('Welcome to our presentation')
      expect(classified.text).toContain('Today we will discuss AI technology')
      expect(classified.translationText).toContain('欢迎参加我们的演示')
      expect(classified.translationText).toContain('今天我们将讨论人工智能技术')

      const mergedText = mergeRecognitionText(recognitionResult)
      expect(mergedText).toContain('Welcome to our presentation')
      expect(mergedText).toContain('欢迎参加我们的演示')

      // 测试语言检测
      sentences.forEach((sentence) => {
        const language = detectTextLanguage(sentence)
        expect(['chinese', 'english']).toContain(language)
      })
    })
  })
})

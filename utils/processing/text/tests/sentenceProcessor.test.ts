import { describe, it, expect, beforeEach } from 'vitest'
import {
  convertWordLevelToSentenceLevel,
  smartSentenceSegmentation,
  validateSegmentationResult,
  checkSentenceEnd,
  createSentenceFromWords,
  analyzeUtteranceData,
  adaptConfigBasedOnAnalysis,
  type SentenceSegmentationConfig,
} from '../sentenceProcessor'
import type { RecognitionUtterance } from '~/composables/api/useSpeechRecognition'
import type { TimestampData } from '~/utils/processing/time/timestampTypes'

describe('Sentence Processor', () => {
  // 测试用的模拟数据
  let mockUtterances: RecognitionUtterance[]
  let mockUtterancesWithPunctuation: RecognitionUtterance[]
  let mockUtterancesWithLongPauses: RecognitionUtterance[]
  let mockShortUtterances: RecognitionUtterance[]
  let mockLongUtterances: RecognitionUtterance[]

  beforeEach(() => {
    // 基础测试数据
    mockUtterances = [
      { start_time: 0.0, end_time: 0.5, text: '你好' },
      { start_time: 0.5, end_time: 1.0, text: '世界' },
      { start_time: 1.0, end_time: 1.5, text: '这是' },
      { start_time: 1.5, end_time: 2.0, text: '一个' },
      { start_time: 2.0, end_time: 2.5, text: '测试' },
    ]

    // 带标点符号的测试数据
    mockUtterancesWithPunctuation = [
      { start_time: 0.0, end_time: 1.0, text: '你好世界' },
      { start_time: 1.0, end_time: 2.0, text: '。' },
      { start_time: 2.0, end_time: 3.0, text: '这是' },
      { start_time: 3.0, end_time: 4.0, text: '测试' },
      { start_time: 4.0, end_time: 5.0, text: '！' },
    ]

    // 带长停顿的测试数据
    mockUtterancesWithLongPauses = [
      { start_time: 0.0, end_time: 0.5, text: '第一' },
      { start_time: 0.5, end_time: 1.0, text: '句子' },
      { start_time: 3.0, end_time: 3.5, text: '第二' }, // 2秒停顿
      { start_time: 3.5, end_time: 4.0, text: '句子' },
    ]

    // 短文本测试数据
    mockShortUtterances = [
      { start_time: 0.0, end_time: 0.5, text: 'Hi' },
      { start_time: 0.5, end_time: 1.0, text: 'OK' },
    ]

    // 长文本测试数据
    mockLongUtterances = [
      { start_time: 0.0, end_time: 1.0, text: '这是一个非常长的句子，' },
      { start_time: 1.0, end_time: 2.0, text: '包含了很多很多的文字内容，' },
      { start_time: 2.0, end_time: 3.0, text: '用来测试长度限制功能的正确性' },
      { start_time: 3.0, end_time: 4.0, text: '以及分割逻辑是否工作正常。' },
    ]
  })

  describe('convertWordLevelToSentenceLevel', () => {
    it('应该正确处理空输入', () => {
      const result = convertWordLevelToSentenceLevel([])
      expect(result).toEqual([])
    })

    it('应该正确处理 null 或 undefined 输入', () => {
      const result1 = convertWordLevelToSentenceLevel(null as unknown as RecognitionUtterance[])
      const result2 = convertWordLevelToSentenceLevel(undefined as unknown as RecognitionUtterance[])
      expect(result1).toEqual([])
      expect(result2).toEqual([])
    })

    it('应该将多个单词合并为一个句子', () => {
      const result = convertWordLevelToSentenceLevel(mockUtterances)

      expect(result).toHaveLength(1)
      expect(result[0]).toMatchObject({
        id: 0,
        text: '你好 世界 这是 一个 测试',
        start: 0.0,
        end: 2.5,
      })
    })

    it('应该根据标点符号分割句子', () => {
      const config: Partial<SentenceSegmentationConfig> = {
        usePunctuation: true,
        useTimeGaps: false,
        useLengthLimit: false,
      }

      const result = convertWordLevelToSentenceLevel(mockUtterancesWithPunctuation, config)

      expect(result).toHaveLength(2)
      expect(result[0].text).toBe('你好世界 。')
      expect(result[1].text).toBe('这是 测试 ！')
    })

    it('应该根据时间间隔分割句子', () => {
      const config: Partial<SentenceSegmentationConfig> = {
        usePunctuation: false,
        useTimeGaps: true,
        maxPauseTime: 1.5,
        useLengthLimit: false,
      }

      const result = convertWordLevelToSentenceLevel(mockUtterancesWithLongPauses, config)

      expect(result).toHaveLength(2)
      expect(result[0].text).toBe('第一 句子')
      expect(result[1].text).toBe('第二 句子')
    })

    it('应该根据长度限制分割句子', () => {
      const config: Partial<SentenceSegmentationConfig> = {
        usePunctuation: false,
        useTimeGaps: false,
        useLengthLimit: true,
        maxSentenceLength: 20,
      }

      const result = convertWordLevelToSentenceLevel(mockLongUtterances, config)

      expect(result.length).toBeGreaterThan(1)
      result.forEach((sentence) => {
        expect(sentence.text.length).toBeLessThanOrEqual(50) // 考虑中文字符和空格
      })
    })

    it('应该保留说话人信息', () => {
      const utterancesWithSpeaker: RecognitionUtterance[] = [
        { start_time: 0.0, end_time: 1.0, text: '你好', attribute: { speaker: 'speaker1' } },
        { start_time: 1.0, end_time: 2.0, text: '世界', attribute: { speaker: 'speaker1' } },
      ]

      const result = convertWordLevelToSentenceLevel(utterancesWithSpeaker)

      expect(result[0].speaker).toBe('speaker1')
    })

    it('应该正确设置句子ID', () => {
      const result = convertWordLevelToSentenceLevel(mockUtterancesWithPunctuation)

      result.forEach((sentence, index) => {
        expect(sentence.id).toBe(index)
      })
    })
  })

  describe('smartSentenceSegmentation', () => {
    it('应该自动分析数据并应用适当的配置', () => {
      const result = smartSentenceSegmentation(mockUtterances)

      expect(result).toHaveLength(1)
      expect(result[0].text).toContain('你好')
    })

    it('应该处理快语速数据', () => {
      // 创建快语速测试数据（高单词/秒比率）
      const fastSpeechUtterances: RecognitionUtterance[] = [
        { start_time: 0.0, end_time: 0.2, text: '快速' },
        { start_time: 0.2, end_time: 0.4, text: '说话' },
        { start_time: 0.4, end_time: 0.6, text: '测试' },
        { start_time: 0.6, end_time: 0.8, text: '数据' },
      ]

      const result = smartSentenceSegmentation(fastSpeechUtterances)
      expect(result).toBeDefined()
      expect(result.length).toBeGreaterThan(0)
    })

    it('应该处理无标点符号的数据', () => {
      const noPuncUtterances: RecognitionUtterance[] = [
        { start_time: 0.0, end_time: 1.0, text: '没有标点' },
        { start_time: 2.0, end_time: 3.0, text: '的文本' },
        { start_time: 4.0, end_time: 5.0, text: '数据' },
      ]

      const result = smartSentenceSegmentation(noPuncUtterances)
      expect(result).toBeDefined()
      expect(result.length).toBeGreaterThan(0)
    })

    it('应该应用用户自定义配置', () => {
      const customConfig: Partial<SentenceSegmentationConfig> = {
        maxSentenceLength: 10,
        useLengthLimit: true,
      }

      const result = smartSentenceSegmentation(mockLongUtterances, customConfig)

      // 由于长度限制，应该产生多个句子
      expect(result.length).toBeGreaterThan(1)
    })
  })

  describe('validateSegmentationResult', () => {
    let validSentences: TimestampData[]

    beforeEach(() => {
      validSentences = [
        { id: 0, text: '这是第一个比较长的句子用来测试', start: 0, end: 2 },
        { id: 1, text: '这是第二个比较长的句子用来测试', start: 2, end: 2.5 },
      ]
    })

    it('应该验证正常的分割结果', () => {
      const result = validateSegmentationResult(mockUtterances, validSentences)

      expect(result.isValid).toBe(true)
      expect(result.warnings).toHaveLength(0)
      expect(result.statistics.originalWords).toBe(mockUtterances.length)
      expect(result.statistics.sentenceCount).toBe(validSentences.length)
    })

    it('应该检测到空结果的问题', () => {
      const result = validateSegmentationResult(mockUtterances, [])

      expect(result.isValid).toBe(false)
      expect(result.warnings).toContain('没有生成任何句子')
    })

    it('应该检测到句子过短的问题', () => {
      const shortSentences: TimestampData[] = [
        { id: 0, text: '短', start: 0, end: 1 },
        { id: 1, text: '句', start: 1, end: 2 },
      ]

      const result = validateSegmentationResult(mockUtterances, shortSentences)

      expect(result.isValid).toBe(false)
      expect(result.warnings).toContain('平均句子长度过短')
    })

    it('应该计算正确的统计信息', () => {
      const result = validateSegmentationResult(mockUtterances, validSentences)

      expect(result.statistics.originalWords).toBe(5)
      expect(result.statistics.sentenceCount).toBe(2)
      expect(result.statistics.averageSentenceLength).toBeGreaterThan(0)
      expect(result.statistics.timeRangeCoverage).toBeCloseTo(1.0, 0)
    })

    it('应该检测时间覆盖率不足', () => {
      const incompleteSentences: TimestampData[] = [{ id: 0, text: '不完整的时间覆盖', start: 0, end: 1 }]

      const result = validateSegmentationResult(mockUtterances, incompleteSentences)

      expect(result.warnings).toContain('时间覆盖率不足')
    })

    it('应该处理空的原始数据', () => {
      const result = validateSegmentationResult([], validSentences)

      expect(result.statistics.originalWords).toBe(0)
      expect(result.statistics.timeRangeCoverage).toBe(0)
    })
  })

  describe('边界情况和错误处理', () => {
    it('应该处理单个单词的输入', () => {
      const singleWord: RecognitionUtterance[] = [{ start_time: 0.0, end_time: 1.0, text: '单词' }]

      const result = convertWordLevelToSentenceLevel(singleWord)

      expect(result).toHaveLength(1)
      expect(result[0].text).toBe('单词')
    })

    it('应该处理相同时间戳的输入', () => {
      const sameTimestamp: RecognitionUtterance[] = [
        { start_time: 1.0, end_time: 1.0, text: '同时' },
        { start_time: 1.0, end_time: 1.0, text: '开始' },
      ]

      const result = convertWordLevelToSentenceLevel(sameTimestamp)

      expect(result).toHaveLength(1)
      expect(result[0].text).toBe('同时 开始')
    })

    it('应该处理包含空白文本的输入', () => {
      const withEmptyText: RecognitionUtterance[] = [
        { start_time: 0.0, end_time: 1.0, text: '正常文本' },
        { start_time: 1.0, end_time: 2.0, text: '   ' },
        { start_time: 2.0, end_time: 3.0, text: '另一个文本' },
      ]

      const result = convertWordLevelToSentenceLevel(withEmptyText)

      expect(result).toHaveLength(1)
      expect(result[0].text.trim()).toBe('正常文本     另一个文本')
    })

    it('应该处理极长的文本', () => {
      // 创建多个短单词组成长文本，这样可以在单词边界分割
      const longTextUtterances: RecognitionUtterance[] = [
        { start_time: 0.0, end_time: 0.2, text: 'a' },
        { start_time: 0.2, end_time: 0.4, text: 'b' },
        { start_time: 0.4, end_time: 0.6, text: 'c' },
        { start_time: 0.6, end_time: 0.8, text: 'd' },
        { start_time: 0.8, end_time: 1.0, text: 'e' },
        { start_time: 1.0, end_time: 1.2, text: 'f' },
        { start_time: 1.2, end_time: 1.4, text: 'g' },
        { start_time: 1.4, end_time: 1.6, text: 'h' },
      ]

      const config: Partial<SentenceSegmentationConfig> = {
        maxSentenceLength: 5, // 只允许5个字符（包括空格）
        useLengthLimit: true,
        usePunctuation: false,
        useTimeGaps: false,
      }

      const result = convertWordLevelToSentenceLevel(longTextUtterances, config)

      // 应该被分割成多个句子
      expect(result.length).toBeGreaterThan(1)
    })

    it('应该处理负数时间戳', () => {
      const negativeTime: RecognitionUtterance[] = [
        { start_time: -1.0, end_time: 0.0, text: '负时间' },
        { start_time: 0.0, end_time: 1.0, text: '正时间' },
      ]

      const result = convertWordLevelToSentenceLevel(negativeTime)

      expect(result).toHaveLength(1)
      expect(result[0].start).toBe(-1.0)
      expect(result[0].end).toBe(1.0)
    })
  })

  describe('配置测试', () => {
    it('应该使用默认配置', () => {
      const result = convertWordLevelToSentenceLevel(mockUtterances)

      expect(result).toBeDefined()
      expect(result.length).toBeGreaterThan(0)
    })

    it('应该禁用所有分割策略时合并所有文本', () => {
      const config: Partial<SentenceSegmentationConfig> = {
        usePunctuation: false,
        useTimeGaps: false,
        useLengthLimit: false,
      }

      const result = convertWordLevelToSentenceLevel(mockUtterancesWithPunctuation, config)

      expect(result).toHaveLength(1)
      expect(result[0].text).toContain('你好世界')
      expect(result[0].text).toContain('测试')
    })

    it('应该支持最小句子长度配置', () => {
      const config: Partial<SentenceSegmentationConfig> = {
        minSentenceLength: 20,
        usePunctuation: true,
      }

      const result = convertWordLevelToSentenceLevel(mockShortUtterances, config)

      // 由于最小长度限制，短文本应该被合并
      expect(result).toHaveLength(1)
    })
  })

  describe('checkSentenceEnd', () => {
    let config: SentenceSegmentationConfig

    beforeEach(() => {
      config = {
        maxSentenceLength: 50,
        minSentenceLength: 10,
        maxPauseTime: 1.0,
        usePunctuation: true,
        useTimeGaps: true,
        useLengthLimit: true,
      }
    })

    it('应该根据标点符号结束句子', () => {
      const currentUtterance: RecognitionUtterance = {
        start_time: 0.0,
        end_time: 1.0,
        text: '你好。',
      }
      const nextUtterance: RecognitionUtterance = {
        start_time: 1.0,
        end_time: 2.0,
        text: '世界',
      }
      const currentSentence = [currentUtterance]

      const result = checkSentenceEnd(currentUtterance, nextUtterance, currentSentence, config)
      expect(result).toBe(true)
    })

    it('应该根据时间间隔结束句子', () => {
      const currentUtterance: RecognitionUtterance = {
        start_time: 0.0,
        end_time: 1.0,
        text: '你好',
      }
      const nextUtterance: RecognitionUtterance = {
        start_time: 3.0, // 2秒停顿，超过maxPauseTime
        end_time: 4.0,
        text: '世界',
      }
      const currentSentence = [currentUtterance]

      const result = checkSentenceEnd(currentUtterance, nextUtterance, currentSentence, config)
      expect(result).toBe(true)
    })

    it('应该根据长度限制结束句子', () => {
      const currentUtterance: RecognitionUtterance = {
        start_time: 2.0,
        end_time: 3.0,
        text: '很长的文本',
      }
      const nextUtterance: RecognitionUtterance = {
        start_time: 3.0,
        end_time: 4.0,
        text: '继续',
      }
      // 创建一个总长度超过50字符的句子
      const currentSentence: RecognitionUtterance[] = [
        { start_time: 0.0, end_time: 1.0, text: '这是一个非常非常非常非常非常非常非常长的句子用来测试长度限制功能' }, // 约32字符
        { start_time: 1.0, end_time: 2.0, text: '包含了很多很多很多很多很多很多内容以确保超过限制' }, // 约26字符
        currentUtterance, // '很长的文本' 约5字符，总计约32+26+5+空格=65+字符，绝对超过50
      ]

      const result = checkSentenceEnd(currentUtterance, nextUtterance, currentSentence, config)
      expect(result).toBe(true)
    })

    it('当禁用标点符号检查时不应该根据标点符号结束', () => {
      const configNoPunc = { ...config, usePunctuation: false }
      const currentUtterance: RecognitionUtterance = {
        start_time: 0.0,
        end_time: 1.0,
        text: '你好。',
      }
      const nextUtterance: RecognitionUtterance = {
        start_time: 1.0,
        end_time: 2.0,
        text: '世界',
      }
      const currentSentence = [currentUtterance]

      const result = checkSentenceEnd(currentUtterance, nextUtterance, currentSentence, configNoPunc)
      expect(result).toBe(false)
    })

    it('当禁用时间间隔检查时不应该根据时间间隔结束', () => {
      const configNoTime = { ...config, useTimeGaps: false }
      const currentUtterance: RecognitionUtterance = {
        start_time: 0.0,
        end_time: 1.0,
        text: '你好',
      }
      const nextUtterance: RecognitionUtterance = {
        start_time: 3.0,
        end_time: 4.0,
        text: '世界',
      }
      const currentSentence = [currentUtterance]

      const result = checkSentenceEnd(currentUtterance, nextUtterance, currentSentence, configNoTime)
      expect(result).toBe(false)
    })

    it('当禁用长度限制检查时不应该根据长度结束', () => {
      const configNoLength = { ...config, useLengthLimit: false }
      const currentUtterance: RecognitionUtterance = {
        start_time: 2.0,
        end_time: 3.0,
        text: '很长的文本',
      }
      const nextUtterance: RecognitionUtterance = {
        start_time: 3.0,
        end_time: 4.0,
        text: '继续',
      }
      const currentSentence: RecognitionUtterance[] = [
        { start_time: 0.0, end_time: 1.0, text: '这是一个非常非常非常长的句子' },
        { start_time: 1.0, end_time: 2.0, text: '包含了很多内容' },
        currentUtterance,
      ]

      const result = checkSentenceEnd(currentUtterance, nextUtterance, currentSentence, configNoLength)
      expect(result).toBe(false)
    })

    it('当没有下一个utterance时应该正确处理', () => {
      const currentUtterance: RecognitionUtterance = {
        start_time: 0.0,
        end_time: 1.0,
        text: '最后一个词',
      }
      const currentSentence = [currentUtterance]

      const result = checkSentenceEnd(currentUtterance, undefined, currentSentence, config)
      expect(result).toBe(false)
    })

    it('应该识别不同的句子结束标点符号', () => {
      const punctuations = ['。', '！', '？', '.', '!', '?']

      punctuations.forEach((punct) => {
        const currentUtterance: RecognitionUtterance = {
          start_time: 0.0,
          end_time: 1.0,
          text: `测试${punct}`,
        }
        const nextUtterance: RecognitionUtterance = {
          start_time: 1.0,
          end_time: 2.0,
          text: '下一句',
        }
        const currentSentence = [currentUtterance]

        const result = checkSentenceEnd(currentUtterance, nextUtterance, currentSentence, config)
        expect(result).toBe(true)
      })
    })
  })

  describe('createSentenceFromWords', () => {
    it('应该正确创建句子级时间戳数据', () => {
      const words: RecognitionUtterance[] = [
        { start_time: 0.0, end_time: 1.0, text: '你好' },
        { start_time: 1.0, end_time: 2.0, text: '世界' },
        { start_time: 2.0, end_time: 3.0, text: '测试' },
      ]

      const result = createSentenceFromWords(words, 5)

      expect(result).toEqual({
        id: 5,
        text: '你好 世界 测试',
        start: 0.0,
        end: 3.0,
        speaker: undefined,
      })
    })

    it('应该正确处理包含说话人信息的数据', () => {
      const words: RecognitionUtterance[] = [
        { start_time: 0.0, end_time: 1.0, text: '你好', attribute: { speaker: 'speaker1' } },
        { start_time: 1.0, end_time: 2.0, text: '世界', attribute: { speaker: 'speaker1' } },
      ]

      const result = createSentenceFromWords(words, 0)

      expect(result?.speaker).toBe('speaker1')
    })

    it('应该处理空单词列表', () => {
      const result = createSentenceFromWords([], 0)
      expect(result).toBe(null)
    })

    it('应该正确处理单个单词', () => {
      const words: RecognitionUtterance[] = [{ start_time: 1.5, end_time: 2.5, text: '单词' }]

      const result = createSentenceFromWords(words, 3)

      expect(result).toEqual({
        id: 3,
        text: '单词',
        start: 1.5,
        end: 2.5,
        speaker: undefined,
      })
    })

    it('应该正确修剪文本中的空格', () => {
      const words: RecognitionUtterance[] = [
        { start_time: 0.0, end_time: 1.0, text: '  开始  ' },
        { start_time: 1.0, end_time: 2.0, text: '  结束  ' },
      ]

      const result = createSentenceFromWords(words, 0)

      expect(result?.text).toBe('开始     结束')
    })

    it('应该使用第一个单词的说话人信息', () => {
      const words: RecognitionUtterance[] = [
        { start_time: 0.0, end_time: 1.0, text: '第一', attribute: { speaker: 'speaker1' } },
        { start_time: 1.0, end_time: 2.0, text: '第二', attribute: { speaker: 'speaker2' } },
      ]

      const result = createSentenceFromWords(words, 0)

      expect(result?.speaker).toBe('speaker1')
    })
  })

  describe('analyzeUtteranceData', () => {
    it('应该正确分析空数据', () => {
      const result = analyzeUtteranceData([])

      expect(result).toEqual({
        totalWords: 0,
        averageWordLength: 0,
        averagePauseTime: 0,
        hasPunctuation: false,
        averageWordsPerSecond: 0,
      })
    })

    it('应该正确分析基本数据特征', () => {
      const utterances: RecognitionUtterance[] = [
        { start_time: 0.0, end_time: 1.0, text: 'hello' }, // 5字符
        { start_time: 1.5, end_time: 2.0, text: 'world' }, // 5字符，0.5秒停顿
        { start_time: 2.0, end_time: 3.0, text: 'test.' }, // 5字符，0秒停顿
      ]

      const result = analyzeUtteranceData(utterances)

      expect(result.totalWords).toBe(3)
      expect(result.averageWordLength).toBe(5)
      expect(result.averagePauseTime).toBe(0.5) // (0.5 + 0) / 1 (只计算了一个停顿)
      expect(result.hasPunctuation).toBe(true)
      expect(result.averageWordsPerSecond).toBe(1) // 3 words / 3 seconds
    })

    it('应该正确识别标点符号', () => {
      const punctuationTests = [
        { text: '测试。', expected: true },
        { text: '测试！', expected: true },
        { text: '测试？', expected: true },
        { text: '测试.', expected: true },
        { text: '测试!', expected: true },
        { text: '测试?', expected: true },
        { text: '测试', expected: false },
        { text: '测试，', expected: false }, // 逗号不是句子结束标点
      ]

      punctuationTests.forEach(({ text, expected }) => {
        const utterances: RecognitionUtterance[] = [{ start_time: 0.0, end_time: 1.0, text }]

        const result = analyzeUtteranceData(utterances)
        expect(result.hasPunctuation).toBe(expected)
      })
    })

    it('应该正确计算停顿时间', () => {
      const utterances: RecognitionUtterance[] = [
        { start_time: 0.0, end_time: 1.0, text: 'first' },
        { start_time: 2.0, end_time: 3.0, text: 'second' }, // 1秒停顿
        { start_time: 3.5, end_time: 4.0, text: 'third' }, // 0.5秒停顿
      ]

      const result = analyzeUtteranceData(utterances)

      expect(result.averagePauseTime).toBe(0.75) // (1.0 + 0.5) / 2
    })

    it('应该正确处理负停顿时间（重叠utterances）', () => {
      const utterances: RecognitionUtterance[] = [
        { start_time: 0.0, end_time: 2.0, text: 'first' },
        { start_time: 1.0, end_time: 3.0, text: 'second' }, // 重叠
      ]

      const result = analyzeUtteranceData(utterances)

      expect(result.averagePauseTime).toBe(0) // 负停顿时间被忽略
    })

    it('应该正确计算语速', () => {
      const utterances: RecognitionUtterance[] = [
        { start_time: 0.0, end_time: 1.0, text: 'word1' },
        { start_time: 1.0, end_time: 2.0, text: 'word2' },
        { start_time: 2.0, end_time: 4.0, text: 'word3' }, // 总时长4秒，3个词
      ]

      const result = analyzeUtteranceData(utterances)

      expect(result.averageWordsPerSecond).toBe(0.75) // 3 words / 4 seconds
    })

    it('应该处理单个utterance的情况', () => {
      const utterances: RecognitionUtterance[] = [{ start_time: 0.0, end_time: 1.0, text: 'single' }]

      const result = analyzeUtteranceData(utterances)

      expect(result.totalWords).toBe(1)
      expect(result.averageWordLength).toBe(6)
      expect(result.averagePauseTime).toBe(0) // 没有停顿
      expect(result.averageWordsPerSecond).toBe(1) // 1 word / 1 second
    })
  })

  describe('adaptConfigBasedOnAnalysis', () => {
    it('应该为快语速调整配置', () => {
      const analysis = {
        totalWords: 100,
        averageWordLength: 5,
        averagePauseTime: 0.1,
        hasPunctuation: true,
        averageWordsPerSecond: 3, // 快语速
      }

      const result = adaptConfigBasedOnAnalysis(analysis)

      expect(result.maxPauseTime).toBe(1.5) // 1.0 * 1.5
    })

    it('应该为无标点符号数据调整配置', () => {
      const analysis = {
        totalWords: 50,
        averageWordLength: 4,
        averagePauseTime: 0.5,
        hasPunctuation: false, // 无标点符号
        averageWordsPerSecond: 1.5,
      }

      const result = adaptConfigBasedOnAnalysis(analysis)

      expect(result.usePunctuation).toBe(false)
      expect(result.maxPauseTime).toBe(0.8) // 1.0 * 0.8
    })

    it('应该为短单词数据调整配置', () => {
      const analysis = {
        totalWords: 100,
        averageWordLength: 2, // 短单词
        averagePauseTime: 0.3,
        hasPunctuation: true,
        averageWordsPerSecond: 2,
      }

      const result = adaptConfigBasedOnAnalysis(analysis)

      expect(result.maxSentenceLength).toBe(180) // 150 * 1.2
    })

    it('应该应用用户自定义配置', () => {
      const analysis = {
        totalWords: 50,
        averageWordLength: 5,
        averagePauseTime: 0.5,
        hasPunctuation: true,
        averageWordsPerSecond: 1.5,
      }
      const customConfig = {
        maxSentenceLength: 200,
        usePunctuation: false,
      }

      const result = adaptConfigBasedOnAnalysis(analysis, customConfig)

      expect(result.maxSentenceLength).toBe(200) // 用户配置覆盖
      expect(result.usePunctuation).toBe(false) // 用户配置覆盖
    })

    it('应该组合多个调整条件', () => {
      const analysis = {
        totalWords: 100,
        averageWordLength: 2, // 短单词
        averagePauseTime: 0.1,
        hasPunctuation: false, // 无标点符号
        averageWordsPerSecond: 3, // 快语速
      }

      const result = adaptConfigBasedOnAnalysis(analysis)

      expect(result.maxPauseTime).toBeCloseTo(1.2, 1) // 1.0 * 1.5 * 0.8，使用 toBeCloseTo 处理浮点数精度
      expect(result.usePunctuation).toBe(false)
      expect(result.maxSentenceLength).toBe(180) // 150 * 1.2
    })

    it('应该返回包含所有必需字段的完整配置', () => {
      const analysis = {
        totalWords: 50,
        averageWordLength: 5,
        averagePauseTime: 0.5,
        hasPunctuation: true,
        averageWordsPerSecond: 1.5,
      }

      const result = adaptConfigBasedOnAnalysis(analysis)

      expect(result).toHaveProperty('maxSentenceLength')
      expect(result).toHaveProperty('minSentenceLength')
      expect(result).toHaveProperty('maxPauseTime')
      expect(result).toHaveProperty('usePunctuation')
      expect(result).toHaveProperty('useTimeGaps')
      expect(result).toHaveProperty('useLengthLimit')
    })

    it('应该保持默认配置的基本值', () => {
      const analysis = {
        totalWords: 50,
        averageWordLength: 5,
        averagePauseTime: 0.5,
        hasPunctuation: true,
        averageWordsPerSecond: 1.5, // 正常语速
      }

      const result = adaptConfigBasedOnAnalysis(analysis)

      expect(result.minSentenceLength).toBe(10) // 默认值未改变
      expect(result.useTimeGaps).toBe(true) // 默认值未改变
      expect(result.useLengthLimit).toBe(true) // 默认值未改变
    })
  })
})

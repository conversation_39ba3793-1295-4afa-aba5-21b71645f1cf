import { describe, it, expect } from 'vitest'
import { 
  calculateSimilarity, 
  calculateSimilarityWithConfig,
  SimilarityAlgorithm,
  preprocessText,
  normalizeText,
  levenshteinDistance,
  findBestMatch,
  calculateSimilarityMatrix,
  calculateJaccard
} from '../similarityCalculator'

describe('similarityCalculator', () => {
  describe('preprocessText', () => {
    it('应该移除标点符号', () => {
      const result = preprocessText('Hello, world!', { removePunctuation: true })
      expect(result).toBe('hello world')
    })

    it('应该保留标点符号当配置为false', () => {
      const result = preprocessText('Hello, world!', { 
        removePunctuation: false,
        caseSensitive: false
      })
      expect(result).toBe('hello, world!')
    })

    it('应该标准化空白字符', () => {
      const result = preprocessText('  hello   world  ', { normalizeWhitespace: true })
      expect(result).toBe('hello world')
    })

    it('应该保留原始空白字符当配置为false', () => {
      const result = preprocessText('  hello   world  ', { 
        normalizeWhitespace: false,
        caseSensitive: false
      })
      expect(result).toBe('  hello   world  ')
    })

    it('应该保持大小写敏感', () => {
      const result = preprocessText('Hello World', { caseSensitive: true })
      expect(result).toBe('Hello World')
    })

    it('应该转换为小写当大小写不敏感', () => {
      const result = preprocessText('Hello World', { caseSensitive: false })
      expect(result).toBe('hello world')
    })

    it('应该处理中文标点符号', () => {
      const result = preprocessText('你好，世界！', { 
        removePunctuation: true,
        caseSensitive: false
      })
      expect(result).toBe('你好世界')
    })

    it('应该同时应用所有预处理选项', () => {
      const result = preprocessText('  Hello,   World!!!  ', {
        normalizeWhitespace: true,
        removePunctuation: true,
        caseSensitive: false
      })
      expect(result).toBe('hello world')
    })
  })

  describe('calculateSimilarity', () => {
    it('应该计算相同文本的相似度为1', () => {
      const result = calculateSimilarity('hello world', 'hello world')
      expect(result).toBe(1)
    })

    it('应该计算完全不同文本的相似度', () => {
      const result = calculateSimilarity('hello', 'goodbye')
      expect(result).toBeLessThan(0.5)
    })

    it('应该处理空字符串', () => {
      expect(calculateSimilarity('', '')).toBe(1)
      expect(calculateSimilarity('hello', '')).toBe(0)
      expect(calculateSimilarity('', 'world')).toBe(0)
    })

    it('应该支持Jaccard算法', () => {
      const result = calculateSimilarity('hello world', 'world hello', {
        algorithm: SimilarityAlgorithm.JACCARD
      })
      expect(result).toBe(1) // 相同单词，不同顺序
    })

    it('应该支持编辑距离算法（默认）', () => {
      const result = calculateSimilarity('hello', 'helo')
      expect(result).toBeGreaterThan(0.5)
      expect(result).toBeLessThan(1)
    })

    it('应该支持大小写敏感配置', () => {
      const caseSensitive = calculateSimilarity('Hello', 'hello', {
        caseSensitive: true
      })
      const caseInsensitive = calculateSimilarity('Hello', 'hello', {
        caseSensitive: false
      })
      
      expect(caseInsensitive).toBeGreaterThan(caseSensitive)
      expect(caseInsensitive).toBe(1)
    })

    it('应该支持移除标点符号配置', () => {
      const withPunctuation = calculateSimilarity('hello!', 'hello?', {
        removePunctuation: false
      })
      const withoutPunctuation = calculateSimilarity('hello!', 'hello?', {
        removePunctuation: true
      })
      
      expect(withoutPunctuation).toBeGreaterThan(withPunctuation)
      expect(withoutPunctuation).toBe(1)
    })

    it('应该支持空白字符标准化配置', () => {
      const result = calculateSimilarity('hello  world', 'hello world', {
        normalizeWhitespace: true
      })
      expect(result).toBe(1)
    })
  })

  describe('normalizeText', () => {
    it('应该转换为小写', () => {
      const result = normalizeText('Hello World')
      expect(result).toBe('hello world')
    })

    it('应该移除前后空白并标准化内部空白', () => {
      const result = normalizeText('  hello   world  ')
      expect(result).toBe('hello world')
    })

    it('应该移除特殊字符但保留中文', () => {
      const result = normalizeText('Hello, 世界!')
      expect(result).toBe('hello 世界')
    })

    it('应该处理纯中文文本', () => {
      const result = normalizeText('你好世界')
      expect(result).toBe('你好世界')
    })

    it('应该处理空字符串', () => {
      const result = normalizeText('')
      expect(result).toBe('')
    })

    it('应该处理只有空白字符的字符串', () => {
      const result = normalizeText('   ')
      expect(result).toBe('')
    })
  })

  describe('levenshteinDistance', () => {
    it('应该计算相同字符串的距离为0', () => {
      const result = levenshteinDistance('hello', 'hello')
      expect(result).toBe(0)
    })

    it('应该计算插入操作的距离', () => {
      const result = levenshteinDistance('cat', 'cats')
      expect(result).toBe(1)
    })

    it('应该计算删除操作的距离', () => {
      const result = levenshteinDistance('cats', 'cat')
      expect(result).toBe(1)
    })

    it('应该计算替换操作的距离', () => {
      const result = levenshteinDistance('cat', 'bat')
      expect(result).toBe(1)
    })

    it('应该计算复杂字符串的距离', () => {
      const result = levenshteinDistance('kitten', 'sitting')
      expect(result).toBe(3)
    })

    it('应该处理空字符串', () => {
      expect(levenshteinDistance('', '')).toBe(0)
      expect(levenshteinDistance('hello', '')).toBe(5)
      expect(levenshteinDistance('', 'world')).toBe(5)
    })

    it('应该处理单字符字符串', () => {
      expect(levenshteinDistance('a', 'b')).toBe(1)
      expect(levenshteinDistance('a', 'a')).toBe(0)
    })
  })

  describe('findBestMatch', () => {
    const candidates = [
      { index: 0, text: 'hello world' },
      { index: 1, text: 'goodbye world' },
      { index: 2, text: 'hello universe' },
      { index: 3, text: 'hi there' }
    ]

    it('应该找到最佳匹配', () => {
      const result = findBestMatch('hello world', candidates)
      expect(result.index).toBe(0)
      expect(result.similarity).toBe(1)
    })

    it('应该找到相似度最高的候选项', () => {
      const result = findBestMatch('hello cosmos', candidates)
      expect(result.index).toBe(0)
      expect(result.similarity).toBeGreaterThan(0.5)
    })

    it('应该处理没有完全匹配的情况', () => {
      const result = findBestMatch('something completely different', candidates)
      expect(result.index).toBeGreaterThanOrEqual(0)
      expect(result.index).toBeLessThan(candidates.length)
      expect(result.similarity).toBeGreaterThanOrEqual(0)
      expect(result.similarity).toBeLessThanOrEqual(1)
    })

    it('应该在候选项列表为空时抛出错误', () => {
      expect(() => findBestMatch('hello', [])).toThrow('候选项列表不能为空')
    })

    it('应该处理单个候选项', () => {
      const singleCandidate = [{ index: 0, text: 'hello' }]
      const result = findBestMatch('hi', singleCandidate)
      expect(result.index).toBe(0)
      expect(result.similarity).toBeGreaterThan(0)
    })
  })

  describe('calculateSimilarityMatrix', () => {
    it('应该计算正确的相似度矩阵', () => {
      const sourceTexts = ['hello', 'world']
      const targetTexts = ['hello', 'goodbye', 'world']
      
      const matrix = calculateSimilarityMatrix(sourceTexts, targetTexts)
      
      expect(matrix).toHaveLength(2) // 2行
      expect(matrix[0]).toHaveLength(3) // 3列
      expect(matrix[1]).toHaveLength(3) // 3列
      
      // 检查对角线上的完全匹配
      expect(matrix[0][0]).toBe(1) // 'hello' vs 'hello'
      expect(matrix[1][2]).toBe(1) // 'world' vs 'world'
      
      // 检查所有值都在0-1范围内
      matrix.forEach(row => {
        row.forEach(similarity => {
          expect(similarity).toBeGreaterThanOrEqual(0)
          expect(similarity).toBeLessThanOrEqual(1)
        })
      })
    })

    it('应该处理空数组', () => {
      const matrix = calculateSimilarityMatrix([], [])
      expect(matrix).toEqual([])
    })

    it('应该处理单个元素', () => {
      const matrix = calculateSimilarityMatrix(['hello'], ['world'])
      expect(matrix).toHaveLength(1)
      expect(matrix[0]).toHaveLength(1)
      expect(matrix[0][0]).toBeGreaterThanOrEqual(0)
      expect(matrix[0][0]).toBeLessThanOrEqual(1)
    })
  })

  describe('calculateJaccard', () => {
    it('应该计算相同文本的Jaccard相似度为1', () => {
      const result = calculateJaccard('hello world', 'hello world')
      expect(result).toBe(1)
    })

    it('应该计算完全不同文本的Jaccard相似度为0', () => {
      const result = calculateJaccard('hello world', 'goodbye universe')
      expect(result).toBe(0)
    })

    it('应该计算部分重叠的文本', () => {
      const result = calculateJaccard('hello world', 'hello universe')
      expect(result).toBeCloseTo(0.3333, 4)
    })

    it('应该忽略词语顺序', () => {
      const result = calculateJaccard('hello world', 'world hello')
      expect(result).toBe(1)
    })

    it('应该处理重复词语', () => {
      const result = calculateJaccard('hello hello world', 'hello world world')
      expect(result).toBe(1)
    })

    it('应该处理空字符串', () => {
      expect(calculateJaccard('', '')).toBe(1)
      expect(calculateJaccard('hello', '')).toBe(0)
      expect(calculateJaccard('', 'world')).toBe(0)
    })

    it('应该处理单词文本', () => {
      const result = calculateJaccard('hello', 'hello')
      expect(result).toBe(1)
    })
  })

  describe('calculateSimilarityWithConfig', () => {
    it('应该使用配置对象正常工作', () => {
      const result = calculateSimilarityWithConfig('hello world', 'hello world', {
        algorithm: SimilarityAlgorithm.JACCARD,
        caseSensitive: false,
        removePunctuation: true,
        normalizeWhitespace: true
      })
      expect(result).toBe(1)
    })

    it('应该使用默认配置', () => {
      const result = calculateSimilarityWithConfig('hello world', 'hello world')
      expect(result).toBe(1)
    })

    it('应该使用Levenshtein算法配置', () => {
      const result = calculateSimilarityWithConfig('hello', 'helo', {
        algorithm: SimilarityAlgorithm.LEVENSHTEIN,
        caseSensitive: false,
        removePunctuation: true,
        normalizeWhitespace: true
      })
      expect(result).toBeGreaterThan(0.5)
      expect(result).toBeLessThan(1)
    })

    it('应该使用Jaccard算法配置', () => {
      const result = calculateSimilarityWithConfig('hello world', 'world hello', {
        algorithm: SimilarityAlgorithm.JACCARD,
        caseSensitive: false,
        removePunctuation: true,
        normalizeWhitespace: true
      })
      expect(result).toBe(1)
    })

    it('应该应用大小写敏感配置', () => {
      const caseSensitive = calculateSimilarityWithConfig('Hello', 'hello', {
        algorithm: SimilarityAlgorithm.LEVENSHTEIN,
        caseSensitive: true,
        removePunctuation: true,
        normalizeWhitespace: true
      })
      const caseInsensitive = calculateSimilarityWithConfig('Hello', 'hello', {
        algorithm: SimilarityAlgorithm.LEVENSHTEIN,
        caseSensitive: false,
        removePunctuation: true,
        normalizeWhitespace: true
      })
      
      expect(caseInsensitive).toBeGreaterThan(caseSensitive)
      expect(caseInsensitive).toBe(1)
    })
  })
}) 
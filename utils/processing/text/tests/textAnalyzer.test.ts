import { describe, it, expect } from 'vitest'
import { splitChunksByChars, splitByMeaning, getPreviousContent, getAfterContent } from '../textAnalyzer'

describe('textAnalyzer', () => {
  describe('splitChunksByChars', () => {
    it('应该按字符数分割文本', () => {
      const sentences = ['这是第一句话', '这是第二句话', '这是第三句话']
      const result = splitChunksByChars(sentences, 20, 10)

      expect(result).toHaveLength(2)
      expect(result[0]).toBe('这是第一句话\n这是第二句话')
      expect(result[1]).toBe('这是第三句话')
    })

    it('应该在达到字符限制时分割', () => {
      const sentences = ['短句', '这是一个非常长的句子，包含很多字符，应该会触发字符限制']
      const result = splitChunksByChars(sentences, 15, 10)

      expect(result).toHaveLength(2)
      expect(result[0]).toBe('短句')
      expect(result[1]).toBe('这是一个非常长的句子，包含很多字符，应该会触发字符限制')
    })

    it('应该在达到句子数限制时分割', () => {
      const sentences = ['句子1', '句子2', '句子3', '句子4', '句子5']
      const result = splitChunksByChars(sentences, 1000, 3)

      expect(result).toHaveLength(2)
      expect(result[0]).toBe('句子1\n句子2\n句子3')
      expect(result[1]).toBe('句子4\n句子5')
    })

    it('应该处理空数组', () => {
      const result = splitChunksByChars([], 100, 10)
      expect(result).toEqual([])
    })

    it('应该处理单个句子', () => {
      const sentences = ['这是唯一的句子']
      const result = splitChunksByChars(sentences, 100, 10)

      expect(result).toHaveLength(1)
      expect(result[0]).toBe('这是唯一的句子')
    })

    it('应该处理空字符串', () => {
      const sentences = ['', '有内容的句子', '']
      const result = splitChunksByChars(sentences, 100, 10)

      expect(result).toHaveLength(1)
      expect(result[0]).toBe('有内容的句子')
    })

    it('应该使用默认参数', () => {
      const sentences = Array(20).fill('这是一个测试句子')
      const result = splitChunksByChars(sentences)

      expect(result.length).toBeGreaterThan(1)
      result.forEach((chunk) => {
        expect(chunk.length).toBeLessThanOrEqual(600)
      })
    })

    it('应该正确处理边界情况', () => {
      const sentences = ['a'.repeat(500), 'b'.repeat(200)]
      const result = splitChunksByChars(sentences, 600, 10)

      expect(result).toHaveLength(2)
      expect(result[0]).toBe('a'.repeat(500))
      expect(result[1]).toBe('b'.repeat(200))
    })
  })

  describe('splitByMeaning', () => {
    it('应该基于中文标点符号分割', () => {
      const sentences = ['这是第一句。这是第二句！这是第三句？']
      const result = splitByMeaning(sentences)

      expect(result).toEqual(['这是第一句', '这是第二句', '这是第三句'])
    })

    it('应该基于英文标点符号分割', () => {
      const sentences = ['This is first. This is second! This is third?']
      const result = splitByMeaning(sentences)

      expect(result).toEqual(['This is first', 'This is second', 'This is third'])
    })

    it('应该处理分号分割', () => {
      const sentences = ['第一部分；第二部分;第三部分']
      const result = splitByMeaning(sentences)

      expect(result).toEqual(['第一部分', '第二部分', '第三部分'])
    })

    it('应该处理多个句子的数组', () => {
      const sentences = ['第一个句子。第二个句子！', '第三个句子？第四个句子。']
      const result = splitByMeaning(sentences)

      expect(result).toEqual(['第一个句子', '第二个句子', '第三个句子', '第四个句子'])
    })

    it('应该过滤空字符串', () => {
      const sentences = ['句子一。。句子二！！句子三？？']
      const result = splitByMeaning(sentences)

      expect(result).toEqual(['句子一', '句子二', '句子三'])
    })

    it('应该处理没有标点符号的句子', () => {
      const sentences = ['这是一个没有标点的句子']
      const result = splitByMeaning(sentences)

      expect(result).toEqual(['这是一个没有标点的句子'])
    })

    it('应该处理空数组', () => {
      const result = splitByMeaning([])
      expect(result).toEqual([])
    })

    it('应该处理空字符串', () => {
      const sentences = ['', '   ', '正常句子。']
      const result = splitByMeaning(sentences)

      expect(result).toEqual(['正常句子'])
    })

    it('应该处理混合中英文标点', () => {
      const sentences = ['中文句子。English sentence! 混合句子？Mixed sentence.']
      const result = splitByMeaning(sentences)

      expect(result).toEqual(['中文句子', 'English sentence', '混合句子', 'Mixed sentence'])
    })

    it('应该去除首尾空白', () => {
      const sentences = ['  句子一。   句子二！  ']
      const result = splitByMeaning(sentences)

      expect(result).toEqual(['句子一', '句子二'])
    })
  })

  describe('getPreviousContent', () => {
    const chunks = ['第一块\n内容1\n内容2\n内容3', '第二块\n内容4\n内容5\n内容6', '第三块\n内容7\n内容8\n内容9']

    it('应该获取前一块的最后几行', () => {
      const result = getPreviousContent(chunks, 1, 2)
      expect(result).toEqual(['内容2', '内容3'])
    })

    it('应该使用默认行数参数', () => {
      const result = getPreviousContent(chunks, 1)
      expect(result).toEqual(['内容1', '内容2', '内容3'])
    })

    it('应该在第一块时返回空数组', () => {
      const result = getPreviousContent(chunks, 0, 3)
      expect(result).toEqual([])
    })

    it('应该处理请求行数超过可用行数的情况', () => {
      const result = getPreviousContent(chunks, 1, 10)
      expect(result).toEqual(['第一块', '内容1', '内容2', '内容3'])
    })

    it('应该过滤空行', () => {
      const chunksWithEmptyLines = ['第一块\n\n内容1\n\n内容2\n', '第二块\n内容3']
      const result = getPreviousContent(chunksWithEmptyLines, 1, 2)
      expect(result).toEqual(['内容1', '内容2'])
    })

    it('应该处理只有一行的前一块', () => {
      const simpleChunks = ['单行内容', '第二块内容']
      const result = getPreviousContent(simpleChunks, 1, 3)
      expect(result).toEqual(['单行内容'])
    })

    it('应该处理边界索引', () => {
      const result = getPreviousContent(chunks, 2, 2)
      expect(result).toEqual(['内容5', '内容6'])
    })
  })

  describe('getAfterContent', () => {
    const chunks = ['第一块\n内容1\n内容2\n内容3', '第二块\n内容4\n内容5\n内容6', '第三块\n内容7\n内容8\n内容9']

    it('应该获取后一块的前几行', () => {
      const result = getAfterContent(chunks, 0, 2)
      expect(result).toEqual(['第二块', '内容4'])
    })

    it('应该使用默认行数参数', () => {
      const result = getAfterContent(chunks, 0)
      expect(result).toEqual(['第二块', '内容4'])
    })

    it('应该在最后一块时返回空数组', () => {
      const result = getAfterContent(chunks, 2, 2)
      expect(result).toEqual([])
    })

    it('应该处理请求行数超过可用行数的情况', () => {
      const result = getAfterContent(chunks, 0, 10)
      expect(result).toEqual(['第二块', '内容4', '内容5', '内容6'])
    })

    it('应该过滤空行', () => {
      const chunksWithEmptyLines = ['第一块内容', '\n第二块\n\n内容1\n\n内容2\n']
      const result = getAfterContent(chunksWithEmptyLines, 0, 2)
      expect(result).toEqual(['第二块', '内容1'])
    })

    it('应该处理只有一行的后一块', () => {
      const simpleChunks = ['第一块内容', '单行内容']
      const result = getAfterContent(simpleChunks, 0, 3)
      expect(result).toEqual(['单行内容'])
    })

    it('应该处理中间索引', () => {
      const result = getAfterContent(chunks, 1, 2)
      expect(result).toEqual(['第三块', '内容7'])
    })

    it('应该处理单个字符块', () => {
      const singleCharChunks = ['a', 'b\nc\nd']
      const result = getAfterContent(singleCharChunks, 0, 2)
      expect(result).toEqual(['b', 'c'])
    })
  })

  describe('集成测试', () => {
    it('应该能够配合使用多个函数', () => {
      // 先进行语义分割
      const sentences = ['这是第一句。这是第二句！', '这是第三句？这是第四句。']
      const meaningChunks = splitByMeaning(sentences)

      expect(meaningChunks).toEqual(['这是第一句', '这是第二句', '这是第三句', '这是第四句'])

      // 再按字符数分割
      const charChunks = splitChunksByChars(meaningChunks, 20, 2)
      expect(charChunks.length).toBeGreaterThan(0)

      // 测试上下文获取
      if (charChunks.length > 1) {
        const prevContent = getPreviousContent(charChunks, 1, 1)
        const afterContent = getAfterContent(charChunks, 0, 1)

        expect(prevContent.length).toBeGreaterThanOrEqual(0)
        expect(afterContent.length).toBeGreaterThanOrEqual(0)
      }
    })

    it('应该处理复杂的真实场景', () => {
      const realText = [
        '人工智能是计算机科学的一个分支。它试图理解智能的实质。AI可以分为弱人工智能和强人工智能。',
        '机器学习是人工智能的一个重要分支！深度学习则是机器学习的一个子集？神经网络是深度学习的基础。',
      ]

      // 语义分割
      const semanticChunks = splitByMeaning(realText)
      expect(semanticChunks.length).toBe(6)

      // 按字符分割
      const charChunks = splitChunksByChars(semanticChunks, 50, 2)
      expect(charChunks.length).toBeGreaterThan(1)

      // 获取上下文
      for (let i = 0; i < charChunks.length; i++) {
        const prev = getPreviousContent(charChunks, i)
        const after = getAfterContent(charChunks, i)

        if (i === 0) {
          expect(prev).toEqual([])
        } else {
          expect(prev.length).toBeGreaterThan(0)
        }

        if (i === charChunks.length - 1) {
          expect(after).toEqual([])
        } else {
          expect(after.length).toBeGreaterThan(0)
        }
      }
    })
  })
})

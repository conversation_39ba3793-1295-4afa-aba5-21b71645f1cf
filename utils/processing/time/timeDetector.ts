/**
 * 检测时间戳格式
 */
export function detectTimestampFormat(timestamp: string | number): string {
  if (typeof timestamp === 'number') {
    return 'seconds'
  }

  const timeString = timestamp.toString()

  if (/^\d{2}:\d{2}:\d{2}[,.]\d{3}$/.test(timeString)) {
    return 'srt'
  }
  if (/^\d+(\.\d+)?$/.test(timeString)) {
    return 'seconds'
  }
  if (/^\d{2}:\d{2}(\.\d+)?$/.test(timeString)) {
    return 'mm:ss'
  }

  return 'unknown'
} 
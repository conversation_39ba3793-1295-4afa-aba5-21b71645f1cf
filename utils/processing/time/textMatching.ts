/**
 * 文本匹配工具函数
 * 用于时间戳对齐中的文本匹配算法
 */

import type { SentenceUtterance, MatchResult, AlignmentOptions } from './alignmentTypes'

/**
 * 标准化文本
 */
export function normalizeText(text: string, options: AlignmentOptions = {}): string {
  if (!options.normalizeText) return text

  let normalized = text.trim()

  // 移除多余空格
  normalized = normalized.replace(/\s+/g, ' ')

  // 大小写处理
  if (!options.caseSensitive) {
    normalized = normalized.toLowerCase()
  }

  // 移除标点符号（用于匹配）
  normalized = normalized.replace(/[.,!?;:"'\-()[\]{}]/g, '')

  return normalized
}

/**
 * 计算Jaccard相似度
 */
export function calculateJaccardSimilarity(text1: string, text2: string): number {
  const words1 = new Set(text1.split(/\s+/).filter((word) => word.length > 0))
  const words2 = new Set(text2.split(/\s+/).filter((word) => word.length > 0))

  const intersection = new Set([...words1].filter((word) => words2.has(word)))
  const union = new Set([...words1, ...words2])

  return union.size === 0 ? 0 : intersection.size / union.size
}

/**
 * 精确文本匹配
 */
export function findExactMatch(text: string, utterances: SentenceUtterance[], options: AlignmentOptions = {}): MatchResult | null {
  const normalizedText = normalizeText(text, options)

  for (const utterance of utterances) {
    const normalizedUtterance = normalizeText(utterance.text, options)

    if (normalizedText === normalizedUtterance) {
      return {
        utterance,
        confidence: 1.0,
        strategy: 'exact',
      }
    }
  }

  return null
}

/**
 * 相似度文本匹配
 */
export function findSimilarityMatch(text: string, utterances: SentenceUtterance[], options: AlignmentOptions = {}): MatchResult | null {
  const normalizedText = normalizeText(text, options)
  const threshold = options.similarityThreshold ?? 0.6

  let bestMatch: MatchResult | null = null

  for (const utterance of utterances) {
    const normalizedUtterance = normalizeText(utterance.text, options)
    const similarity = calculateJaccardSimilarity(normalizedText, normalizedUtterance)

    if (similarity >= threshold) {
      if (!bestMatch || similarity > bestMatch.confidence) {
        bestMatch = {
          utterance,
          confidence: similarity,
          strategy: 'similarity',
        }
      }
    }
  }

  return bestMatch
}

/**
 * 位置估算匹配
 */
export function estimateByPosition(index: number, totalCount: number, utterances: SentenceUtterance[]): MatchResult {
  if (utterances.length === 0) {
    // 如果没有utterances，创建一个默认的
    const defaultUtterance: SentenceUtterance = {
      start_time: index * 3000, // 假设每句3秒
      end_time: (index + 1) * 3000,
      text: '',
    }

    return {
      utterance: defaultUtterance,
      confidence: 0.3,
      strategy: 'position',
    }
  }

  // 计算在utterances中的相对位置
  const relativePosition = totalCount > 1 ? index / (totalCount - 1) : 0
  const utteranceIndex = Math.floor(relativePosition * (utterances.length - 1))
  const clampedIndex = Math.max(0, Math.min(utteranceIndex, utterances.length - 1))

  return {
    utterance: utterances[clampedIndex],
    confidence: 0.5,
    strategy: 'position',
  }
}

/**
 * 综合文本匹配（按优先级尝试不同策略）
 */
export function findBestMatch(
  text: string,
  utterances: SentenceUtterance[],
  index: number,
  totalCount: number,
  options: AlignmentOptions = {},
): MatchResult {
  // 1. 尝试精确匹配
  const exactMatch = findExactMatch(text, utterances, options)
  if (exactMatch) {
    return exactMatch
  }

  // 2. 尝试相似度匹配
  const similarityMatch = findSimilarityMatch(text, utterances, options)
  if (similarityMatch) {
    return similarityMatch
  }

  // 3. 使用位置估算
  return estimateByPosition(index, totalCount, utterances)
} 
/**
 * 简化的时间戳对齐类型定义
 * 专注于句子级时间戳对齐功能
 */

/**
 * 句子级语音识别结果
 */
export interface SentenceUtterance {
  start_time: number // 毫秒
  end_time: number // 毫秒
  text: string
  attribute?: {
    speaker?: string
  }
}

/**
 * 翻译数据
 */
export interface Translation {
  source: string
  translation: string
  index: number
}

/**
 * 对齐后的翻译结果
 */
export interface AlignedTranslation {
  source: string
  translation: string
  startTime: number // 秒
  endTime: number // 秒
  confidence: number
  matchStrategy: 'exact' | 'similarity' | 'position'
}

/**
 * 对齐匹配策略
 */
export type MatchStrategy = 'exact' | 'similarity' | 'position'

/**
 * 文本匹配结果
 */
export interface MatchResult {
  utterance: SentenceUtterance
  confidence: number
  strategy: MatchStrategy
}

/**
 * 对齐处理结果
 */
export interface AlignmentResult {
  alignedTranslations: AlignedTranslation[]
  totalCount: number
  successCount: number
  averageConfidence: number
  processingTime: number
}

/**
 * 对齐配置选项
 */
export interface AlignmentOptions {
  similarityThreshold?: number // 相似度阈值，默认0.6
  normalizeText?: boolean // 是否标准化文本，默认true
  caseSensitive?: boolean // 是否大小写敏感，默认false
} 
import { describe, it, expect } from 'vitest'
import type { Subtitle } from '~/types/subtitle'
import { validateSrtTimeFormat, validateTimeLogic, hasTimestampError, isValidTimestamp } from '../timeValidator'

// 创建测试用的Subtitle对象的助手函数
function createSubtitle(overrides: Partial<Subtitle> = {}): Subtitle {
  return {
    uuid: 'test-uuid',
    id: 1,
    startTime: '00:00:10,500',
    endTime: '00:00:12,000',
    text: 'Test text',
    translationText: 'Test translation',
    ...overrides,
  }
}

describe('SRT时间格式验证', () => {
  it('应正确验证有效的SRT格式', () => {
    expect(validateSrtTimeFormat('00:01:30,500')).toBe(true)
    expect(validateSrtTimeFormat('01:23:45,123')).toBe(true)
    expect(validateSrtTimeFormat('00:00:00,000')).toBe(true)
    expect(validateSrtTimeFormat('99:59:59,999')).toBe(true)
  })

  it('应正确拒绝无效格式', () => {
    expect(validateSrtTimeFormat('1:30,500')).toBe(false)
    expect(validateSrtTimeFormat('01:30')).toBe(false)
    expect(validateSrtTimeFormat('01:30.500')).toBe(false)
    expect(validateSrtTimeFormat('invalid')).toBe(false)
    expect(validateSrtTimeFormat('')).toBe(false)
  })

  it('应正确处理边界值', () => {
    expect(validateSrtTimeFormat('01:60:30,500')).toBe(false)
    expect(validateSrtTimeFormat('01:30:60,500')).toBe(false)
    expect(validateSrtTimeFormat('01:30:45,1000')).toBe(false)
    expect(validateSrtTimeFormat('100:00:00,000')).toBe(false)
  })
})

describe('时间逻辑验证', () => {
  it('应正确验证正常时间逻辑', () => {
    expect(validateTimeLogic('00:01:00,000', '00:01:30,000')).toBe(true)
    expect(validateTimeLogic('00:00:00,000', '00:59:59,999')).toBe(true)
    expect(validateTimeLogic('01:23:45,123', '01:23:50,456')).toBe(true)
  })

  it('应正确处理相等时间', () => {
    expect(validateTimeLogic('00:01:30,500', '00:01:30,500')).toBe(true)
  })

  it('应拒绝开始时间晚于结束时间', () => {
    expect(validateTimeLogic('00:01:30,000', '00:01:00,000')).toBe(false)
    expect(validateTimeLogic('01:00:00,000', '00:59:59,999')).toBe(false)
  })

  it('应拒绝无效时间格式', () => {
    expect(validateTimeLogic('invalid', '00:01:30,000')).toBe(false)
    expect(validateTimeLogic('00:01:30,000', 'invalid')).toBe(false)
    expect(validateTimeLogic('invalid', 'invalid')).toBe(false)
  })
})

describe('字幕时间戳错误检测', () => {
  it('应检测格式错误', () => {
    const subtitle = createSubtitle({
      startTime: 'invalid',
      endTime: '00:01:30,000',
    })
    expect(hasTimestampError(subtitle)).toBe(true)

    const subtitle2 = createSubtitle({
      startTime: '00:01:00,000',
      endTime: 'invalid',
    })
    expect(hasTimestampError(subtitle2)).toBe(true)
  })

  it('应检测时间逻辑错误', () => {
    const subtitle = createSubtitle({
      startTime: '00:01:30,000',
      endTime: '00:01:00,000',
    })
    expect(hasTimestampError(subtitle)).toBe(true)
  })

  it('应检测与前一字幕的顺序错误', () => {
    const prevSubtitle = createSubtitle({
      startTime: '00:01:00,000',
      endTime: '00:01:30,000',
    })
    const currentSubtitle = createSubtitle({
      startTime: '00:01:20,000',
      endTime: '00:01:40,000',
    })
    expect(hasTimestampError(currentSubtitle, prevSubtitle)).toBe(true)
  })

  it('应检测与下一字幕的顺序错误', () => {
    const currentSubtitle = createSubtitle({
      startTime: '00:01:00,000',
      endTime: '00:01:30,000',
    })
    const nextSubtitle = createSubtitle({
      startTime: '00:01:20,000',
      endTime: '00:01:40,000',
    })
    expect(hasTimestampError(currentSubtitle, undefined, nextSubtitle)).toBe(true)
  })

  it('应正确处理正常字幕', () => {
    const subtitle = createSubtitle({
      startTime: '00:01:00,000',
      endTime: '00:01:30,000',
    })
    expect(hasTimestampError(subtitle)).toBe(false)
  })

  it('应正确处理正常字幕序列', () => {
    const prevSubtitle = createSubtitle({
      startTime: '00:01:00,000',
      endTime: '00:01:30,000',
    })
    const currentSubtitle = createSubtitle({
      startTime: '00:01:30,000',
      endTime: '00:02:00,000',
    })
    const nextSubtitle = createSubtitle({
      startTime: '00:02:00,000',
      endTime: '00:02:30,000',
    })
    expect(hasTimestampError(currentSubtitle, prevSubtitle, nextSubtitle)).toBe(false)
  })

  it('应处理解析失败的时间戳', () => {
    // 场景1：前一字幕的endTime无效，无法进行时间冲突检查
    const prevSubtitle = createSubtitle({
      startTime: '00:01:00,000',
      endTime: 'invalid', // 前一字幕的endTime无效
    })
    const currentSubtitle = createSubtitle({
      startTime: '00:01:00,000',
      endTime: '00:01:30,000',
    })
    // 当前字幕格式正确，但前一字幕的endTime无效，parseSrtTime会返回null
    // 根据源码第49行逻辑，当currentStart !== null && prevEnd !== null时才检查冲突
    // 这里prevEnd为null，所以不会进入冲突检查，应该返回false
    // 这是合理的：无法解析前一字幕时间时，就无法进行有意义的时间冲突检查
    expect(hasTimestampError(currentSubtitle, prevSubtitle)).toBe(false)

    // 场景2：前一字幕的startTime无效，但endTime有效
    const prevSubtitleWithInvalidStart = createSubtitle({
      startTime: 'invalid', // startTime无效
      endTime: '00:01:00,000', // endTime有效
    })
    const currentSubtitle2 = createSubtitle({
      startTime: '00:00:30,000', // 早于前一字幕的endTime，应该检测到冲突
      endTime: '00:01:30,000',
    })
    // 因为前一字幕的endTime有效，可以进行时间冲突检查
    // 当前字幕开始时间早于前一字幕结束时间，应该检测到错误
    expect(hasTimestampError(currentSubtitle2, prevSubtitleWithInvalidStart)).toBe(true)

    // 场景3：当前字幕本身格式不是严格SRT格式，会在第一步就失败
    const currentSubtitleWithLooseFormat = createSubtitle({
      startTime: '01:30', // 这不是严格的SRT格式
      endTime: '00:01:30,000',
    })
    expect(hasTimestampError(currentSubtitleWithLooseFormat, prevSubtitle)).toBe(true)
  })

  it('应处理下一字幕解析失败的情况', () => {
    const currentSubtitle = createSubtitle({
      startTime: '00:01:00,000',
      endTime: '00:01:30,000',
    })

    // 下一字幕的startTime无效
    const nextSubtitleWithInvalidStart = createSubtitle({
      startTime: 'invalid',
      endTime: '00:02:00,000',
    })
    // 无法解析下一字幕的开始时间，不会进行时间冲突检查
    expect(hasTimestampError(currentSubtitle, undefined, nextSubtitleWithInvalidStart)).toBe(false)

    // 下一字幕的startTime有效，但有时间冲突
    const nextSubtitleWithValidStart = createSubtitle({
      startTime: '00:01:20,000', // 早于当前字幕的结束时间
      endTime: '00:02:00,000',
    })
    // 可以检测到时间冲突
    expect(hasTimestampError(currentSubtitle, undefined, nextSubtitleWithValidStart)).toBe(true)
  })
})

describe('时间戳格式验证', () => {
  it('应正确验证数字类型', () => {
    expect(isValidTimestamp(0)).toBe(true)
    expect(isValidTimestamp(90.5)).toBe(true)
    expect(isValidTimestamp(123)).toBe(true)
    expect(isValidTimestamp(-10)).toBe(false)
  })

  it('应正确验证SRT格式字符串', () => {
    expect(isValidTimestamp('00:01:30,500')).toBe(true)
    expect(isValidTimestamp('01:23:45.123')).toBe(true)
    expect(isValidTimestamp('00:00:00,000')).toBe(true)
  })

  it('应正确验证纯数字字符串', () => {
    expect(isValidTimestamp('0')).toBe(true)
    expect(isValidTimestamp('90.5')).toBe(true)
    expect(isValidTimestamp('123')).toBe(true)
    expect(isValidTimestamp('-10')).toBe(false)
    expect(isValidTimestamp('abc123')).toBe(false)
  })

  it('应处理无效格式', () => {
    expect(isValidTimestamp('invalid')).toBe(false)
    expect(isValidTimestamp('')).toBe(false)
    expect(isValidTimestamp('01:30')).toBe(false) // 不是SRT格式也不是纯数字
    expect(isValidTimestamp('abc')).toBe(false)
  })

  it('应正确处理边界值', () => {
    expect(isValidTimestamp(0)).toBe(true)
    expect(isValidTimestamp('0')).toBe(true)
    expect(isValidTimestamp('0.0')).toBe(true)
    expect(isValidTimestamp('0.001')).toBe(true)
  })
})

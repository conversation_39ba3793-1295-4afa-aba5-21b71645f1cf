import { describe, it, expect } from 'vitest'
import type { TimestampData, DataStructureInfo } from '../timestampTypes'
import {
  detectTimestampType,
  analyzeDataStructure,
  performTypeDetection,
  estimateTypeAndConfidence,
  generateRecommendations,
  generateWarnings,
  createEmptyDataStructure
  } from '../timestampDetector'

// 测试数据常量
const PLAIN_TEXT_DATA: TimestampData[] = [
  { text: '这是第一段文本内容' },
  { text: '这是第二段文本内容' },
  { text: '这是第三段文本内容' }
]

const WORD_LEVEL_DATA: TimestampData[] = Array.from({ length: 120 }, (_, i) => ({
  text: `词${i}`,
  start: i * 0.5,
  end: (i + 1) * 0.5
}))

const SENTENCE_LEVEL_DATA: TimestampData[] = [
  { text: '这是一个完整的句子，包含了较多的文字内容。', start: 0, end: 3 },
  { text: '这是另一个完整的句子，同样包含了较多的文字内容。', start: 3, end: 6 },
  { text: '这是第三个完整的句子，具有正常的句子长度。', start: 6, end: 9 }
]

const MIXED_DATA: TimestampData[] = [
  { text: '有时间戳的文本', start: 0, end: 2 },
  { text: '没有时间戳的文本' },
  { text: '另一个有时间戳的文本', start: 2, end: 4 }
]

describe('时间戳类型检测', () => {
  it('应正确处理空数据', () => {
    const result = detectTimestampType([])
    expect(result.type).toBe('plain_text')
    expect(result.confidence).toBe(0)
    expect(result.warnings).toContain('数据为空')
    expect(result.recommendations).toContain('请提供有效的数据')
  })

  it('应正确检测纯文本数据', () => {
    const result = detectTimestampType(PLAIN_TEXT_DATA)
    expect(result.type).toBe('plain_text')
    expect(result.confidence).toBe(0.9)
    expect(result.dataStructure.hasTimeStamps).toBe(false)
    expect(result.warnings).toContain('数据中没有时间戳信息')
  })

  it('应正确检测词级数据', () => {
    const result = detectTimestampType(WORD_LEVEL_DATA)
    expect(result.type).toBe('word_level')
    expect(result.confidence).toBeGreaterThan(0)
    expect(result.dataStructure.hasTimeStamps).toBe(true)
    expect(result.recommendations).toContain('建议使用字符级精确匹配算法')
  })

  it('应正确检测句子级数据', () => {
    const result = detectTimestampType(SENTENCE_LEVEL_DATA)
    expect(result.type).toBe('sentence_level')
    expect(result.confidence).toBeGreaterThan(0)
    expect(result.dataStructure.hasTimeStamps).toBe(true)
    expect(result.recommendations).toContain('建议使用句子级智能映射算法')
  })

  it('应返回完整的检测结果结构', () => {
    const result = detectTimestampType(SENTENCE_LEVEL_DATA)
    expect(result).toHaveProperty('type')
    expect(result).toHaveProperty('confidence')
    expect(result).toHaveProperty('dataStructure')
    expect(result).toHaveProperty('recommendations')
    expect(result).toHaveProperty('warnings')
    expect(Array.isArray(result.recommendations)).toBe(true)
    expect(Array.isArray(result.warnings)).toBe(true)
  })
})

describe('数据结构分析', () => {
  it('应正确分析基本数据结构', () => {
    const result = analyzeDataStructure(SENTENCE_LEVEL_DATA)
    expect(result.totalRecords).toBe(3)
    expect(result.hasTimeStamps).toBe(true)
    expect(result.timeStampFormat).toBe('seconds')
    expect(result.averageTextLength).toBeGreaterThan(0)
    expect(result.estimatedType).toBeDefined()
    expect(result.confidence).toBeGreaterThan(0)
  })

  it('应正确分析纯文本数据', () => {
    const result = analyzeDataStructure(PLAIN_TEXT_DATA)
    expect(result.totalRecords).toBe(3)
    expect(result.hasTimeStamps).toBe(false)
    expect(result.timeStampFormat).toBe('none')
    expect(result.estimatedType).toBe('plain_text')
    expect(result.confidence).toBe(0.9)
  })

  it('应正确分析带时间戳数据', () => {
    const result = analyzeDataStructure(WORD_LEVEL_DATA)
    expect(result.totalRecords).toBe(120)
    expect(result.hasTimeStamps).toBe(true)
    expect(result.timeStampFormat).toBe('seconds')
    expect(result.estimatedType).toBe('word_level')
    expect(result.confidence).toBe(0.8)
  })

  it('应正确处理单条记录', () => {
    const singleData = [{ text: '单条文本', start: 0, end: 1 }]
    const result = analyzeDataStructure(singleData)
    expect(result.totalRecords).toBe(1)
    expect(result.averageTextLength).toBe(4)
  })
})

describe('类型检测执行', () => {
  it('应正确处理无时间戳数据', () => {
    const dataStructure: DataStructureInfo = {
      totalRecords: 3,
      hasTimeStamps: false,
      timeStampFormat: 'none',
      averageTextLength: 20,
      estimatedType: 'plain_text',
      confidence: 0.9
    }
    const result = performTypeDetection(PLAIN_TEXT_DATA, dataStructure)
    expect(result.type).toBe('plain_text')
    expect(result.confidence).toBe(0.9)
  })

  it('应正确检测词级特征', () => {
    const dataStructure: DataStructureInfo = {
      totalRecords: 120,
      hasTimeStamps: true,
      timeStampFormat: 'seconds',
      averageTextLength: 5,
      estimatedType: 'word_level',
      confidence: 0.8
    }
    const result = performTypeDetection(WORD_LEVEL_DATA, dataStructure)
    expect(result.type).toBe('word_level')
    expect(result.confidence).toBeGreaterThan(0)
  })

  it('应正确检测句子级特征', () => {
    const dataStructure: DataStructureInfo = {
      totalRecords: 3,
      hasTimeStamps: true,
      timeStampFormat: 'seconds',
      averageTextLength: 40,
      estimatedType: 'sentence_level',
      confidence: 0.8
    }
    const result = performTypeDetection(SENTENCE_LEVEL_DATA, dataStructure)
    expect(result.type).toBe('sentence_level')
    expect(result.confidence).toBeGreaterThan(0)
  })

  it('应正确处理特征分数相等的情况', () => {
    const balancedData = [
      { text: '中等长度的文本内容', start: 0, end: 1 }
    ]
    const dataStructure: DataStructureInfo = {
      totalRecords: 1,
      hasTimeStamps: true,
      timeStampFormat: 'seconds',
      averageTextLength: 25,
      estimatedType: 'sentence_level',
      confidence: 0.6
    }
    const result = performTypeDetection(balancedData, dataStructure)
    expect(result.type).toBe('sentence_level')
    expect(result.confidence).toBe(0.6)
  })
})

describe('类型和置信度估算', () => {
  it('应正确处理无时间戳场景', () => {
    const result = estimateTypeAndConfidence(PLAIN_TEXT_DATA, false, 20)
    expect(result.estimatedType).toBe('plain_text')
    expect(result.confidence).toBe(0.9)
  })

  it('应正确估算词级场景', () => {
    const result = estimateTypeAndConfidence(WORD_LEVEL_DATA, true, 10)
    expect(result.estimatedType).toBe('word_level')
    expect(result.confidence).toBe(0.8)
  })

  it('应正确估算句子级场景', () => {
    const result = estimateTypeAndConfidence(SENTENCE_LEVEL_DATA, true, 60)
    expect(result.estimatedType).toBe('sentence_level')
    expect(result.confidence).toBe(0.8)
  })

  it('应正确处理默认场景', () => {
    const result = estimateTypeAndConfidence(SENTENCE_LEVEL_DATA, true, 30)
    expect(result.estimatedType).toBe('sentence_level')
    expect(result.confidence).toBe(0.6)
  })
})

describe('建议生成', () => {
  it('应为词级类型生成正确建议', () => {
    const dataStructure: DataStructureInfo = {
      totalRecords: 120,
      hasTimeStamps: true,
      timeStampFormat: 'seconds',
      averageTextLength: 10,
      estimatedType: 'word_level',
      confidence: 0.8
    }
    const recommendations = generateRecommendations('word_level', dataStructure)
    expect(recommendations).toContain('建议使用字符级精确匹配算法')
    expect(recommendations).toContain('确保文本预处理一致性')
  })

  it('应为句子级类型生成正确建议', () => {
    const dataStructure: DataStructureInfo = {
      totalRecords: 10,
      hasTimeStamps: true,
      timeStampFormat: 'seconds',
      averageTextLength: 40,
      estimatedType: 'sentence_level',
      confidence: 0.8
    }
    const recommendations = generateRecommendations('sentence_level', dataStructure)
    expect(recommendations).toContain('建议使用句子级智能映射算法')
    expect(recommendations).toContain('可以尝试相似度匹配提高准确性')
  })

  it('应为纯文本类型生成正确建议', () => {
    const dataStructure: DataStructureInfo = {
      totalRecords: 5,
      hasTimeStamps: false,
      timeStampFormat: 'none',
      averageTextLength: 20,
      estimatedType: 'plain_text',
      confidence: 0.9
    }
    const recommendations = generateRecommendations('plain_text', dataStructure)
    expect(recommendations).toContain('需要手动设置时间戳或提供参考时间信息')
    expect(recommendations).toContain('建议根据文本长度估算时间分配')
  })

  it('应为低置信度生成额外建议', () => {
    const dataStructure: DataStructureInfo = {
      totalRecords: 10,
      hasTimeStamps: true,
      timeStampFormat: 'seconds',
      averageTextLength: 20,
      estimatedType: 'sentence_level',
      confidence: 0.5
    }
    const recommendations = generateRecommendations('sentence_level', dataStructure)
    expect(recommendations).toContain('数据特征不明显，建议手动确认数据类型')
  })
})

describe('警告生成', () => {
  it('应为数据量过少生成警告', () => {
    const smallData = [
      { text: '文本1', start: 0, end: 1 },
      { text: '文本2', start: 1, end: 2 }
    ]
    const dataStructure: DataStructureInfo = {
      totalRecords: 2,
      hasTimeStamps: true,
      timeStampFormat: 'seconds',
      averageTextLength: 3,
      estimatedType: 'word_level',
      confidence: 0.8
    }
    const warnings = generateWarnings(smallData, dataStructure)
    expect(warnings).toContain('数据量过少，可能影响检测准确性')
  })

  it('应为无时间戳生成警告', () => {
    const dataStructure: DataStructureInfo = {
      totalRecords: 3,
      hasTimeStamps: false,
      timeStampFormat: 'none',
      averageTextLength: 20,
      estimatedType: 'plain_text',
      confidence: 0.9
    }
    const warnings = generateWarnings(PLAIN_TEXT_DATA, dataStructure)
    expect(warnings).toContain('数据中没有时间戳信息')
  })

  it('应为缺少完整时间戳生成警告', () => {
    const dataStructure: DataStructureInfo = {
      totalRecords: 3,
      hasTimeStamps: true,
      timeStampFormat: 'seconds',
      averageTextLength: 20,
      estimatedType: 'sentence_level',
      confidence: 0.8
    }
    const warnings = generateWarnings(MIXED_DATA, dataStructure)
    expect(warnings).toContain('有1条记录缺少完整时间戳')
  })

  it('应正确处理混合场景', () => {
    const smallMixedData = [
      { text: '有时间戳', start: 0, end: 1 },
      { text: '无时间戳' }
    ]
    const dataStructure: DataStructureInfo = {
      totalRecords: 2,
      hasTimeStamps: true,
      timeStampFormat: 'seconds',
      averageTextLength: 10,
      estimatedType: 'word_level',
      confidence: 0.8
    }
    const warnings = generateWarnings(smallMixedData, dataStructure)
    expect(warnings).toContain('数据量过少，可能影响检测准确性')
    expect(warnings).toContain('有1条记录缺少完整时间戳')
  })
})

describe('空数据结构创建', () => {
  it('应返回正确的空数据结构', () => {
    const emptyStructure = createEmptyDataStructure()
    expect(emptyStructure.totalRecords).toBe(0)
    expect(emptyStructure.hasTimeStamps).toBe(false)
    expect(emptyStructure.timeStampFormat).toBe('none')
    expect(emptyStructure.averageTextLength).toBe(0)
    expect(emptyStructure.estimatedType).toBe('plain_text')
    expect(emptyStructure.confidence).toBe(0)
  })

  it('应返回完整的数据结构', () => {
    const emptyStructure = createEmptyDataStructure()
    expect(emptyStructure).toHaveProperty('totalRecords')
    expect(emptyStructure).toHaveProperty('hasTimeStamps')
    expect(emptyStructure).toHaveProperty('timeStampFormat')
    expect(emptyStructure).toHaveProperty('averageTextLength')
    expect(emptyStructure).toHaveProperty('estimatedType')
    expect(emptyStructure).toHaveProperty('confidence')
  })
}) 
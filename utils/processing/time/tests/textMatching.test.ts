import { describe, it, expect } from 'vitest'
import {
  normalizeText,
  calculateJaccardSimilarity,
  findExactMatch,
  findSimilarityMatch,
  estimateByPosition,
  findBestMatch
} from '../textMatching'
import type { SentenceUtterance, AlignmentOptions } from '../alignmentTypes'

// 模拟测试数据
const mockUtterances: SentenceUtterance[] = [
  {
    start_time: 0,
    end_time: 2000,
    text: "Hello world, this is a test."
  },
  {
    start_time: 2000,
    end_time: 4500,
    text: "This is the second sentence."
  },
  {
    start_time: 4500,
    end_time: 7000,
    text: "And this is the third one."
  },
  {
    start_time: 7000,
    end_time: 9000,
    text: "Finally, the last sentence."
  }
]

const emptyUtterances: SentenceUtterance[] = []

describe('normalizeText', () => {
  describe('基本标准化', () => {
    it('应移除多余空格', () => {
      const options: AlignmentOptions = { normalizeText: true }
      
      expect(normalizeText('hello   world', options)).toBe('hello world')
      expect(normalizeText('  spaced  text  ', options)).toBe('spaced text')
      expect(normalizeText('multiple\t\tspaces', options)).toBe('multiple spaces')
    })

    it('应处理大小写', () => {
      const caseSensitiveOptions: AlignmentOptions = { normalizeText: true, caseSensitive: true }
      const caseInsensitiveOptions: AlignmentOptions = { normalizeText: true, caseSensitive: false }
      
      expect(normalizeText('Hello World', caseSensitiveOptions)).toBe('Hello World')
      expect(normalizeText('Hello World', caseInsensitiveOptions)).toBe('hello world')
    })

    it('应移除标点符号', () => {
      const options: AlignmentOptions = { normalizeText: true }
      
      expect(normalizeText('Hello, world!', options)).toBe('hello world')
      expect(normalizeText('Test? Yes.', options)).toBe('test yes')
      expect(normalizeText('One-two-three', options)).toBe('onetwothree')
      expect(normalizeText('Quote "text" here', options)).toBe('quote text here')
      expect(normalizeText('(parentheses)', options)).toBe('parentheses')
      expect(normalizeText('[brackets]', options)).toBe('brackets')
      expect(normalizeText('{braces}', options)).toBe('braces')
    })

    it('应处理复合操作', () => {
      const options: AlignmentOptions = { 
        normalizeText: true, 
        caseSensitive: false 
      }
      
      expect(normalizeText('  Hello,  WORLD!  This is a "test".  ', options))
        .toBe('hello world this is a test')
    })
  })

  describe('选项控制', () => {
    it('当normalizeText为false时应保持原始文本', () => {
      const options: AlignmentOptions = { normalizeText: false }
      
      const originalText = '  Hello,  WORLD!  '
      expect(normalizeText(originalText, options)).toBe(originalText)
    })

    it('应使用默认选项', () => {
      expect(normalizeText('Hello World')).toBe('Hello World')
    })

    it('应处理空选项对象', () => {
      expect(normalizeText('Hello World', {})).toBe('Hello World')
    })
  })

  describe('边界情况', () => {
    it('应处理空字符串', () => {
      const options: AlignmentOptions = { normalizeText: true }
      expect(normalizeText('', options)).toBe('')
    })

    it('应处理只有空格的字符串', () => {
      const options: AlignmentOptions = { normalizeText: true }
      expect(normalizeText('   ', options)).toBe('')
    })

    it('应处理只有标点符号的字符串', () => {
      const options: AlignmentOptions = { normalizeText: true }
      expect(normalizeText('.,!?', options)).toBe('')
    })

    it('应处理单个字符', () => {
      const options: AlignmentOptions = { normalizeText: true, caseSensitive: false }
      expect(normalizeText('A', options)).toBe('a')
    })
  })
})

describe('calculateJaccardSimilarity', () => {
  describe('基本相似度计算', () => {
    it('应计算完全相同文本的相似度为1', () => {
      expect(calculateJaccardSimilarity('hello world', 'hello world')).toBe(1.0)
      expect(calculateJaccardSimilarity('test sentence', 'test sentence')).toBe(1.0)
    })

    it('应计算完全不同文本的相似度为0', () => {
      expect(calculateJaccardSimilarity('hello world', 'completely different')).toBe(0.0)
      expect(calculateJaccardSimilarity('one two three', 'four five six')).toBe(0.0)
    })

    it('应计算部分匹配的相似度', () => {
      // "hello world" vs "hello test" = ["hello"] / ["hello", "world", "test"] = 1/3
      expect(calculateJaccardSimilarity('hello world', 'hello test')).toBeCloseTo(1/3, 5)
      
      // "test one two" vs "test two three" = ["test", "two"] / ["test", "one", "two", "three"] = 2/4 = 0.5
      expect(calculateJaccardSimilarity('test one two', 'test two three')).toBe(0.5)
    })

    it('应处理词序不同的情况', () => {
      // 词序不应影响Jaccard相似度
      expect(calculateJaccardSimilarity('hello world test', 'test world hello')).toBe(1.0)
      expect(calculateJaccardSimilarity('one two three', 'three one two')).toBe(1.0)
    })
  })

  describe('边界情况', () => {
    it('应处理空字符串', () => {
      expect(calculateJaccardSimilarity('', '')).toBe(0)
      expect(calculateJaccardSimilarity('hello', '')).toBe(0)
      expect(calculateJaccardSimilarity('', 'world')).toBe(0)
    })

    it('应处理只有空格的字符串', () => {
      expect(calculateJaccardSimilarity('   ', 'hello')).toBe(0)
      expect(calculateJaccardSimilarity('hello', '   ')).toBe(0)
    })

    it('应处理重复单词', () => {
      // "hello hello world" vs "hello world" 应该是相同的集合
      expect(calculateJaccardSimilarity('hello hello world', 'hello world')).toBe(1.0)
      expect(calculateJaccardSimilarity('test test test', 'test')).toBe(1.0)
    })

    it('应处理单个单词', () => {
      expect(calculateJaccardSimilarity('hello', 'hello')).toBe(1.0)
      expect(calculateJaccardSimilarity('hello', 'world')).toBe(0.0)
    })
  })

  describe('复杂情况', () => {
    it('应处理包含标点符号的文本', () => {
      // calculateJaccardSimilarity直接对原始文本进行分词，标点符号作为独立token
      // 'hello, world!' -> ['hello,', 'world!']
      // 'hello world' -> ['hello', 'world']
      // 交集为空，所以相似度为0
      const similarity = calculateJaccardSimilarity('hello, world!', 'hello world')
      expect(similarity).toBe(0)
    })

    it('应处理不同长度的文本', () => {
      const shortText = 'hello'
      const longText = 'hello world this is a very long sentence'
      const similarity = calculateJaccardSimilarity(shortText, longText)
      expect(similarity).toBeGreaterThan(0) // 有"hello"匹配
      expect(similarity).toBeLessThan(0.5) // 但大部分不匹配
    })
  })
})

describe('findExactMatch', () => {
  describe('精确匹配', () => {
    it('应找到完全匹配的utterance', () => {
      const result = findExactMatch('Hello world, this is a test.', mockUtterances)
      
      expect(result).not.toBeNull()
      expect(result!.utterance.text).toBe('Hello world, this is a test.')
      expect(result!.confidence).toBe(1.0)
      expect(result!.strategy).toBe('exact')
    })

    it('应找到第二个匹配项', () => {
      const result = findExactMatch('This is the second sentence.', mockUtterances)
      
      expect(result).not.toBeNull()
      expect(result!.utterance.text).toBe('This is the second sentence.')
      expect(result!.utterance.start_time).toBe(2000)
      expect(result!.utterance.end_time).toBe(4500)
    })

    it('当没有匹配时应返回null', () => {
      const result = findExactMatch('No match for this text', mockUtterances)
      expect(result).toBeNull()
    })
  })

  describe('标准化匹配', () => {
    it('应支持大小写不敏感匹配', () => {
      const options: AlignmentOptions = { 
        normalizeText: true, 
        caseSensitive: false 
      }
      
      const result = findExactMatch('HELLO WORLD, THIS IS A TEST.', mockUtterances, options)
      
      expect(result).not.toBeNull()
      expect(result!.confidence).toBe(1.0)
      expect(result!.strategy).toBe('exact')
    })

    it('应支持标点符号忽略匹配', () => {
      const options: AlignmentOptions = { 
        normalizeText: true, 
        caseSensitive: false 
      }
      
      const result = findExactMatch('Hello world this is a test', mockUtterances, options)
      
      expect(result).not.toBeNull()
      expect(result!.confidence).toBe(1.0)
    })

    it('应支持空格标准化匹配', () => {
      const options: AlignmentOptions = { 
        normalizeText: true, 
        caseSensitive: false 
      }
      
      const result = findExactMatch('  Hello   world,   this   is  a   test.  ', mockUtterances, options)
      
      expect(result).not.toBeNull()
      expect(result!.confidence).toBe(1.0)
    })
  })

  describe('选项控制', () => {
    it('当normalizeText为false时应严格匹配', () => {
      const options: AlignmentOptions = { normalizeText: false }
      
      const result = findExactMatch('hello world, this is a test.', mockUtterances, options)
      expect(result).toBeNull() // 大小写不匹配
    })

    it('当caseSensitive为true时应区分大小写', () => {
      const options: AlignmentOptions = { 
        normalizeText: true, 
        caseSensitive: true 
      }
      
      const result = findExactMatch('hello world, this is a test.', mockUtterances, options)
      expect(result).toBeNull() // 大小写不匹配
    })
  })

  describe('边界情况', () => {
    it('应处理空的utterances数组', () => {
      const result = findExactMatch('test', emptyUtterances)
      expect(result).toBeNull()
    })

    it('应处理空的搜索文本', () => {
      const result = findExactMatch('', mockUtterances)
      expect(result).toBeNull()
    })

    it('应使用默认选项', () => {
      const result = findExactMatch('Hello world, this is a test.', mockUtterances)
      expect(result).not.toBeNull()
    })
  })
})

describe('findSimilarityMatch', () => {
  describe('相似度匹配', () => {
    it('应找到高相似度的匹配', () => {
      const result = findSimilarityMatch('Hello world test', mockUtterances)
      
      // 由于normalizeText会移除标点符号，计算实际相似度较低
      // 实际相似度约为0.33，低于默认阈值0.6，所以返回null
      expect(result).toBeNull()
    })

    it('应找到最佳匹配', () => {
      const testUtterances: SentenceUtterance[] = [
        { start_time: 0, end_time: 1000, text: 'hello test' },
        { start_time: 1000, end_time: 2000, text: 'hello world test sentence' },
        { start_time: 2000, end_time: 3000, text: 'different text' }
      ]
      
      const result = findSimilarityMatch('hello world test', testUtterances)
      
      expect(result).not.toBeNull()
      expect(result!.utterance.text).toBe('hello world test sentence') // 最相似的
    })

    it('应使用自定义相似度阈值', () => {
      const options: AlignmentOptions = { similarityThreshold: 0.8 }
      
      const result = findSimilarityMatch('Hello test', mockUtterances, options)
      
      // 低相似度的匹配应该被过滤掉
      expect(result).toBeNull()
    })

    it('应使用默认阈值0.6', () => {
      const result = findSimilarityMatch('Hello world', mockUtterances)
      
      // 相似度计算: "hello world" vs "hello world this is a test"
      // 交集: ["hello", "world"], 并集: ["hello", "world", "this", "is", "a", "test"]
      // 相似度 = 2/6 = 0.33，低于阈值0.6
      expect(result).toBeNull()
    })
  })

  describe('标准化选项', () => {
    it('应支持大小写不敏感匹配', () => {
      const options: AlignmentOptions = { 
        normalizeText: true, 
        caseSensitive: false 
      }
      
      const result = findSimilarityMatch('HELLO WORLD TEST', mockUtterances, options)
      
      // 即使大小写不敏感，相似度仍然是2/6=0.33，低于阈值
      expect(result).toBeNull()
    })

    it('应支持标点符号标准化', () => {
      const options: AlignmentOptions = { 
        normalizeText: true 
      }
      
      const result = findSimilarityMatch('Hello world test!!!', mockUtterances, options)
      
      // 标点符号被移除后，相似度仍然是2/6=0.33，低于阈值
      expect(result).toBeNull()
    })
  })

  describe('边界情况', () => {
    it('应处理空的utterances数组', () => {
      const result = findSimilarityMatch('test', emptyUtterances)
      expect(result).toBeNull()
    })

    it('应处理空的搜索文本', () => {
      const result = findSimilarityMatch('', mockUtterances)
      expect(result).toBeNull()
    })

    it('应处理没有超过阈值的匹配', () => {
      const result = findSimilarityMatch('completely different unrelated words', mockUtterances)
      expect(result).toBeNull()
    })

    it('应处理高阈值设置', () => {
      const options: AlignmentOptions = { similarityThreshold: 0.99 }
      
      const result = findSimilarityMatch('Hello world test', mockUtterances, options)
      expect(result).toBeNull() // 阈值太高
    })
  })

  describe('相似度计算', () => {
    it('应返回正确的置信度', () => {
      // 创建一个能确保匹配的测试用例
      const testUtterances: SentenceUtterance[] = [
        { start_time: 0, end_time: 1000, text: 'hello world test' },
        { start_time: 1000, end_time: 2000, text: 'different text' }
      ]
      
      const options: AlignmentOptions = { similarityThreshold: 0.5 }
      const result = findSimilarityMatch('hello world', testUtterances, options)
      
      expect(result).not.toBeNull()
      // "hello world" vs "hello world test"
      // 交集: ["hello", "world"], 并集: ["hello", "world", "test"]
      // 相似度应该是 2/3 = 0.667...
      expect(result!.confidence).toBeCloseTo(2/3, 2)
    })
  })
})

describe('estimateByPosition', () => {
  describe('基本位置估算', () => {
    it('应根据位置返回对应的utterance', () => {
      const result = estimateByPosition(0, 4, mockUtterances)
      
      expect(result.utterance).toBe(mockUtterances[0])
      expect(result.confidence).toBe(0.5)
      expect(result.strategy).toBe('position')
    })

    it('应处理中间位置', () => {
      const result = estimateByPosition(1, 4, mockUtterances)
      
      // 1/(4-1) = 1/3, 1/3 * (4-1) = 1, 所以应该是index 1
      expect(result.utterance).toBe(mockUtterances[1])
    })

    it('应处理最后位置', () => {
      const result = estimateByPosition(3, 4, mockUtterances)
      
      expect(result.utterance).toBe(mockUtterances[3])
    })
  })

  describe('边界情况', () => {
    it('应处理空的utterances数组', () => {
      const result = estimateByPosition(0, 1, emptyUtterances)
      
      expect(result.confidence).toBe(0.3)
      expect(result.strategy).toBe('position')
      expect(result.utterance.text).toBe('')
      expect(result.utterance.start_time).toBe(0)
      expect(result.utterance.end_time).toBe(3000) // index * 3000 + 3000
    })

    it('应处理单个条目情况', () => {
      const result = estimateByPosition(0, 1, mockUtterances)
      
      expect(result.utterance).toBe(mockUtterances[0])
    })

    it('应处理超出范围的索引', () => {
      const result = estimateByPosition(10, 4, mockUtterances)
      
      // 应该被夹紧到最后一个utterance
      expect(result.utterance).toBe(mockUtterances[3])
    })

    it('应处理负索引', () => {
      const result = estimateByPosition(-1, 4, mockUtterances)
      
      // 应该被夹紧到第一个utterance
      expect(result.utterance).toBe(mockUtterances[0])
    })
  })

  describe('时间估算', () => {
    it('应为空utterances创建合理的时间戳', () => {
      const result1 = estimateByPosition(0, 3, emptyUtterances)
      const result2 = estimateByPosition(1, 3, emptyUtterances)
      const result3 = estimateByPosition(2, 3, emptyUtterances)
      
      expect(result1.utterance.start_time).toBe(0)
      expect(result1.utterance.end_time).toBe(3000)
      
      expect(result2.utterance.start_time).toBe(3000)
      expect(result2.utterance.end_time).toBe(6000)
      
      expect(result3.utterance.start_time).toBe(6000)
      expect(result3.utterance.end_time).toBe(9000)
    })
  })

  describe('置信度设置', () => {
    it('应为有utterances的情况设置0.5置信度', () => {
      const result = estimateByPosition(0, 4, mockUtterances)
      expect(result.confidence).toBe(0.5)
    })

    it('应为空utterances设置0.3置信度', () => {
      const result = estimateByPosition(0, 1, emptyUtterances)
      expect(result.confidence).toBe(0.3)
    })
  })
})

describe('findBestMatch', () => {
  describe('匹配策略优先级', () => {
    it('应优先使用精确匹配', () => {
      const result = findBestMatch('Hello world, this is a test.', mockUtterances, 0, 4)
      
      expect(result.strategy).toBe('exact')
      expect(result.confidence).toBe(1.0)
      expect(result.utterance.text).toBe('Hello world, this is a test.')
    })

    it('应在无精确匹配时使用相似度匹配', () => {
      // 使用较高相似度的测试用例
      const testUtterances: SentenceUtterance[] = [
        { start_time: 0, end_time: 1000, text: 'Hello world test sentence' },
        { start_time: 1000, end_time: 2000, text: 'Different content here' },
      ]
      
      const result = findBestMatch('Hello world test', testUtterances, 0, 2)
      
      // "hello world test" vs "hello world test sentence"
      // 交集: ["hello", "world", "test"], 并集: ["hello", "world", "test", "sentence"]
      // 相似度 = 3/4 = 0.75 > 0.6，应该使用相似度匹配
      expect(result.strategy).toBe('similarity')
      expect(result.confidence).toBeCloseTo(0.75, 2)
      expect(result.utterance.text).toBe('Hello world test sentence')
    })

    it('应在无相似匹配时使用位置估算', () => {
      const result = findBestMatch('Completely different text', mockUtterances, 1, 4)
      
      expect(result.strategy).toBe('position')
      expect(result.confidence).toBe(0.5)
    })
  })

  describe('选项传递', () => {
    it('应将选项传递给精确匹配', () => {
      const options: AlignmentOptions = { 
        normalizeText: true, 
        caseSensitive: false 
      }
      
      const result = findBestMatch('HELLO WORLD, THIS IS A TEST.', mockUtterances, 0, 4, options)
      
      expect(result.strategy).toBe('exact')
      expect(result.confidence).toBe(1.0)
    })

    it('应将选项传递给相似度匹配', () => {
      const options: AlignmentOptions = { 
        similarityThreshold: 0.3,
        normalizeText: true, 
        caseSensitive: false 
      }
      
      const result = findBestMatch('HELLO WORLD', mockUtterances, 0, 4, options)
      
      expect(result.strategy).toBe('similarity')
      expect(result.confidence).toBeGreaterThan(0.3)
    })

    it('应处理高相似度阈值设置', () => {
      const options: AlignmentOptions = { 
        similarityThreshold: 0.9
      }
      
      const result = findBestMatch('Hello world', mockUtterances, 0, 4, options)
      
      // 相似度不够高，应该回退到位置估算
      expect(result.strategy).toBe('position')
    })
  })

  describe('边界情况', () => {
    it('应处理空的utterances数组', () => {
      const result = findBestMatch('test', emptyUtterances, 0, 1)
      
      expect(result.strategy).toBe('position')
      expect(result.confidence).toBe(0.3)
    })

    it('应处理空的搜索文本', () => {
      const result = findBestMatch('', mockUtterances, 0, 4)
      
      expect(result.strategy).toBe('position')
    })

    it('应使用默认选项', () => {
      const result = findBestMatch('Hello world, this is a test.', mockUtterances, 0, 4)
      
      expect(result.strategy).toBe('exact')
      expect(result.confidence).toBe(1.0)
    })
  })

  describe('位置信息传递', () => {
    it('应正确传递位置信息到位置估算', () => {
      const result = findBestMatch('No match', mockUtterances, 2, 4)
      
      expect(result.strategy).toBe('position')
      // 应该返回位置2对应的utterance
      expect(result.utterance).toBe(mockUtterances[2])
    })

    it('应处理不同的总数', () => {
      const result1 = findBestMatch('No match', mockUtterances, 0, 1)
      const result2 = findBestMatch('No match', mockUtterances, 0, 10)
      
      // 两个结果应该相同，因为都对应第一个位置
      expect(result1.utterance).toBe(mockUtterances[0])
      expect(result2.utterance).toBe(mockUtterances[0])
    })
  })

  describe('策略回退', () => {
    it('应在每个策略失败时正确回退', () => {
      // 测试完整的回退链：精确匹配 -> 相似度匹配 -> 位置估算
      
      // 1. 精确匹配成功
      const exactResult = findBestMatch('Hello world, this is a test.', mockUtterances, 0, 4)
      expect(exactResult.strategy).toBe('exact')
      
      // 2. 精确匹配失败，相似度匹配也失败（相似度低于阈值），使用位置估算
      const positionResult = findBestMatch('Hello world test', mockUtterances, 0, 4)
      expect(positionResult.strategy).toBe('position')
      
      // 3. 精确和相似度匹配都失败，使用位置估算
      const positionResult2 = findBestMatch('Completely unrelated text', mockUtterances, 0, 4)
      expect(positionResult2.strategy).toBe('position')
    })
  })
}) 
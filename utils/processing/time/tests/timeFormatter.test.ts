import { describe, it, expect } from 'vitest'
import { 
  formatTimeToString, 
  formatTimeForVtt, 
  formatTimeForSRT,
  formatDurationToMinute, 
  normalizeVttTime,
  millisecondsToSeconds
} from '../timeFormatter'

describe('formatTimeToString', () => {
  describe('HH:MM:SS格式', () => {
    it('应将秒格式化为 HH:MM:SS,mmm (默认逗号分隔符)', () => {
      expect(formatTimeToString(0)).toBe('00:00:00,000')
      expect(formatTimeToString(59.1)).toBe('00:00:59,100')
      expect(formatTimeToString(60)).toBe('00:01:00,000')
      expect(formatTimeToString(125.12)).toBe('00:02:05,120')
      expect(formatTimeToString(3600)).toBe('01:00:00,000')
      expect(formatTimeToString(3665.999)).toBe('01:01:05,999')
      expect(formatTimeToString(0.001)).toBe('00:00:00,001')
      expect(formatTimeToString(0.8)).toBe('00:00:00,800')
    })

    it('应将秒格式化为 HH:MM:SS.mmm (点号分隔符)', () => {
      expect(formatTimeToString(0, '.')).toBe('00:00:00.000')
      expect(formatTimeToString(59.1, '.')).toBe('00:00:59.100')
      expect(formatTimeToString(60, '.')).toBe('00:01:00.000')
      expect(formatTimeToString(125.12, '.')).toBe('00:02:05.120')
      expect(formatTimeToString(3600, '.')).toBe('01:00:00.000')
      expect(formatTimeToString(3665.999, '.')).toBe('01:01:05.999')
      expect(formatTimeToString(0.001, '.')).toBe('00:00:00.001')
      expect(formatTimeToString(0.8, '.')).toBe('00:00:00.800')
    })
  })

  describe('MM:SS格式', () => {
    it('应将秒格式化为 MM:SS,mmm 格式', () => {
      expect(formatTimeToString(0, ',', 'MM:SS')).toBe('00:00,000')
      expect(formatTimeToString(65.5, ',', 'MM:SS')).toBe('01:05,500')
      expect(formatTimeToString(3665, ',', 'MM:SS')).toBe('61:05,000')
      expect(formatTimeToString(125.12, ',', 'MM:SS')).toBe('02:05,120')
    })

    it('应将秒格式化为 MM:SS.mmm 格式', () => {
      expect(formatTimeToString(0, '.', 'MM:SS')).toBe('00:00.000')
      expect(formatTimeToString(65.5, '.', 'MM:SS')).toBe('01:05.500')
      expect(formatTimeToString(3665, '.', 'MM:SS')).toBe('61:05.000')
    })
  })

  describe('边界值和异常情况', () => {
    it('应处理极小值', () => {
      expect(formatTimeToString(0.0001)).toBe('00:00:00,000')
    })

    it('应处理极大值', () => {
      expect(formatTimeToString(359999.999)).toBe('99:59:59,999')
    })
  })
})

describe('formatTimeForVtt', () => {
  describe('已是VTT格式', () => {
    it('应直接返回已是VTT格式的时间字符串', () => {
      expect(formatTimeForVtt('00:01:23.456')).toBe('00:01:23.456')
      expect(formatTimeForVtt('12:34:56.789')).toBe('12:34:56.789')
      expect(formatTimeForVtt('00:00:00.000')).toBe('00:00:00.000')
    })
  })

  describe('格式转换', () => {
    it('应将逗号分隔的格式转换为VTT格式', () => {
      expect(formatTimeForVtt('00:01:23,456')).toBe('00:01:23.456')
      expect(formatTimeForVtt('12:34:56,789')).toBe('12:34:56.789')
    })

    it('应将简短格式转换为VTT格式', () => {
      expect(formatTimeForVtt('1:23')).toBe('00:01:23.000')
      expect(formatTimeForVtt('1:23.5')).toBe('00:01:23.500')
      expect(formatTimeForVtt('01:23')).toBe('00:01:23.000')
    })
  })

  describe('无效输入处理', () => {
    it('应处理无效输入', () => {
      expect(formatTimeForVtt('invalid')).toBe('invalid')
      expect(formatTimeForVtt('')).toBe('')
      expect(formatTimeForVtt('1:2:3:4')).toBe('1:2:3:4')
    })
  })
})

describe('formatDurationToMinute', () => {
  describe('基本转换', () => {
    it('应将毫秒转换为 mm:ss 格式', () => {
      expect(formatDurationToMinute(0)).toBe('0:00')
      expect(formatDurationToMinute(1000)).toBe('0:01')
      expect(formatDurationToMinute(60000)).toBe('1:00')
      expect(formatDurationToMinute(65000)).toBe('1:05')
      expect(formatDurationToMinute(3665000)).toBe('61:05')
    })

    it('应处理复杂时长', () => {
      expect(formatDurationToMinute(125120)).toBe('2:05')
      expect(formatDurationToMinute(3600000)).toBe('60:00')
    })
  })

  describe('边界值', () => {
    it('应处理不到1秒的时长', () => {
      expect(formatDurationToMinute(500)).toBe('0:00')
      expect(formatDurationToMinute(999)).toBe('0:00')
    })

    it('应处理长时间', () => {
      expect(formatDurationToMinute(3599000)).toBe('59:59')
      expect(formatDurationToMinute(7200000)).toBe('120:00')
    })
  })
})

describe('normalizeVttTime', () => {
  describe('mm:ss格式标准化', () => {
    it('应标准化基本mm:ss格式', () => {
      expect(normalizeVttTime('3:5')).toBe('00:03:05,000')
      expect(normalizeVttTime('03:05')).toBe('00:03:05,000')
      expect(normalizeVttTime('12:34')).toBe('00:12:34,000')
    })

    it('应标准化带毫秒的mm:ss格式', () => {
      expect(normalizeVttTime('3:5.1')).toBe('00:03:05,100')
      expect(normalizeVttTime('03:05.123')).toBe('00:03:05,123')
      expect(normalizeVttTime('3:5,456')).toBe('00:03:05,456')
    })
  })

  describe('HH:MM:SS格式标准化', () => {
    it('应标准化基本HH:MM:SS格式', () => {
      expect(normalizeVttTime('1:2:3')).toBe('01:02:03,000')
      expect(normalizeVttTime('12:34:56')).toBe('12:34:56,000')
      expect(normalizeVttTime('01:02:03')).toBe('01:02:03,000')
    })

    it('应标准化带毫秒的HH:MM:SS格式', () => {
      expect(normalizeVttTime('1:2:3.456')).toBe('01:02:03,456')
      expect(normalizeVttTime('1:2:3,789')).toBe('01:02:03,789')
      expect(normalizeVttTime('12:34:56.123')).toBe('12:34:56,123')
    })
  })

  describe('分隔符测试', () => {
    it('应支持点号分隔符', () => {
      expect(normalizeVttTime('3:5.1', '.')).toBe('00:03:05.100')
      expect(normalizeVttTime('1:2:3,456', '.')).toBe('01:02:03.456')
      expect(normalizeVttTime('1:2:3.456', '.')).toBe('01:02:03.456')
    })

    it('应支持逗号分隔符（默认）', () => {
      expect(normalizeVttTime('3:5.1', ',')).toBe('00:03:05,100')
      expect(normalizeVttTime('1:2:3.456', ',')).toBe('01:02:03,456')
    })
  })

  describe('宽松格式解析', () => {
    it('应截取过长的毫秒部分', () => {
      expect(normalizeVttTime('3:5.1234')).toBe('00:03:05,123')
      expect(normalizeVttTime('1:2:3.123456')).toBe('01:02:03,123')
    })

    it('应补齐不足的毫秒部分', () => {
      expect(normalizeVttTime('3:5.1')).toBe('00:03:05,100')
      expect(normalizeVttTime('1:2:3.12')).toBe('01:02:03,120')
    })
  })

  describe('不合法格式', () => {
    it('应原样返回不合法格式', () => {
      expect(normalizeVttTime('invalid')).toBe('invalid')
      expect(normalizeVttTime('')).toBe('')
      expect(normalizeVttTime('1:2:3:4')).toBe('1:2:3:4')
      expect(normalizeVttTime('abc:def')).toBe('abc:def')
    })
  })
})

describe('formatTimeForSRT', () => {
  describe('基本转换', () => {
    it('应将秒数格式化为 SRT 格式 (HH:MM:SS,mmm)', () => {
      expect(formatTimeForSRT(0)).toBe('00:00:00,000')
      expect(formatTimeForSRT(59.1)).toBe('00:00:59,100')
      expect(formatTimeForSRT(60)).toBe('00:01:00,000')
      expect(formatTimeForSRT(125.12)).toBe('00:02:05,120')
      expect(formatTimeForSRT(3600)).toBe('01:00:00,000')
      expect(formatTimeForSRT(3665.999)).toBe('01:01:05,999')
    })

    it('应处理小数秒', () => {
      expect(formatTimeForSRT(0.001)).toBe('00:00:00,001')
      expect(formatTimeForSRT(0.8)).toBe('00:00:00,800')
      expect(formatTimeForSRT(1.234)).toBe('00:00:01,234')
      expect(formatTimeForSRT(59.999)).toBe('00:00:59,999')
    })
  })

  describe('边界值测试', () => {
    it('应处理零值', () => {
      expect(formatTimeForSRT(0)).toBe('00:00:00,000')
    })

    it('应处理大时间值', () => {
      expect(formatTimeForSRT(359999.999)).toBe('99:59:59,999')
    })

    it('应处理极小值', () => {
      expect(formatTimeForSRT(0.0001)).toBe('00:00:00,000')
    })
  })

  describe('精度测试', () => {
    it('应正确处理毫秒精度', () => {
      expect(formatTimeForSRT(1.001)).toBe('00:00:01,001')
      expect(formatTimeForSRT(1.01)).toBe('00:00:01,010')
      expect(formatTimeForSRT(1.1)).toBe('00:00:01,100')
    })
  })
})

describe('millisecondsToSeconds', () => {
  describe('基本转换', () => {
    it('应将毫秒转换为秒', () => {
      expect(millisecondsToSeconds(1000)).toBe(1)
      expect(millisecondsToSeconds(2000)).toBe(2)
      expect(millisecondsToSeconds(5000)).toBe(5)
      expect(millisecondsToSeconds(60000)).toBe(60)
    })

    it('应处理小数毫秒', () => {
      expect(millisecondsToSeconds(500)).toBe(0.5)
      expect(millisecondsToSeconds(250)).toBe(0.25)
      expect(millisecondsToSeconds(100)).toBe(0.1)
      expect(millisecondsToSeconds(10)).toBe(0.01)
      expect(millisecondsToSeconds(1)).toBe(0.001)
    })
  })

  describe('边界值测试', () => {
    it('应处理零值', () => {
      expect(millisecondsToSeconds(0)).toBe(0)
    })

    it('应处理大数值', () => {
      expect(millisecondsToSeconds(3600000)).toBe(3600)
      expect(millisecondsToSeconds(86400000)).toBe(86400)
    })

    it('应处理极小值', () => {
      expect(millisecondsToSeconds(0.1)).toBe(0.0001)
      expect(millisecondsToSeconds(0.01)).toBe(0.00001)
    })
  })

  describe('精度测试', () => {
    it('应保持精确的小数转换', () => {
      expect(millisecondsToSeconds(1500)).toBe(1.5)
      expect(millisecondsToSeconds(2750)).toBe(2.75)
      expect(millisecondsToSeconds(125)).toBe(0.125)
    })

    it('应处理复杂的毫秒值', () => {
      expect(millisecondsToSeconds(1234)).toBe(1.234)
      expect(millisecondsToSeconds(999)).toBe(0.999)
      expect(millisecondsToSeconds(1001)).toBe(1.001)
    })
  })
}) 
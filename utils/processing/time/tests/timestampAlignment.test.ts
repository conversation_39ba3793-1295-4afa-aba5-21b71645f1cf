import { describe, it, expect } from 'vitest'
import {
  alignSentenceTimestamps,
  quickAlign,
  generateSRTSubtitles,
  validateAlignmentQuality
} from '../timestampAlignment'
import type { SentenceUtterance, Translation, AlignmentOptions } from '../alignmentTypes'

// 模拟测试数据
const mockUtterances: SentenceUtterance[] = [
  {
    start_time: 0,
    end_time: 2000,
    text: "Hello world, this is a test."
  },
  {
    start_time: 2000,
    end_time: 4500,
    text: "This is the second sentence."
  },
  {
    start_time: 4500,
    end_time: 7000,
    text: "And this is the third one."
  },
  {
    start_time: 7000,
    end_time: 9000,
    text: "Finally, the last sentence."
  }
]

const mockTranslations: Translation[] = [
  {
    source: "Hello world, this is a test.",
    translation: "你好世界，这是一个测试。",
    index: 0
  },
  {
    source: "This is the second sentence.",
    translation: "这是第二句话。",
    index: 1
  },
  {
    source: "And this is the third one.",
    translation: "这是第三句。",
    index: 2
  },
  {
    source: "Finally, the last sentence.",
    translation: "最后，最后一句话。",
    index: 3
  }
]

const mockPartialTranslations: Translation[] = [
  {
    source: "Hello world test",
    translation: "你好世界测试",
    index: 0
  },
  {
    source: "second sentence",
    translation: "第二句话",
    index: 1
  }
]

const mockMismatchedTranslations: Translation[] = [
  {
    source: "Completely different text",
    translation: "完全不同的文本",
    index: 0
  },
  {
    source: "Another unmatched sentence",
    translation: "另一个不匹配的句子",
    index: 1
  }
]

describe('alignSentenceTimestamps', () => {
  describe('精确匹配', () => {
    it('应正确对齐完全匹配的句子', () => {
      const result = alignSentenceTimestamps(mockUtterances, mockTranslations)

      expect(result.totalCount).toBe(4)
      expect(result.successCount).toBe(4)
      expect(result.averageConfidence).toBe(1.0)
      expect(result.alignedTranslations).toHaveLength(4)

      // 检查第一个对齐结果
      const firstAlignment = result.alignedTranslations[0]
      expect(firstAlignment.source).toBe("Hello world, this is a test.")
      expect(firstAlignment.translation).toBe("你好世界，这是一个测试。")
      expect(firstAlignment.startTime).toBe(0)
      expect(firstAlignment.endTime).toBe(2)
      expect(firstAlignment.confidence).toBe(1.0)
      expect(firstAlignment.matchStrategy).toBe('exact')
    })

    it('应正确处理时间戳转换（毫秒到秒）', () => {
      const result = alignSentenceTimestamps(mockUtterances, mockTranslations)
      
      expect(result.alignedTranslations[1].startTime).toBe(2)
      expect(result.alignedTranslations[1].endTime).toBe(4.5)
      expect(result.alignedTranslations[2].startTime).toBe(4.5)
      expect(result.alignedTranslations[2].endTime).toBe(7)
    })
  })

  describe('相似度匹配', () => {
    it('应处理部分匹配的文本', () => {
      const result = alignSentenceTimestamps(mockUtterances, mockPartialTranslations)

      expect(result.totalCount).toBe(2)
      // 由于相似度不够高，会使用位置估算，成功率为0（因为position策略置信度是0.5，不>0.5）
      expect(result.successCount).toBe(0)
      // 平均置信度会是0.5（所有都用position策略）
      expect(result.averageConfidence).toBe(0.5)
    })

    it('应使用自定义相似度阈值', () => {
      const options: AlignmentOptions = {
        similarityThreshold: 0.8
      }
      const result = alignSentenceTimestamps(mockUtterances, mockPartialTranslations, options)

      // 高阈值应该导致更少的成功匹配
      expect(result.averageConfidence).toBeLessThan(1.0)
    })
  })

  describe('位置估算', () => {
    it('应对无匹配的文本使用位置估算', () => {
      const result = alignSentenceTimestamps(mockUtterances, mockMismatchedTranslations)

      expect(result.totalCount).toBe(2)
      expect(result.alignedTranslations).toHaveLength(2)

      // 应该使用位置估算策略
      result.alignedTranslations.forEach(alignment => {
        expect(alignment.matchStrategy).toBe('position')
        expect(alignment.confidence).toBeLessThan(0.6)
      })
    })
  })

  describe('配置选项', () => {
    it('应支持大小写敏感选项', () => {
      const upperCaseTranslations: Translation[] = [
        {
          source: "HELLO WORLD, THIS IS A TEST.",
          translation: "你好世界，这是一个测试。",
          index: 0
        }
      ]

      const caseSensitiveResult = alignSentenceTimestamps(mockUtterances, upperCaseTranslations, {
        caseSensitive: true
      })

      const caseInsensitiveResult = alignSentenceTimestamps(mockUtterances, upperCaseTranslations, {
        caseSensitive: false
      })

      expect(caseInsensitiveResult.averageConfidence).toBeGreaterThan(caseSensitiveResult.averageConfidence)
    })

    it('应支持文本标准化选项', () => {
      const noisyTranslations: Translation[] = [
        {
          source: " Hello   world,  this   is a test.  ",
          translation: "你好世界，这是一个测试。",
          index: 0
        }
      ]

      const normalizedResult = alignSentenceTimestamps(mockUtterances, noisyTranslations, {
        normalizeText: true
      })

      const nonNormalizedResult = alignSentenceTimestamps(mockUtterances, noisyTranslations, {
        normalizeText: false
      })

      // 两种情况都会使用精确匹配，所以置信度相同
      expect(normalizedResult.averageConfidence).toBe(nonNormalizedResult.averageConfidence)
    })
  })

  describe('边界情况', () => {
    it('应处理空的输入数组', () => {
      const result = alignSentenceTimestamps([], [])
      
      expect(result.totalCount).toBe(0)
      expect(result.successCount).toBe(0)
      expect(result.averageConfidence).toBe(0)
      expect(result.alignedTranslations).toHaveLength(0)
    })

    it('应处理空的utterances数组', () => {
      const result = alignSentenceTimestamps([], mockTranslations)
      
      expect(result.totalCount).toBe(4)
      expect(result.alignedTranslations).toHaveLength(4)
      
      // 应该使用位置估算
      result.alignedTranslations.forEach(alignment => {
        expect(alignment.matchStrategy).toBe('position')
      })
    })

    it('应处理空的translations数组', () => {
      const result = alignSentenceTimestamps(mockUtterances, [])
      
      expect(result.totalCount).toBe(0)
      expect(result.successCount).toBe(0)
      expect(result.alignedTranslations).toHaveLength(0)
    })
  })

  describe('性能测试', () => {
    it('应在合理时间内完成处理', () => {
      const result = alignSentenceTimestamps(mockUtterances, mockTranslations)
      
      expect(result.processingTime).toBeLessThan(1000) // 小于1秒
      expect(result.processingTime).toBeGreaterThanOrEqual(0)
    })
  })
})

describe('quickAlign', () => {
  it('应返回对齐的翻译数组', () => {
    const result = quickAlign(mockUtterances, mockTranslations)
    
    expect(Array.isArray(result)).toBe(true)
    expect(result).toHaveLength(4)
    expect(result[0]).toHaveProperty('source')
    expect(result[0]).toHaveProperty('translation')
    expect(result[0]).toHaveProperty('startTime')
    expect(result[0]).toHaveProperty('endTime')
    expect(result[0]).toHaveProperty('confidence')
    expect(result[0]).toHaveProperty('matchStrategy')
  })

  it('应使用默认配置', () => {
    const result = quickAlign(mockUtterances, mockTranslations)
    
    // 应该能找到精确匹配
    expect(result[0].confidence).toBe(1.0)
    expect(result[0].matchStrategy).toBe('exact')
  })

  it('应处理空输入', () => {
    const result = quickAlign([], [])
    expect(result).toHaveLength(0)
  })
})

describe('generateSRTSubtitles', () => {
  describe('基本功能', () => {
    it('应生成正确格式的SRT字幕', () => {
      const alignedTranslations = quickAlign(mockUtterances, mockTranslations)
      const srtContent = generateSRTSubtitles(alignedTranslations)
      
      expect(srtContent).toContain('1\n')
      expect(srtContent).toContain('00:00:00,000 --> 00:00:02,000')
      expect(srtContent).toContain('你好世界，这是一个测试。')
      expect(srtContent).toContain('2\n')
      expect(srtContent).toContain('00:00:02,000 --> 00:00:04,500')
      expect(srtContent).toContain('这是第二句话。')
    })

    it('应正确编号SRT条目', () => {
      const alignedTranslations = quickAlign(mockUtterances, mockTranslations)
      const srtContent = generateSRTSubtitles(alignedTranslations)
      
      const lines = srtContent.split('\n')
      expect(lines[0]).toBe('1')
      expect(lines[4]).toBe('2')
      expect(lines[8]).toBe('3')
      expect(lines[12]).toBe('4')
    })

    it('应处理多行翻译文本', () => {
      const multiLineTranslations = [{
        source: "Test",
        translation: "第一行\n第二行",
        startTime: 0,
        endTime: 2,
        confidence: 1.0,
        matchStrategy: 'exact' as const
      }]
      
      const srtContent = generateSRTSubtitles(multiLineTranslations)
      expect(srtContent).toContain('第一行\n第二行')
    })
  })

  describe('边界情况', () => {
    it('应处理空的对齐结果', () => {
      const srtContent = generateSRTSubtitles([])
      expect(srtContent).toBe('')
    })

    it('应处理单个条目', () => {
      const singleTranslation = [{
        source: "Test",
        translation: "测试",
        startTime: 1.5,
        endTime: 3.2,
        confidence: 1.0,
        matchStrategy: 'exact' as const
      }]
      
      const srtContent = generateSRTSubtitles(singleTranslation)
      expect(srtContent).toBe('1\n00:00:01,500 --> 00:00:03,200\n测试')
    })

    it('应正确处理时间格式', () => {
      const preciseTranslation = [{
        source: "Test",
        translation: "测试",
        startTime: 65.123,
        endTime: 125.999,
        confidence: 1.0,
        matchStrategy: 'exact' as const
      }]
      
      const srtContent = generateSRTSubtitles(preciseTranslation)
      expect(srtContent).toContain('00:01:05,123 --> 00:02:05,999')
    })
  })
})

describe('validateAlignmentQuality', () => {
  describe('良好对齐', () => {
    it('应识别高质量对齐', () => {
      const goodResult = alignSentenceTimestamps(mockUtterances, mockTranslations)
      const validation = validateAlignmentQuality(goodResult)
      
      expect(validation.isGood).toBe(true)
      expect(validation.warnings).toHaveLength(0)
      expect(validation.suggestions).toHaveLength(0)
    })
  })

  describe('低质量对齐检测', () => {
    it('应检测低平均置信度', () => {
      // 创建一个真正的低置信度结果（平均置信度 < 0.5）
      const veryPoorResult = {
        totalCount: 3,
        successCount: 0,
        averageConfidence: 0.3, // 小于0.5
        alignedTranslations: [],
        processingTime: 100
      }
      
      const validation = validateAlignmentQuality(veryPoorResult)
      
      expect(validation.isGood).toBe(false)
      expect(validation.warnings.some(w => w.includes('平均置信度较低'))).toBe(true)
      expect(validation.suggestions.some(s => s.includes('相似度阈值'))).toBe(true)
    })

    it('应检测低成功率', () => {
      // 创建一个成功率较低的结果
      const lowSuccessResult = {
        totalCount: 10,
        successCount: 3,
        averageConfidence: 0.6,
        alignedTranslations: [],
        processingTime: 100
      }
      
      const validation = validateAlignmentQuality(lowSuccessResult)
      
      expect(validation.isGood).toBe(false)
      expect(validation.warnings.some(w => w.includes('成功对齐率较低'))).toBe(true)
      expect(validation.suggestions.some(s => s.includes('源文本与翻译文本'))).toBe(true)
    })
  })

  describe('时间重叠检测', () => {
    it('应检测时间重叠问题', () => {
      const overlappingTranslations = [
        {
          source: "First",
          translation: "第一",
          startTime: 0,
          endTime: 3,
          confidence: 1.0,
          matchStrategy: 'exact' as const
        },
        {
          source: "Second",
          translation: "第二",
          startTime: 2, // 重叠：2 < 3
          endTime: 5,
          confidence: 1.0,
          matchStrategy: 'exact' as const
        },
        {
          source: "Third",
          translation: "第三",
          startTime: 4, // 重叠：4 < 5
          endTime: 7,
          confidence: 1.0,
          matchStrategy: 'exact' as const
        }
      ]
      
      const overlappingResult = {
        totalCount: 3,
        successCount: 3,
        averageConfidence: 1.0,
        alignedTranslations: overlappingTranslations,
        processingTime: 100
      }
      
      const validation = validateAlignmentQuality(overlappingResult)
      
      expect(validation.isGood).toBe(false)
      expect(validation.warnings.some(w => w.includes('时间重叠'))).toBe(true)
      expect(validation.suggestions.some(s => s.includes('调整时间戳'))).toBe(true)
    })

    it('应正确计算重叠数量', () => {
      const multipleOverlapsTranslations = [
        { source: "1", translation: "一", startTime: 0, endTime: 2, confidence: 1.0, matchStrategy: 'exact' as const },
        { source: "2", translation: "二", startTime: 1, endTime: 3, confidence: 1.0, matchStrategy: 'exact' as const },
        { source: "3", translation: "三", startTime: 2.5, endTime: 4, confidence: 1.0, matchStrategy: 'exact' as const },
        { source: "4", translation: "四", startTime: 5, endTime: 7, confidence: 1.0, matchStrategy: 'exact' as const },
      ]
      
      const result = {
        totalCount: 4,
        successCount: 4,
        averageConfidence: 1.0,
        alignedTranslations: multipleOverlapsTranslations,
        processingTime: 100
      }
      
      const validation = validateAlignmentQuality(result)
      expect(validation.warnings.some(w => w.includes('发现2处时间重叠'))).toBe(true)
    })
  })

  describe('边界情况', () => {
    it('应处理空的对齐结果', () => {
      const emptyResult = {
        totalCount: 0,
        successCount: 0,
        averageConfidence: 0,
        alignedTranslations: [],
        processingTime: 0
      }
      
      const validation = validateAlignmentQuality(emptyResult)
      expect(validation.isGood).toBe(false) // 空结果会触发"平均置信度较低"警告，因为averageConfidence=0 < 0.5
    })

    it('应处理单个条目', () => {
      const singleResult = {
        totalCount: 1,
        successCount: 1,
        averageConfidence: 1.0,
        alignedTranslations: [{
          source: "Test",
          translation: "测试",
          startTime: 0,
          endTime: 2,
          confidence: 1.0,
          matchStrategy: 'exact' as const
        }],
        processingTime: 50
      }
      
      const validation = validateAlignmentQuality(singleResult)
      expect(validation.isGood).toBe(true)
    })
  })
}) 
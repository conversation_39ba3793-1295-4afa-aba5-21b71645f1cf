import { describe, it, expect } from 'vitest'
import { detectTimestampFormat } from '../timeDetector'

describe('detectTimestampFormat', () => {
  describe('数字类型输入', () => {
    it('应该识别整数为seconds格式', () => {
      expect(detectTimestampFormat(123)).toBe('seconds')
    })

    it('应该识别浮点数为seconds格式', () => {
      expect(detectTimestampFormat(123.45)).toBe('seconds')
    })

    it('应该识别零为seconds格式', () => {
      expect(detectTimestampFormat(0)).toBe('seconds')
    })
  })

  describe('SRT格式时间戳', () => {
    it('应该识别逗号分隔的毫秒格式', () => {
      expect(detectTimestampFormat('00:01:23,456')).toBe('srt')
    })

    it('应该识别点分隔的毫秒格式', () => {
      expect(detectTimestampFormat('00:01:23.456')).toBe('srt')
    })

    it('应该识别其他SRT格式时间戳', () => {
      expect(detectTimestampFormat('12:34:56,789')).toBe('srt')
    })
  })

  describe('纯秒数格式', () => {
    it('应该识别整数字符串为seconds格式', () => {
      expect(detectTimestampFormat('123')).toBe('seconds')
    })

    it('应该识别小数字符串为seconds格式', () => {
      expect(detectTimestampFormat('123.45')).toBe('seconds')
    })

    it('应该识别零字符串为seconds格式', () => {
      expect(detectTimestampFormat('0')).toBe('seconds')
    })

    it('应该识别小数零点几为seconds格式', () => {
      expect(detectTimestampFormat('0.5')).toBe('seconds')
    })
  })

  describe('mm:ss格式', () => {
    it('应该识别基本mm:ss格式', () => {
      expect(detectTimestampFormat('01:23')).toBe('mm:ss')
    })

    it('应该识别带小数的mm:ss格式', () => {
      expect(detectTimestampFormat('01:23.45')).toBe('mm:ss')
    })

    it('应该识别边界mm:ss格式', () => {
      expect(detectTimestampFormat('59:59')).toBe('mm:ss')
    })
  })

  describe('未知格式', () => {
    it('应该识别字母字符串为未知格式', () => {
      expect(detectTimestampFormat('abc')).toBe('unknown')
    })

    it('应该识别过多冒号为未知格式', () => {
      expect(detectTimestampFormat('1:2:3:4')).toBe('unknown')
    })

    it('应该识别空字符串为未知格式', () => {
      expect(detectTimestampFormat('')).toBe('unknown')
    })

    it('应该识别无效SRT格式为未知格式', () => {
      expect(detectTimestampFormat('00:01:23,ab')).toBe('unknown')
    })
  })
})

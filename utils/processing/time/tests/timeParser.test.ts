import { describe, it, expect } from 'vitest'
import { 
  parseTimeStringToSeconds,
  parseSrtTime,
  parseNormalizedTimeToSeconds,
  timestampToSeconds
} from '../timeParser'
import { normalizeVttTime } from '../timeFormatter'

describe("解析时间字符串为秒数", () => {
  it("应正确解析分钟和秒", () => {
    expect(parseTimeStringToSeconds("01:30")).toBe(90)
    expect(parseTimeStringToSeconds("10:00")).toBe(600)
  })

  it("应正确解析分钟、秒和毫秒", () => {
    expect(parseTimeStringToSeconds("00:59.500")).toBeCloseTo(59.5)
    expect(parseTimeStringToSeconds("02:05.120")).toBeCloseTo(125.12)
    expect(parseTimeStringToSeconds("00:00.123")).toBeCloseTo(0.123)
    expect(parseTimeStringToSeconds("01:30.1234")).toBeCloseTo(90.123)
  })

  it("应正确处理零值", () => {
    expect(parseTimeStringToSeconds("00:00.000")).toBe(0)
    expect(parseTimeStringToSeconds("00:00")).toBe(0)
  })

  it("对于无效格式应返回 0", () => {
    expect(parseTimeStringToSeconds("invalid-time")).toBe(0)
    expect(parseTimeStringToSeconds("")).toBe(0)
    expect(parseTimeStringToSeconds("60:00")).toBe(0)
    expect(parseTimeStringToSeconds("01:30.")).toBe(0)
  })

  it("应正确解析三级时间格式", () => {
    expect(parseTimeStringToSeconds("01:23:45")).toBe(5025)
    expect(parseTimeStringToSeconds("01:23:45.123")).toBeCloseTo(5025.123)
  })

  it("应正确解析单位数时间", () => {
    expect(parseTimeStringToSeconds("1:5")).toBe(65)
    expect(parseTimeStringToSeconds("1:5.1")).toBeCloseTo(65.1)
  })
})

describe("解析SRT时间格式", () => {
  it("应正确解析正确的SRT格式", () => {
    expect(parseSrtTime("00:01:30,500")).toBeCloseTo(90.5)
    expect(parseSrtTime("01:23:45,123")).toBeCloseTo(5025.123)
    expect(parseSrtTime("00:00:00,000")).toBe(0)
  })

  it("应正确处理边界值", () => {
    expect(parseSrtTime("99:59:59,999")).toBeCloseTo(359999.999)
    expect(parseSrtTime("00:00:00,001")).toBeCloseTo(0.001)
  })

  it("应正确处理可标准化的格式", () => {
    expect(parseSrtTime("01:30")).toBe(90)
    expect(parseSrtTime("1:30:45")).toBe(5445)
    expect(parseSrtTime("01:30.500")).toBeCloseTo(90.5)
  })

  it("对于完全无效格式应返回 null", () => {
    expect(parseSrtTime("invalid")).toBeNull()
    expect(parseSrtTime("")).toBeNull()
    expect(parseSrtTime("1:2:3:4")).toBeNull()
    expect(parseSrtTime("01:60:30,500")).toBeNull()
    expect(parseSrtTime("01:30:60,500")).toBeNull()
  })
})

describe("解析标准化时间为秒数", () => {
  it("应正确解析有效的标准化格式", () => {
    expect(parseNormalizedTimeToSeconds("00:01:30,500")).toBeCloseTo(90.5)
    expect(parseNormalizedTimeToSeconds("00:01:30.500")).toBeCloseTo(90.5)
    expect(parseNormalizedTimeToSeconds("01:23:45,123")).toBeCloseTo(5025.123)
  })

  it("应正确处理边界值", () => {
    expect(parseNormalizedTimeToSeconds("00:59:59,999")).toBeCloseTo(3599.999)
    expect(parseNormalizedTimeToSeconds("99:00:00,000")).toBe(356400)
  })

  it("对于无效格式应返回 null", () => {
    expect(parseNormalizedTimeToSeconds("1:30:45,500")).toBeNull()
    expect(parseNormalizedTimeToSeconds("01:60:30,500")).toBeNull()
    expect(parseNormalizedTimeToSeconds("01:30:60,500")).toBeNull()
    expect(parseNormalizedTimeToSeconds("01:30:45,1000")).toBeNull()
    expect(parseNormalizedTimeToSeconds("invalid")).toBeNull()
  })
})

describe("时间戳转秒数", () => {
  it("应正确处理数字输入", () => {
    expect(timestampToSeconds(90.5)).toBe(90.5)
    expect(timestampToSeconds(0)).toBe(0)
    expect(timestampToSeconds(123)).toBe(123)
  })

  it("应正确处理有效时间字符串", () => {
    expect(timestampToSeconds("01:30")).toBe(90)
    expect(timestampToSeconds("01:30.500")).toBeCloseTo(90.5)
    expect(timestampToSeconds("00:01:23,456")).toBeCloseTo(83.456)
  })

  it("应正确处理纯数字字符串", () => {
    expect(timestampToSeconds("90.5")).toBe(90.5)
    expect(timestampToSeconds("123")).toBe(123)
    expect(timestampToSeconds("0")).toBe(0)
  })

  it("对于无效输入应返回 0", () => {
    expect(timestampToSeconds("invalid")).toBe(0)
    expect(timestampToSeconds("")).toBe(0)
    expect(timestampToSeconds("-10")).toBe(0)
    expect(timestampToSeconds("abc123")).toBe(0)
  })
})

describe("标准化时间字符串", () => {
  it("应正确标准化 mm:ss 格式", () => {
    expect(normalizeVttTime("1:30")).toBe("00:01:30,000")
    expect(normalizeVttTime("01:30")).toBe("00:01:30,000")
    expect(normalizeVttTime("1:5")).toBe("00:01:05,000")
  })

  it("应正确标准化 mm:ss.xxx 格式", () => {
    expect(normalizeVttTime("1:30.5")).toBe("00:01:30,500")
    expect(normalizeVttTime("01:30.123")).toBe("00:01:30,123")
    expect(normalizeVttTime("1:5.1234")).toBe("00:01:05,123")
  })

  it("应正确标准化 HH:MM:SS 格式", () => {
    expect(normalizeVttTime("1:23:45")).toBe("01:23:45,000")
    expect(normalizeVttTime("01:23:45")).toBe("01:23:45,000")
  })

  it("应正确标准化 HH:MM:SS.xxx 格式", () => {
    expect(normalizeVttTime("1:23:45.123")).toBe("01:23:45,123")
    expect(normalizeVttTime("01:23:45,456")).toBe("01:23:45,456")
  })

  it("应正确处理不同分隔符", () => {
    expect(normalizeVttTime("1:30.5", ".")).toBe("00:01:30.500")
    expect(normalizeVttTime("1:30.5", ",")).toBe("00:01:30,500")
  })

  it("对于无效格式应原样返回", () => {
    expect(normalizeVttTime("invalid")).toBe("invalid")
    expect(normalizeVttTime("1")).toBe("1")
    expect(normalizeVttTime("1:2:3:4")).toBe("1:2:3:4")
  })
}) 
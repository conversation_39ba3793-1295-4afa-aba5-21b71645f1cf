/**
 * 句子级时间戳对齐主函数
 * 专注于处理JSON格式的utterances数据
 */

import type { SentenceUtterance, Translation, AlignedTranslation, AlignmentResult, AlignmentOptions } from './alignmentTypes'
import { findBestMatch } from './textMatching'
import { millisecondsToSeconds, formatTimeForSRT } from './timeFormatter'

/**
 * 主要对齐函数：将句子级utterances与翻译结果对齐
 */
export function alignSentenceTimestamps(
  utterances: SentenceUtterance[],
  translations: Translation[],
  options: AlignmentOptions = {},
): AlignmentResult {
  const startTime = Date.now()

  // 设置默认选项
  const alignmentOptions: AlignmentOptions = {
    similarityThreshold: 0.6,
    normalizeText: true,
    caseSensitive: false,
    ...options,
  }

  const alignedTranslations: AlignedTranslation[] = []
  let totalConfidence = 0
  let successCount = 0

  // 为每个翻译项找到最佳匹配的utterance
  for (let i = 0; i < translations.length; i++) {
    const translation = translations[i]

    // 使用source文本进行匹配
    const matchResult = findBestMatch(translation.source, utterances, i, translations.length, alignmentOptions)

    // 创建对齐结果
    const alignedTranslation: AlignedTranslation = {
      source: translation.source,
      translation: translation.translation,
      startTime: millisecondsToSeconds(matchResult.utterance.start_time),
      endTime: millisecondsToSeconds(matchResult.utterance.end_time),
      confidence: matchResult.confidence,
      matchStrategy: matchResult.strategy,
    }

    alignedTranslations.push(alignedTranslation)
    totalConfidence += matchResult.confidence

    // 统计成功对齐的数量（置信度 > 0.5）
    if (matchResult.confidence > 0.5) {
      successCount++
    }
  }

  const processingTime = Date.now() - startTime
  const averageConfidence = translations.length > 0 ? totalConfidence / translations.length : 0

  return {
    alignedTranslations,
    totalCount: translations.length,
    successCount,
    averageConfidence,
    processingTime,
  }
}

/**
 * 快速对齐函数：使用默认配置进行对齐
 */
export function quickAlign(utterances: SentenceUtterance[], translations: Translation[]): AlignedTranslation[] {
  const result = alignSentenceTimestamps(utterances, translations)
  return result.alignedTranslations
}

/**
 * 生成SRT格式的字幕
 */
export function generateSRTSubtitles(alignedTranslations: AlignedTranslation[]): string {
  let srtContent = ''

  for (let i = 0; i < alignedTranslations.length; i++) {
    const translation = alignedTranslations[i]

    srtContent += `${i + 1}\n`
    srtContent += `${formatTimeForSRT(translation.startTime)} --> ${formatTimeForSRT(translation.endTime)}\n`
    srtContent += `${translation.translation}\n\n`
  }

  return srtContent.trim()
}

/**
 * 验证对齐质量
 */
export function validateAlignmentQuality(result: AlignmentResult): {
  isGood: boolean
  warnings: string[]
  suggestions: string[]
} {
  const warnings: string[] = []
  const suggestions: string[] = []

  // 检查平均置信度
  if (result.averageConfidence < 0.5) {
    warnings.push(`平均置信度较低: ${(result.averageConfidence * 100).toFixed(1)}%`)
    suggestions.push('考虑调整相似度阈值或检查翻译文本质量')
  }

  // 检查成功率
  const successRate = result.totalCount > 0 ? result.successCount / result.totalCount : 0
  if (successRate < 0.7) {
    warnings.push(`成功对齐率较低: ${(successRate * 100).toFixed(1)}%`)
    suggestions.push('检查源文本与翻译文本是否匹配')
  }

  // 检查时间重叠
  const overlaps = checkTimeOverlaps(result.alignedTranslations)
  if (overlaps.length > 0) {
    warnings.push(`发现${overlaps.length}处时间重叠`)
    suggestions.push('考虑调整时间戳或重新分割句子')
  }

  const isGood = warnings.length === 0

  return { isGood, warnings, suggestions }
}

/**
 * 检查时间重叠
 */
function checkTimeOverlaps(alignedTranslations: AlignedTranslation[]): number[] {
  const overlaps: number[] = []

  for (let i = 0; i < alignedTranslations.length - 1; i++) {
    const current = alignedTranslations[i]
    const next = alignedTranslations[i + 1]

    if (current.endTime > next.startTime) {
      overlaps.push(i)
    }
  }

  return overlaps
}

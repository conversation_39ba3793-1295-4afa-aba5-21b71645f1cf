import type { Subtitle } from '~/types/subtitle'
import { normalizeVttTime } from './timeFormatter'
import { parseNormalizedTimeToSeconds, parseSrtTime } from './timeParser'

/**
 * 校验 SRT 时间字符串格式 "HH:MM:SS,mmm"
 */
export function validateSrtTimeFormat(timeString: string): boolean {
  const normalized = normalizeVttTime(timeString, ',')
  const result = parseNormalizedTimeToSeconds(normalized)
  return normalized === timeString && result !== null
}

/**
 * 校验开始时间不能晚于结束时间
 */
export function validateTimeLogic(startTime: string, endTime: string): boolean {
  const startNormalized = normalizeVttTime(startTime, ',')
  const endNormalized = normalizeVttTime(endTime, ',')
  const start = parseNormalizedTimeToSeconds(startNormalized)
  const end = parseNormalizedTimeToSeconds(endNormalized)
  if (start === null || end === null) return false
  return start <= end
}

/**
 * 检测字幕时间戳是否有错误
 * @param subtitle 当前字幕
 * @param prevSubtitle 前一个字幕（可选）
 * @param nextSubtitle 下一个字幕（可选）
 * @returns 如果时间戳有错误返回true，否则返回false
 */
export function hasTimestampError(subtitle: Subtitle, prevSubtitle?: Subtitle, nextSubtitle?: Subtitle): boolean {
  // 1. 格式验证
  if (!validateSrtTimeFormat(subtitle.startTime) || !validateSrtTimeFormat(subtitle.endTime)) {
    return true
  }

  // 2. 时间逻辑验证
  if (!validateTimeLogic(subtitle.startTime, subtitle.endTime)) {
    return true
  }

  // 3. 与前一字幕的顺序验证
  if (prevSubtitle) {
    const currentStart = parseSrtTime(subtitle.startTime)
    const prevEnd = parseSrtTime(prevSubtitle.endTime)
    if (currentStart !== null && prevEnd !== null && currentStart < prevEnd) {
      return true
    }
  }

  // 4. 与下一字幕的顺序验证
  if (nextSubtitle) {
    const currentEnd = parseSrtTime(subtitle.endTime)
    const nextStart = parseSrtTime(nextSubtitle.startTime)
    if (currentEnd !== null && nextStart !== null && currentEnd > nextStart) {
      return true
    }
  }

  return false
}

/**
 * 验证时间戳格式
 */
export function isValidTimestamp(timestamp: string | number): boolean {
  if (typeof timestamp === 'number') {
    return timestamp >= 0
  }

  const timeString = timestamp.toString()

  // SRT格式
  if (/^\d{2}:\d{2}:\d{2}[,.]\d{3}$/.test(timeString)) {
    return parseSrtTime(timeString) !== null
  }

  // 秒数格式
  if (/^\d+(\.\d+)?$/.test(timeString)) {
    const seconds = parseFloat(timeString)
    return !isNaN(seconds) && seconds >= 0
  }

  return false
}

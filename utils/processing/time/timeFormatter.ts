import Decimal from 'decimal.js-light'

/**
 * 通用时间格式化函数
 * @param seconds 秒数
 * @param separator 秒与毫秒的分隔符
 * @param format 时间格式：HH:MM:SS 或 MM:SS
 * @returns 格式化后的时间字符串
 */
export function formatTimeToString(seconds: number, separator: ',' | '.' = ',', format: 'HH:MM:SS' | 'MM:SS' = 'HH:MM:SS'): string {
  const dSeconds = new Decimal(seconds)
  const totalMs = dSeconds.times(1000).toDecimalPlaces(0)
  const ms = totalMs.modulo(1000)
  const totalIntSeconds = totalMs.minus(ms).dividedBy(1000).toDecimalPlaces(0)
  const secs = totalIntSeconds.modulo(60)
  const minutes = totalIntSeconds.dividedToIntegerBy(60).modulo(60)
  const hours = totalIntSeconds.dividedToIntegerBy(3600)

  const msStr = ms.toNumber().toString().padStart(3, '0')
  const secsStr = secs.toNumber().toString().padStart(2, '0')
  const minsStr = minutes.toNumber().toString().padStart(2, '0')
  const hoursStr = hours.toNumber().toString().padStart(2, '0')

  if (format === 'MM:SS') {
    const totalMinutes = totalIntSeconds.dividedToIntegerBy(60)
    return `${totalMinutes.toNumber().toString().padStart(2, '0')}:${secsStr}${separator}${msStr}`
  }

  return `${hoursStr}:${minsStr}:${secsStr}${separator}${msStr}`
}

/**
 * 格式化时间为VTT格式 (HH:MM:SS.mmm)
 * @param timeString 时间字符串，支持多种格式
 * @returns VTT格式的时间字符串
 */
export function formatTimeForVtt(timeString: string): string {
  // 如果已经是VTT格式，直接返回
  if (/^\d{2}:\d{2}:\d{2}\.\d{3}$/.test(timeString)) {
    return timeString
  }

  // 先标准化为带逗号的格式，然后转换为VTT格式
  const normalized = normalizeVttTime(timeString, ',')
  return normalized.replace(',', '.')
}

/**
 * 格式化时间为SRT格式显示 (HH:MM:SS,mmm)
 * @param seconds 秒数
 * @returns SRT格式的时间字符串
 */
export function formatTimeForSRT(seconds: number): string {
  const dSeconds = new Decimal(seconds)
  const totalMs = dSeconds.times(1000).toDecimalPlaces(0)
  const ms = totalMs.modulo(1000)
  const totalIntSeconds = totalMs.minus(ms).dividedBy(1000).toDecimalPlaces(0)
  const secs = totalIntSeconds.modulo(60)
  const minutes = totalIntSeconds.dividedToIntegerBy(60).modulo(60)
  const hours = totalIntSeconds.dividedToIntegerBy(3600)

  const msStr = ms.toNumber().toString().padStart(3, '0')
  const secsStr = secs.toNumber().toString().padStart(2, '0')
  const minsStr = minutes.toNumber().toString().padStart(2, '0')
  const hoursStr = hours.toNumber().toString().padStart(2, '0')

  return `${hoursStr}:${minsStr}:${secsStr},${msStr}`
}

/**
 * 格式化持续时间（毫秒 → "分:秒" 字符串） 返回 mm:ss
 */
export function formatDurationToMinute(ms: number): string {
  const seconds = Math.floor(ms / 1000)
  const minutes = Math.floor(seconds / 60)
  const remainingSeconds = seconds % 60
  return `${minutes}:${remainingSeconds.toString().padStart(2, '0')}`
}

/**
 * 标准化时间字符串为 HH:MM:SS,mmm 或 HH:MM:SS.mmm 格式
 * 支持各种宽松格式：mm:ss, mm:ss.xxx, HH:MM:SS, HH:MM:SS.xxx 等
 * @param raw 原始时间字符串，如 "3:5", "3:5.1", "3:5:1.1", "03:05:01.123", "12:34:56" 等
 * @param separator 秒与毫秒的分隔符，默认为逗号
 * @returns 标准化后的时间字符串 "HH:MM:SS,mmm" 或 "HH:MM:SS.mmm"
 */
/**
 * 毫秒转秒
 * @param ms 毫秒数
 * @returns 秒数
 */
export function millisecondsToSeconds(ms: number): number {
  return new Decimal(ms).dividedBy(1000).toNumber()
}

export function normalizeVttTime(raw: string, separator: ',' | '.' = ','): string {
  // 匹配 mm:ss 或 mm:ss.xxx 格式（2级时间）
  let match = raw.match(/^(\d{1,2}):(\d{1,2})(?:[.,](\d{1,4}))?$/)
  if (match) {
    let [, m, s, ms] = match
    m = m.padStart(2, '0')
    s = s.padStart(2, '0')
    ms = ms ? ms.slice(0, 3).padEnd(3, '0') : '000'
    return `00:${m}:${s}${separator}${ms}`
  }

  // 匹配 HH:MM:SS 或 HH:MM:SS.xxx 格式（3级时间）
  match = raw.match(/^(\d{1,2}):(\d{1,2}):(\d{1,2})([.,](\d+))?$/)
  if (match) {
    let [, h, m, s, , ms] = match
    h = h.padStart(2, '0')
    m = m.padStart(2, '0')
    s = s.padStart(2, '0')
    ms = ms ? ms.slice(0, 3).padEnd(3, '0') : '000'
    return `${h}:${m}:${s}${separator}${ms}`
  }

  return raw // 不合法则原样返回
}

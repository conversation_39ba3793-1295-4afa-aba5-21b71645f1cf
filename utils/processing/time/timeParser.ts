import Decimal from 'decimal.js-light'
import { normalizeVttTime } from './timeFormatter'

/**
 * 解析 SRT 时间字符串 "HH:MM:SS,mmm" 为秒数（浮点数）
 */
export function parseSrtTime(timeString: string): number | null {
  const normalized = normalizeVttTime(timeString, ',')
  if (normalized === timeString && !timeString.match(/^(\d{2}):(\d{2}):(\d{2}),(\d{3})$/)) {
    return null // 严格验证SRT格式，不能标准化说明格式不正确
  }
  const result = parseNormalizedTimeToSeconds(normalized)
  return result === null ? null : result
}

/**
 * 将时间字符串解析为秒数
 * @param timeString 格式为 "mm:ss" 或 "mm:ss.xxx" 的时间字符串
 * @returns 转换后的秒数，如果格式无效则返回 0
 */
export function parseTimeStringToSeconds(timeString: string): number {
  const normalized = normalizeVttTime(timeString, ',')
  const result = parseNormalizedTimeToSeconds(normalized)
  return result === null ? 0 : result
}

/**
 * 通用解析函数：将标准化的时间字符串转换为秒数
 * @param normalizedTime 标准化的时间字符串 "HH:MM:SS,mmm" 或 "HH:MM:SS.mmm"
 * @returns 转换后的秒数，如果格式无效则返回 0
 */
export function parseNormalizedTimeToSeconds(normalizedTime: string): number | null {
  const match = normalizedTime.match(/^(\d{2}):(\d{2}):(\d{2})[,.](\d{3})$/)
  if (!match) return null

  const [, hh, mm, ss, ms] = match
  const hours = new Decimal(hh)
  const minutes = new Decimal(mm)
  const seconds = new Decimal(ss)
  const milliseconds = new Decimal(ms)

  // 验证范围
  if (minutes.gte(60) || seconds.gte(60) || hours.gte(100) || milliseconds.gte(1000)) return null

  return hours.times(3600).plus(minutes.times(60)).plus(seconds).plus(milliseconds.dividedBy(1000)).toNumber()
}

/**
 * 通用时间戳转秒数函数
 */
export function timestampToSeconds(timestamp: string | number): number {
  if (typeof timestamp === 'number') {
    return timestamp
  }

  const timeString = timestamp.toString()

  // 先尝试使用 parseTimeStringToSeconds，因为它有完整的格式验证
  const result = parseTimeStringToSeconds(timeString)
  if (result !== 0) {
    return result
  }

  // 如果 parseTimeStringToSeconds 返回 0，再尝试简单的秒数解析
  if (/^\d+(.\d+)?$/.test(timeString)) {
    const seconds = parseFloat(timeString)
    return !isNaN(seconds) && seconds >= 0 ? seconds : 0
  }

  return 0
}



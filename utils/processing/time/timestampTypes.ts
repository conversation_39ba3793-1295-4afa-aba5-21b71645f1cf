// 时间戳对齐功能类型定义

/**
 * 时间戳数据类型枚举
 */
export type TimestampDataType = 'word_level' | 'sentence_level' | 'plain_text'

/**
 * 输入时间戳数据接口
 */
export interface TimestampData {
  id?: string | number
  text: string
  start?: number | string // 秒数或时间字符串
  end?: number | string // 秒数或时间字符串
  speaker?: string
  confidence?: number
}

/**
 * 目标翻译数据接口
 */
export interface TranslationData {
  id?: string | number
  source: string
  translation: string
  index?: number
}

/**
 * 数据结构信息
 */
export interface DataStructureInfo {
  totalRecords: number
  hasTimeStamps: boolean
  timeStampFormat: string
  averageTextLength: number
  estimatedType: TimestampDataType
  confidence: number
}

/**
 * 类型检测结果
 */
export interface DetectionResult {
  type: TimestampDataType
  confidence: number
  dataStructure: DataStructureInfo
  recommendations: string[]
  warnings: string[]
}

/**
 * 验证结果接口
 */
export interface ValidationResult {
  isValid: boolean
  errors: string[]
  warnings: string[]
  suggestions: string[]
}

import type { TimestampData, DetectionResult, DataStructureInfo, TimestampDataType } from './timestampTypes'
import { detectTimestampFormat } from './timeDetector'

/**
 * 检测时间戳数据类型
 */
export function detectTimestampType(data: TimestampData[]): DetectionResult {
  if (!data || data.length === 0) {
    return {
      type: 'plain_text',
      confidence: 0,
      dataStructure: createEmptyDataStructure(),
      recommendations: ['请提供有效的数据'],
      warnings: ['数据为空'],
    }
  }

  const dataStructure = analyzeDataStructure(data)
  const detectionResults = performTypeDetection(data, dataStructure)

  return {
    type: detectionResults.type,
    confidence: detectionResults.confidence,
    dataStructure,
    recommendations: generateRecommendations(detectionResults.type, dataStructure),
    warnings: generateWarnings(data, dataStructure),
  }
}

/**
 * 分析数据结构
 */
export function analyzeDataStructure(data: TimestampData[]): DataStructureInfo {
  const totalRecords = data.length
  const hasTimeStamps = data.some((item) => item.start !== undefined || item.end !== undefined)

  // 分析时间戳格式
  let timeStampFormat = 'none'
  if (hasTimeStamps) {
    const timestampItem = data.find((item) => item.start !== undefined)
    if (timestampItem?.start !== undefined) {
      timeStampFormat = detectTimestampFormat(timestampItem.start)
    }
  }

  // 计算平均文本长度
  const averageTextLength = data.reduce((sum, item) => sum + item.text.length, 0) / totalRecords

  // 估算类型和置信度
  const { estimatedType, confidence } = estimateTypeAndConfidence(data, hasTimeStamps, averageTextLength)

  return {
    totalRecords,
    hasTimeStamps,
    timeStampFormat,
    averageTextLength,
    estimatedType,
    confidence,
  }
}

/**
 * 执行类型检测
 */
export function performTypeDetection(data: TimestampData[], dataStructure: DataStructureInfo) {
  const { hasTimeStamps, averageTextLength, totalRecords } = dataStructure

  if (!hasTimeStamps) {
    return { type: 'plain_text' as TimestampDataType, confidence: 0.9 }
  }

  // 词级检测特征
  const wordLevelIndicators = [
    averageTextLength < 20, // 短文本
    totalRecords > 50, // 数据量大
    data.some((item) => item.text.split(' ').length <= 3), // 单词或短语
  ]

  // 句子级检测特征
  const sentenceLevelIndicators = [
    averageTextLength > 30, // 长文本
    totalRecords < 100, // 适中数据量
    data.some((item) => item.text.includes('.') || item.text.includes('。')), // 包含句号
  ]

  const wordLevelScore = wordLevelIndicators.filter(Boolean).length
  const sentenceLevelScore = sentenceLevelIndicators.filter(Boolean).length

  if (wordLevelScore > sentenceLevelScore) {
    return { type: 'word_level' as TimestampDataType, confidence: wordLevelScore / 3 }
  } else if (sentenceLevelScore > wordLevelScore) {
    return { type: 'sentence_level' as TimestampDataType, confidence: sentenceLevelScore / 3 }
  } else {
    // 默认句子级，置信度中等
    return { type: 'sentence_level' as TimestampDataType, confidence: 0.6 }
  }
}

/**
 * 估算类型和置信度
 */
export function estimateTypeAndConfidence(data: TimestampData[], hasTimeStamps: boolean, averageTextLength: number) {
  if (!hasTimeStamps) {
    return { estimatedType: 'plain_text' as TimestampDataType, confidence: 0.9 }
  }

  if (averageTextLength < 15 && data.length > 100) {
    return { estimatedType: 'word_level' as TimestampDataType, confidence: 0.8 }
  }

  if (averageTextLength > 50 && data.length < 50) {
    return { estimatedType: 'sentence_level' as TimestampDataType, confidence: 0.8 }
  }

  return { estimatedType: 'sentence_level' as TimestampDataType, confidence: 0.6 }
}

/**
 * 生成建议
 */
export function generateRecommendations(type: TimestampDataType, dataStructure: DataStructureInfo): string[] {
  const recommendations: string[] = []

  switch (type) {
    case 'word_level':
      recommendations.push('建议使用字符级精确匹配算法')
      recommendations.push('确保文本预处理一致性')
      break
    case 'sentence_level':
      recommendations.push('建议使用句子级智能映射算法')
      recommendations.push('可以尝试相似度匹配提高准确性')
      break
    case 'plain_text':
      recommendations.push('需要手动设置时间戳或提供参考时间信息')
      recommendations.push('建议根据文本长度估算时间分配')
      break
  }

  if (dataStructure.confidence < 0.7) {
    recommendations.push('数据特征不明显，建议手动确认数据类型')
  }

  return recommendations
}

/**
 * 生成警告
 */
export function generateWarnings(data: TimestampData[], dataStructure: DataStructureInfo): string[] {
  const warnings: string[] = []

  if (dataStructure.totalRecords < 5) {
    warnings.push('数据量过少，可能影响检测准确性')
  }

  if (!dataStructure.hasTimeStamps) {
    warnings.push('数据中没有时间戳信息')
  }

  const missingTimestamps = data.filter((item) => item.start === undefined || item.end === undefined).length
  if (missingTimestamps > 0) {
    warnings.push(`有${missingTimestamps}条记录缺少完整时间戳`)
  }

  return warnings
}

/**
 * 创建空数据结构
 */
export function createEmptyDataStructure(): DataStructureInfo {
  return {
    totalRecords: 0,
    hasTimeStamps: false,
    timeStampFormat: 'none',
    averageTextLength: 0,
    estimatedType: 'plain_text',
    confidence: 0,
  }
}

import { describe, it, expect } from 'vitest'
import { reindexSubtitles } from '../subtitleProcessor'
import type { Subtitle } from '~/types/subtitle'

// 扩展接口用于测试额外字段
interface SubtitleWithExtra extends Subtitle {
  extra: string
}

describe('Subtitle Processor', () => {
  it('应该对空数组返回空数组', () => {
    const result = reindexSubtitles([])
    expect(result).toEqual([])
  })

  it('应该对单个字幕正确编号', () => {
    const input: Subtitle[] = [
      {
        uuid: 'test-uuid-1',
        id: 99,
        startTime: '00:00:00,000',
        endTime: '00:00:01,000',
        text: 'hello',
        translationText: '',
      },
    ]
    const result = reindexSubtitles(input)
    expect(result).toEqual([
      {
        uuid: 'test-uuid-1',
        id: 1,
        startTime: '00:00:00,000',
        endTime: '00:00:01,000',
        text: 'hello',
        translationText: '',
      },
    ])
  })

  it('应该对多个字幕重新编号', () => {
    const input: Subtitle[] = [
      {
        uuid: 'test-uuid-1',
        id: 5,
        startTime: '00:00:00,000',
        endTime: '00:00:01,000',
        text: 'a',
        translationText: '',
      },
      {
        uuid: 'test-uuid-2',
        id: 8,
        startTime: '00:00:02,000',
        endTime: '00:00:03,000',
        text: 'b',
        translationText: '',
      },
      {
        uuid: 'test-uuid-3',
        id: 12,
        startTime: '00:00:04,000',
        endTime: '00:00:05,000',
        text: 'c',
        translationText: '',
      },
    ]
    const result = reindexSubtitles(input)
    expect(result).toEqual([
      {
        uuid: 'test-uuid-1',
        id: 1,
        startTime: '00:00:00,000',
        endTime: '00:00:01,000',
        text: 'a',
        translationText: '',
      },
      {
        uuid: 'test-uuid-2',
        id: 2,
        startTime: '00:00:02,000',
        endTime: '00:00:03,000',
        text: 'b',
        translationText: '',
      },
      {
        uuid: 'test-uuid-3',
        id: 3,
        startTime: '00:00:04,000',
        endTime: '00:00:05,000',
        text: 'c',
        translationText: '',
      },
    ])
  })

  it('应该不改变其他字段', () => {
    const input: SubtitleWithExtra[] = [
      {
        uuid: 'test-uuid-1',
        id: 1,
        startTime: '00:00:00,000',
        endTime: '00:00:01,000',
        text: 'x',
        translationText: '',
        extra: 'foo',
      },
      {
        uuid: 'test-uuid-2',
        id: 2,
        startTime: '00:00:02,000',
        endTime: '00:00:03,000',
        text: 'y',
        translationText: '',
        extra: 'bar',
      },
    ]
    const result = reindexSubtitles(input)
    expect((result[0] as SubtitleWithExtra).extra).toBe('foo')
    expect((result[1] as SubtitleWithExtra).extra).toBe('bar')
  })
})

import { describe, it, expect } from 'vitest'
import { generateSrt } from '../srtGenerator'
import type { Subtitle } from '~/types/subtitle'

describe('SRT Generator', () => {
  it('应该正确生成包含文本和翻译的SRT内容', () => {
    const subtitles: Subtitle[] = [
      {
        uuid: '1',
        id: 1,
        startTime: '00:00:01,000',
        endTime: '00:00:04,000',
        text: '你好',
        translationText: 'Hello'
      },
      {
        uuid: '2',
        id: 2,
        startTime: '00:00:05,000',
        endTime: '00:00:07,000',
        text: '世界',
        translationText: ''
      },
      {
        uuid: '3',
        id: 3,
        startTime: '00:00:08,000',
        endTime: '00:00:10,000',
        text: '',
        translationText: 'World'
      }
    ]
    const result = generateSrt(subtitles)
    const lines = result.split(/\r?\n/).filter(Boolean)
    expect(lines).toEqual([
      '1',
      '00:00:01,000 --> 00:00:04,000',
      '你好',
      'Hello',
      '2',
      '00:00:05,000 --> 00:00:07,000',
      '世界',
      '3',
      '00:00:08,000 --> 00:00:10,000',
      'World'
    ])
  })

  it('应该处理空数组', () => {
    expect(generateSrt([])).toBe('')
  })
})
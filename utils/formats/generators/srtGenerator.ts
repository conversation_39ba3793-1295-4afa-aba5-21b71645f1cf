import type { Subtitle } from '~/types/subtitle'
import { map } from 'lodash-es'

/**
 * 将 Subtitle[] 生成 SRT 文件内容
 */
export function generateSrt(subtitles: Subtitle[]): string {
  return map(subtitles, (sub, idx) => {
    const lines = [(idx + 1).toString(), `${sub.startTime} --> ${sub.endTime}`]
    if (sub.text) {
      lines.push(sub.text)
    }
    if (sub.translationText) {
      lines.push(sub.translationText)
    }
    lines.push('')
    return lines.join('\n')
  }).join('\n')
}

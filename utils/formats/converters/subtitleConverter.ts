import { v4 as uuidv4 } from 'uuid'
import Decimal from 'decimal.js-light'
import { filter, isEmpty, map, forEach } from 'lodash-es'
import type { LrcLine, LrcParseResult } from '~/types/lrcTypes'
import { parseSrtTime } from '~/utils/processing/time/timeParser'
import { formatTimeToString } from '~/utils/processing/time/timeFormatter'
import type { Subtitle } from '~/types/subtitle'
import { DEFAULT_SUBTITLE_DURATION, END_TIME_ADJUSTMENT } from '~/common/constants'

/**
 * 将 LRC 歌词行数组转换为 SRT 字幕对象数组。
 * 每个字幕对象包含原文 (text)，并可选地包含翻译文本 (translationText)。
 * 每个字幕对象拥有唯一递增的 id。
 */
export function lrcToSrt(lrcLines: LrcLine[]): Subtitle[] {
  // 初始化字幕数组
  const srtSubtitles: Subtitle[] = []
  // nextId 用于为每个字幕对象分配唯一递增的 id
  let nextId = 1

  forEach(lrcLines, (line, index) => {
    // 计算当前行的结束时间：优先使用 translationTime，其次下一个 LrcLine 的 time，否则默认+5秒
    let endTime: number
    if (line.translationTime !== undefined && line.translationTime > line.time) {
      endTime = line.translationTime
    } else {
      const nextLine = lrcLines[index + 1]
      endTime = nextLine ? nextLine.time : new Decimal(line.time).plus(DEFAULT_SUBTITLE_DURATION).toNumber()
    }
    // 格式化时间为 SRT 字幕格式 (HH:MM:SS,mmm)
    const startTimeStr = formatTimeToString(line.time).replace('.', ',')
    const endTimeStr = formatTimeToString(endTime).replace('.', ',')

    // 原文和翻译始终同一块输出
    if (line.text || line.translationText) {
      const srtSubtitle: Subtitle = {
        uuid: uuidv4(),
        id: nextId++,
        startTime: startTimeStr,
        endTime: endTimeStr,
        text: line.text || '',
        translationText: line.translationText || '',
      }
      srtSubtitles.push(srtSubtitle)
    }
  })

  return srtSubtitles
}

// subTitle 转 lrc
export function subtitleToLrc(subtitles: Subtitle[]): LrcParseResult {
  const lrcLines: LrcLine[] = []

  forEach(subtitles, (subtitle) => {
    const startTimeInSeconds = parseSrtTime(subtitle.startTime)
    if (startTimeInSeconds === null) return

    // 创建 LrcLine 对象
    const lrcLine: LrcLine = {
      time: startTimeInSeconds,
      text: subtitle.text || '',
      translationText: subtitle.translationText || undefined,
    }

    // 只有当有内容时才添加到数组中
    if (lrcLine.text || lrcLine.translationText) {
      lrcLines.push(lrcLine)
    }
  })

  // 返回 LrcParseResult 格式
  return {
    lrcLines,
    plainTextLines: [], // SRT 转换时通常没有纯文本行
    metadata: {}, // SRT 转换时通常没有元数据
  }
}

// 将字幕数组转换为 VTT 字符串
export const subtitlesToVttString = (subtitles: Subtitle[]): string => {
  if (isEmpty(subtitles)) {
    return ''
  }

  // 先过滤掉没有内容的字幕 text 和 translationText
  const filteredSubtitles = filter(subtitles, (sub) => sub.text.trim() !== '' || sub.translationText.trim() !== '')

  // 转换的时候将 , 替换为 . 换行拼接原文和翻译，如果没有翻译则只拼接原文(就不需要换行符了)
  const vttString = map(filteredSubtitles, (sub, index, array) => {
    // 将逗号转换成句号
    const startTime = sub.startTime.replace(',', '.')

    let vttEndTimeString: string
    if (index < array.length - 1 && sub.endTime === array[index + 1].startTime) {
      // 微调 endTime：如果本条的结束时间和下一条的开始时间完全相同，为避免重叠，将结束时间提前0.1秒
      const endSec = parseSrtTime(sub.endTime) // 解析结束时间为秒
      const startSec = parseSrtTime(sub.startTime) // 解析开始时间为秒
      if (typeof endSec === 'number' && typeof startSec === 'number') {
        let adjusted = endSec - END_TIME_ADJUSTMENT // 结束时间减去0.1秒
        if (adjusted < startSec) adjusted = startSec // 防止调整后早于开始时间，若小于则强制等于开始时间
        vttEndTimeString = formatTimeToString(adjusted).replace(',', '.') // 格式化为VTT时间字符串（毫秒分隔符为点）
      } else {
        // 如果解析失败，降级处理：仅做格式替换
        vttEndTimeString = sub.endTime.replace(',', '.')
      }
    } else {
      vttEndTimeString = sub.endTime.replace(',', '.')
    }

    const text = sub.text.trim()
    const translationText = sub.translationText.trim()

    const vttText = translationText !== '' ? `${text}\n${translationText}` : text

    return `${startTime} --> ${vttEndTimeString}\n${vttText}\n`
  }).join('\n')

  return `WEBVTT\n\n${vttString}`
}

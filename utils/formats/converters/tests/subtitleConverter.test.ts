import { describe, it, expect } from 'vitest'
import { lrcToSrt, subtitleToLrc, subtitlesToVttString } from '../subtitleConverter'
import type { LrcLine } from '~/types/lrcTypes'
import type { Subtitle } from '~/types/subtitle'

// 测试数据工厂函数
function createLrcLine(overrides: Partial<LrcLine> = {}): LrcLine {
  return {
    time: 10.5,
    text: 'Test text',
    translationText: 'Test translation',
    translationTime: 12.0,
    ...overrides,
  }
}

function createSubtitle(overrides: Partial<Subtitle> = {}): Subtitle {
  return {
    uuid: 'test-uuid',
    id: 1,
    startTime: '00:00:10,500',
    endTime: '00:00:12,000',
    text: 'Test text',
    translationText: 'Test translation',
    ...overrides,
  }
}

describe('Subtitle Converter', () => {
  describe('lrcToSrt', () => {
    it('应该将单个 LrcLine 转换为 Subtitle', () => {
      const lrcLines = [createLrcLine()]
      const result = lrcToSrt(lrcLines)

      expect(result).toHaveLength(1)
      expect(result[0]).toMatchObject({
        id: 1,
        startTime: '00:00:10,500',
        endTime: '00:00:12,000',
        text: 'Test text',
        translationText: 'Test translation',
      })
      expect(result[0].uuid).toMatch(/^[0-9a-f]{8}-[0-9a-f]{4}-[0-9a-f]{4}-[0-9a-f]{4}-[0-9a-f]{12}$/i)
    })

    it('应该正确处理 id 递增', () => {
      const lrcLines = [createLrcLine({ time: 10, text: 'First' }), createLrcLine({ time: 20, text: 'Second' })]
      const result = lrcToSrt(lrcLines)

      expect(result).toHaveLength(2)
      expect(result[0].id).toBe(1)
      expect(result[1].id).toBe(2)
    })

    it('应该使用 translationTime 作为结束时间当其大于 time 时', () => {
      const lrcLines = [
        createLrcLine({
          time: 10,
          translationTime: 15,
        }),
      ]
      const result = lrcToSrt(lrcLines)

      expect(result[0].endTime).toBe('00:00:15,000')
    })

    it('应该使用下一行的 time 作为结束时间', () => {
      const lrcLines = [
        createLrcLine({ time: 10, translationTime: 8 }), // translationTime 小于 time
        createLrcLine({ time: 20 }),
      ]
      const result = lrcToSrt(lrcLines)

      expect(result[0].endTime).toBe('00:00:20,000')
    })

    it('应该为最后一行默认添加 2 秒', () => {
      const lrcLines = [createLrcLine({ time: 10, translationTime: 8 })]
      const result = lrcToSrt(lrcLines)

      expect(result[0].endTime).toBe('00:00:12,000')
    })

    it('应该处理无翻译文本的情况', () => {
      const lrcLines = [
        createLrcLine({
          text: 'Only original',
          translationText: undefined,
        }),
      ]
      const result = lrcToSrt(lrcLines)

      expect(result[0].text).toBe('Only original')
      expect(result[0].translationText).toBe('')
    })

    it('应该处理无原文的情况', () => {
      const lrcLines = [
        createLrcLine({
          text: '',
          translationText: 'Only translation',
        }),
      ]
      const result = lrcToSrt(lrcLines)

      expect(result[0].text).toBe('')
      expect(result[0].translationText).toBe('Only translation')
    })

    it('应该跳过没有任何文本内容的行', () => {
      const lrcLines = [createLrcLine({ text: '', translationText: '' }), createLrcLine({ text: 'Valid text' })]
      const result = lrcToSrt(lrcLines)

      expect(result).toHaveLength(1)
      expect(result[0].text).toBe('Valid text')
    })

    it('应该处理空数组输入', () => {
      const result = lrcToSrt([])
      expect(result).toEqual([])
    })

    it('应该处理时间为 0 的情况', () => {
      const lrcLines = [createLrcLine({ time: 0 })]
      const result = lrcToSrt(lrcLines)

      expect(result[0].startTime).toBe('00:00:00,000')
    })
  })

  describe('subtitleToLrc', () => {
    it('应该将单个 Subtitle 转换为 LrcParseResult', () => {
      const subtitles = [createSubtitle()]
      const result = subtitleToLrc(subtitles)

      expect(result.lrcLines).toHaveLength(1)
      expect(result.lrcLines[0]).toMatchObject({
        time: 10.5,
        text: 'Test text',
        translationText: 'Test translation',
      })
      expect(result.plainTextLines).toEqual([])
      expect(result.metadata).toEqual({})
    })

    it('应该正确解析不同的时间格式', () => {
      const subtitles = [createSubtitle({ startTime: '00:01:30,250' }), createSubtitle({ startTime: '01:00:00,000' })]
      const result = subtitleToLrc(subtitles)

      expect(result.lrcLines[0].time).toBeCloseTo(90.25)
      expect(result.lrcLines[1].time).toBe(3600)
    })

    it('应该跳过无效时间格式的字幕', () => {
      const subtitles = [createSubtitle({ startTime: 'invalid-time' }), createSubtitle({ startTime: '00:00:10,000', text: 'Valid' })]
      const result = subtitleToLrc(subtitles)

      expect(result.lrcLines).toHaveLength(1)
      expect(result.lrcLines[0].text).toBe('Valid')
    })

    it('应该处理空文本内容', () => {
      const subtitles = [createSubtitle({ text: '', translationText: '' }), createSubtitle({ text: 'Valid', translationText: '' })]
      const result = subtitleToLrc(subtitles)

      expect(result.lrcLines).toHaveLength(1)
      expect(result.lrcLines[0].text).toBe('Valid')
    })

    it('应该处理翻译文本为 undefined 的情况', () => {
      const subtitles = [createSubtitle({ translationText: '' })]
      const result = subtitleToLrc(subtitles)

      expect(result.lrcLines[0].translationText).toBeUndefined()
    })

    it('应该处理空数组输入', () => {
      const result = subtitleToLrc([])

      expect(result.lrcLines).toEqual([])
      expect(result.plainTextLines).toEqual([])
      expect(result.metadata).toEqual({})
    })

    it('应该返回正确的 LrcParseResult 结构', () => {
      const subtitles = [createSubtitle()]
      const result = subtitleToLrc(subtitles)

      expect(result).toHaveProperty('lrcLines')
      expect(result).toHaveProperty('plainTextLines')
      expect(result).toHaveProperty('metadata')
      expect(Array.isArray(result.lrcLines)).toBe(true)
      expect(Array.isArray(result.plainTextLines)).toBe(true)
      expect(typeof result.metadata).toBe('object')
    })
  })

  describe('subtitlesToVttString', () => {
    it('应该生成正确的 VTT 格式字符串', () => {
      const subtitles = [
        createSubtitle({
          startTime: '00:00:10,500',
          endTime: '00:00:12,000',
          text: 'Hello',
          translationText: '你好',
        }),
      ]
      const result = subtitlesToVttString(subtitles)

      expect(result).toBe('WEBVTT\n\n00:00:10.500 --> 00:00:12.000\nHello\n你好\n')
    })

    it('应该将逗号转换为点号', () => {
      const subtitles = [
        createSubtitle({
          startTime: '00:00:10,500',
          endTime: '00:00:12,750',
        }),
      ]
      const result = subtitlesToVttString(subtitles)

      expect(result).toContain('00:00:10.500 --> 00:00:12.750')
    })

    it('应该处理仅有原文的情况', () => {
      const subtitles = [
        createSubtitle({
          text: 'Only original',
          translationText: '',
        }),
      ]
      const result = subtitlesToVttString(subtitles)

      expect(result).toContain('Only original\n')
      expect(result).not.toContain('\n\n\n')
    })

    it('应该处理仅有翻译的情况', () => {
      const subtitles = [
        createSubtitle({
          text: '',
          translationText: 'Only translation',
        }),
      ]
      const result = subtitlesToVttString(subtitles)

      expect(result).toContain('\nOnly translation\n')
    })

    it('应该处理时间重叠的情况', () => {
      const subtitles = [
        createSubtitle({
          startTime: '00:00:10,000',
          endTime: '00:00:12,000',
          text: 'First',
        }),
        createSubtitle({
          startTime: '00:00:12,000', // 与上一条结束时间相同
          endTime: '00:00:14,000',
          text: 'Second',
        }),
      ]
      const result = subtitlesToVttString(subtitles)

      // 第一条的结束时间应该被调整为 11.99
      expect(result).toContain('00:00:10.000 --> 00:00:11.990')
      expect(result).toContain('00:00:12.000 --> 00:00:14.000')
    })

    it('应该防止调整后的时间早于开始时间', () => {
      const subtitles = [
        createSubtitle({
          startTime: '00:00:10,000',
          endTime: '00:00:10,005', // 很短的持续时间
        }),
        createSubtitle({
          startTime: '00:00:10,005',
          endTime: '00:00:12,000',
        }),
      ]
      const result = subtitlesToVttString(subtitles)

      // 调整后不应早于开始时间
      expect(result).toContain('00:00:10.000 --> 00:00:10.000')
    })

    it('应该处理时间解析失败的情况', () => {
      const subtitles = [
        createSubtitle({
          startTime: 'invalid-time',
          endTime: 'invalid-end',
          text: 'Test',
        }),
        createSubtitle({
          startTime: 'invalid-time',
          endTime: '00:00:12,000',
        }),
      ]
      const result = subtitlesToVttString(subtitles)

      // 应该降级处理，仅做格式替换
      expect(result).toContain('invalid-time --> invalid-end')
      expect(result).toContain('invalid-time --> 00:00:12.000')
    })

    it('应该过滤掉空内容的字幕', () => {
      const subtitles = [
        createSubtitle({ text: '  ', translationText: '  ' }), // 仅空格
        createSubtitle({ text: 'Valid', translationText: '' }),
      ]
      const result = subtitlesToVttString(subtitles)

      expect(result).toContain('Valid')
      expect(result).not.toContain('00:00:10.500 --> 00:00:12.000\n  \n  \n')
    })

    it('应该处理空数组输入', () => {
      const result = subtitlesToVttString([])
      expect(result).toBe('')
    })

    it('应该处理多行字幕的正确拼接', () => {
      const subtitles = [
        createSubtitle({
          text: 'Line 1',
          translationText: '第一行',
        }),
        createSubtitle({
          text: 'Line 2',
          translationText: '第二行',
        }),
      ]
      const result = subtitlesToVttString(subtitles)

      expect(result).toContain('Line 1\n第一行\n')
      expect(result).toContain('Line 2\n第二行\n')
      expect(result.split('\n\n')).toHaveLength(3) // WEBVTT + 2 字幕
    })
  })
})

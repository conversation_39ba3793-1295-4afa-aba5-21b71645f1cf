import { filter, forEach } from 'lodash-es'
import { v4 as uuidv4 } from 'uuid'
import type { Subtitle } from "~/types/subtitle";
import { classifySubtitleLines } from '~/utils/processing/text/textProcessor'
import { normalizeVttTime } from '~/utils/processing/time/timeFormatter'

/**
 * 解析 vtt 文件内容为 Subtitle[]
 */
export function parseVtt(vttText: string): Subtitle[] {
  const blocks = vttText.split(/\r?\n\r?\n/)
  const subtitles: Subtitle[] = []
  let id = 1
  
  forEach(blocks, (block) => {
    const lines = filter(block.split(/\r?\n/), Boolean)
    if (lines.length < 2) return
    
    // 跳过WEBVTT头部块
    if (lines.some(line => line.trim() === 'WEBVTT')) return
    
    // 查找时间戳行
    let timeLineIdx = -1
    for (let i = 0; i < lines.length; i++) {
      if (lines[i].includes('-->')) {
        timeLineIdx = i
        break
      }
    }
    
    if (timeLineIdx === -1) return
    
    const timeLine = lines[timeLineIdx]
    // 支持各种宽松格式的时间戳
    const timeMatch = timeLine.match(/^\s*([\d:.,]+)\s*-->\s*([\d:.,]+)\s*$/)
    if (!timeMatch) return
    
    const [, startRaw, endRaw] = timeMatch
    const startTime = normalizeVttTime(startRaw.trim())
    const endTime = normalizeVttTime(endRaw.trim())
    
    const subtitleLines = lines.slice(timeLineIdx + 1)
    const { text, translationText } = classifySubtitleLines(subtitleLines)
    
    subtitles.push({
      uuid: uuidv4(),
      id: id++,
      startTime,
      endTime,
      text,
      translationText,
    })
  })
  
  return subtitles
}
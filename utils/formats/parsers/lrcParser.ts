import Decimal from 'decimal.js-light'
import type { LrcMetadata, LrcParseResult, LrcLine } from '~/types/lrcTypes'
import { includes, filter, find, forEach, sortBy } from 'lodash-es'
import { parseTimeStringToSeconds } from '../../processing/time/timeParser'
import { normalizeVttTime } from '../../processing/time/timeFormatter'
import { detectTextLanguage } from '../../processing/text/textProcessor'

// LrcData 接口定义
export interface LrcData {
  id: string | number
  title: string
  artist: string
  duration?: number
  syncedLyrics: string
}

// 检查是否为已知的元数据键
export function isKnownMetadataKey(key: string): key is keyof LrcMetadata {
  const validKeys: ReadonlyArray<keyof LrcMetadata> = ['ti', 'ar', 'al', 'by', 'offset']
  return includes(validKeys, key as keyof LrcMetadata)
}

// 解析元数据标签
export function parseMetadata(line: string): [string, string] | null {
  const match = line.match(/^\[([a-z]+):(.*?)\]$/i)
  if (!match) {
    return null
  }
  return [match[1].toLowerCase(), match[2].trim()]
}

// 智能判断原文和翻译行顺序的函数
export function determineOriginalTranslationOrder(
  tempLines: Array<{
    time: number
    text: string
    translationText?: string
    isCombined: boolean
    isTranslation: boolean
    needsPairing: boolean
    originalLineIndex: number
  }>,
): typeof tempLines {
  // 按时间戳分组需要配对的行
  const groupsByTime = new Map<number, typeof tempLines>()

  forEach(tempLines, (line) => {
    if (line.needsPairing) {
      const timeKey = line.time
      if (!groupsByTime.has(timeKey)) {
        groupsByTime.set(timeKey, [])
      }
      groupsByTime.get(timeKey)!.push(line)
    }
  })

  // 为每个时间组重新设置isTranslation标志
  groupsByTime.forEach((group) => {
    if (group.length >= 2) {
      // 按原始行索引排序，确保顺序正确
      const sortedGroup = sortBy(group, 'originalLineIndex')

      // 智能判断：使用统一的语言检测函数
      const hasChinese = sortedGroup.some((line) => detectTextLanguage(line.text) === 'chinese')
      const hasNonChinese = sortedGroup.some((line) => detectTextLanguage(line.text) === 'english')

      if (hasChinese && hasNonChinese) {
        // 混合语言情况：中文为翻译，非中文为原文
        sortedGroup.forEach((line) => {
          line.isTranslation = detectTextLanguage(line.text) === 'chinese'
        })
      } else {
        // 同种语言或其他情况：按出现顺序，第一行为原文
        sortedGroup.forEach((line, index) => {
          line.isTranslation = index > 0
        })
      }
    } else if (group.length === 1) {
      // 单行使用统一的语言检测
      group[0].isTranslation = detectTextLanguage(group[0].text) === 'chinese'
    }
  })

  return tempLines
}

// 主解析函数
export function parseLrc(lrcContent: string): LrcParseResult {
  const lines = lrcContent.split('\n')
  const metadata: Record<string, string | number> = {}
  const plainTextLines: string[] = []
  // 临时存储解析出的行，包含更多信息用于后续处理
  const tempLines: {
    time: number
    text: string
    translationText?: string
    isCombined: boolean
    isTranslation: boolean
    needsPairing: boolean
    originalLineIndex: number
  }[] = []
  // 时间标签正则表达式 [mm:ss.xx] 或 [mm:ss:xx] 或 [mm:ss.xxx]
  const timeRegex = /\s*\[\s*(\d{2})\s*:\s*(\d{2})\s*(?:[:.]\s*(\d{1,3})\s*)?\]\s*/g

  forEach(lines, (line, lineIndex) => {
    const trimmed = line.trim()
    if (!trimmed) return
    // 处理元数据
    const metaData = parseMetadata(trimmed)
    if (metaData) {
      const [key, value] = metaData
      if (isKnownMetadataKey(key)) {
        if (key === 'offset') {
          metadata[key] = Number(value)
        } else {
          metadata[key] = value
        }
      }
      return
    }
    // 提取所有时间标签
    const timeMatches = [...trimmed.matchAll(timeRegex)]
    if (timeMatches.length === 0) {
      plainTextLines.push(trimmed)
      return
    }
    // 移除所有时间标签，得到歌词文本
    const rawText = trimmed.replace(timeRegex, '').trim()
    // 合并行检测：寻找英文和中文文本的分界点
    let splitIndex = -1

    // 优化的分割逻辑：先快速找到中文字符位置，再验证语言分割
    // 使用与 detectTextLanguage 一致的中文字符范围
    const chineseCharRegex = /[\u4e00-\u9fff\u3400-\u4dbf\u3000-\u303f]/

    // 寻找第一个中文字符的位置
    for (let i = 1; i < rawText.length; i++) {
      if (chineseCharRegex.test(rawText[i])) {
        const beforeText = rawText.substring(0, i).trim()
        const afterText = rawText.substring(i).trim()

        if (beforeText && afterText) {
          // 使用统一的语言检测函数验证分割是否合理
          const beforeLang = detectTextLanguage(beforeText)
          const afterLang = detectTextLanguage(afterText)

          // 如果前面是英文，后面是中文，则找到有效分割点
          if (beforeLang === 'english' && afterLang === 'chinese') {
            splitIndex = i
            break
          }
        }
      }
    }

    if (splitIndex > 0) {
      // 前面有原文，后面有翻译
      const originalPart = rawText.substring(0, splitIndex).trim()
      const translationPart = rawText.substring(splitIndex).trim()
      if (originalPart && translationPart) {
        forEach(timeMatches, (match) => {
          const minutes = match[1]
          const seconds = match[2]
          let milliseconds = '000'
          if (match[3]) {
            milliseconds = match[3].padEnd(3, '0')
          }
          const offset = typeof metadata.offset === 'number' ? new Decimal(metadata.offset).dividedBy(1000) : new Decimal(0)
          const time = new Decimal(minutes)
            .times(60)
            .plus(new Decimal(seconds))
            .plus(new Decimal(milliseconds).dividedBy(1000))
            .plus(offset)
            .toNumber()
          tempLines.push({
            time,
            text: originalPart,
            translationText: translationPart,
            isCombined: true,
            isTranslation: false,
            needsPairing: false,
            originalLineIndex: lineIndex,
          })
        })
        return
      }
    }
    // 分离行处理
    forEach(timeMatches, (match) => {
      const minutes = match[1]
      const seconds = match[2]
      let milliseconds = '000'
      if (match[3]) {
        milliseconds = match[3].padEnd(3, '0')
      }
      const offset = typeof metadata.offset === 'number' ? new Decimal(metadata.offset).dividedBy(1000) : new Decimal(0)
      const time = new Decimal(minutes)
        .times(60)
        .plus(new Decimal(seconds))
        .plus(new Decimal(milliseconds).dividedBy(1000))
        .plus(offset)
        .toNumber()
      tempLines.push({
        time,
        text: rawText,
        isCombined: false,
        isTranslation: false, // 临时设置，将由determineOriginalTranslationOrder函数重新判断
        needsPairing: true,
        originalLineIndex: lineIndex,
      })
    })
  })

  // 智能判断原文和翻译行的顺序
  const updatedTempLines = determineOriginalTranslationOrder(tempLines)

  // 分离行配对逻辑
  const linesToPair = filter(updatedTempLines, (line) => line.needsPairing)
  const potentialPairs: {
    original: (typeof linesToPair)[0]
    translation: (typeof linesToPair)[0]
    distance: number
  }[] = []
  forEach(linesToPair, (originalLine, i) => {
    if (!originalLine.isTranslation) {
      // 优先查找时间戳完全相等的翻译行
      const sameTimeTranslation = find(linesToPair, (line) => line.isTranslation && line.time === originalLine.time)
      if (sameTimeTranslation) {
        potentialPairs.push({
          original: originalLine,
          translation: sameTimeTranslation,
          distance: 0,
        })
        return
      }
      // 否则查找后续最近的翻译行
      forEach(linesToPair.slice(i + 1), (translationLine) => {
        if (translationLine.isTranslation && translationLine.originalLineIndex > originalLine.originalLineIndex) {
          potentialPairs.push({
            original: originalLine,
            translation: translationLine,
            distance: translationLine.originalLineIndex - originalLine.originalLineIndex,
          })
        }
      })
    }
  })
  const sortedPairs = sortBy(potentialPairs, 'distance')
  const matchedOriginalIndices = new Set<number>()
  const matchedTranslationIndices = new Set<number>()
  const pairMap = new Map<number, { text: string; time: number }>()
  forEach(sortedPairs, (pair) => {
    if (
      !matchedOriginalIndices.has(pair.original.originalLineIndex) &&
      !matchedTranslationIndices.has(pair.translation.originalLineIndex)
    ) {
      matchedOriginalIndices.add(pair.original.originalLineIndex)
      matchedTranslationIndices.add(pair.translation.originalLineIndex)
      pairMap.set(pair.original.originalLineIndex, { text: pair.translation.text, time: pair.translation.time })
    }
  })
  // 构建最终 LrcLine 结果
  const sortedTempLines = sortBy(updatedTempLines, [(a) => a.time, (a) => a.originalLineIndex])
  const finalLrcLines: LrcLine[] = []
  forEach(sortedTempLines, (line) => {
    if (line.isCombined) {
      finalLrcLines.push({
        time: line.time,
        text: line.text,
        translationText: line.translationText,
        translationTime: line.time, // 合并行，原文和翻译时间一致
      })
    } else if (line.needsPairing && !line.isTranslation) {
      const translationPair = pairMap.get(line.originalLineIndex)
      finalLrcLines.push({
        time: line.time,
        text: line.text,
        ...(translationPair ? { translationText: translationPair.text, translationTime: translationPair.time } : {}),
      })
    }
    // 分离行的翻译行已被配对，不单独输出
  })
  return {
    lrcLines: finalLrcLines,
    plainTextLines,
    metadata,
  }
}

// lrc 数据项转换
export function normalizeLrcItem(item: unknown): LrcData | null {
  if (!item || typeof item !== 'object') return null
  const obj = item as Record<string, unknown>
  const id = typeof obj.id === 'string' || typeof obj.id === 'number' ? obj.id : ''
  const title = typeof obj.trackName === 'string' ? obj.trackName : typeof obj.name === 'string' ? obj.name : ''
  const artist = typeof obj.artistName === 'string' ? obj.artistName : ''
  const duration = typeof obj.duration === 'number' ? obj.duration : undefined
  const syncedLyrics = typeof obj.syncedLyrics === 'string' ? obj.syncedLyrics : ''
  return {
    id,
    title,
    artist,
    duration,
    syncedLyrics,
  }
}

// 批量转换
export function normalizeLyricsList(list: unknown[]): LrcData[] {
  if (!Array.isArray(list)) return []
  return list.map(normalizeLrcItem).filter(Boolean) as LrcData[]
}

// 改进的时间戳提取函数
export function extractTimestamp(line: string): { timestamp: string; content: string } | null {
  // 支持多种时间戳格式：[mm:ss.xxx]、[mm:ss:xxx]、[h:mm:ss.xxx] 等
  const timeRegex = /^\s*\[(\d{1,2}:\d{2}(?:[:.]\d{1,3})?)\]\s*(.*)/
  const match = line.match(timeRegex)

  if (!match) return null

  return {
    timestamp: match[1],
    content: match[2],
  }
}

/**
 * 合并歌词
 * @param lyric 原歌词
 * @param tlyric 翻译歌词
 * @returns 合并后的歌词
 */
export function mergeLyrics(lyric: string, tlyric: string): string {
  // 输入验证
  if (typeof lyric !== 'string' || typeof tlyric !== 'string') {
    return ''
  }

  // 处理空输入
  if (!lyric.trim() && !tlyric.trim()) return ''
  if (!lyric.trim()) return tlyric
  if (!tlyric.trim()) return lyric

  // 分割歌词为行数组（过滤空行）
  const lyricLines = lyric.split('\n').filter((line) => line.trim() !== '')
  const tlyricLines = tlyric.split('\n').filter((line) => line.trim() !== '')

  // 提取元数据（时间戳行之前的所有非时间戳行）
  const metadata: string[] = []
  let tlyricIndex = 0 // 🔧 修复：改为 let，避免无限循环

  // 从翻译歌词中提取元数据
  while (tlyricIndex < tlyricLines.length) {
    const currentLine = tlyricLines[tlyricIndex]
    const timestampResult = extractTimestamp(currentLine)

    if (timestampResult) {
      // 遇到时间戳行，停止提取元数据
      break
    }

    metadata.push(currentLine)
    tlyricIndex++ // 🔧 修复：正确递增索引
  }

  // 从翻译歌词数组中移除已提取的元数据
  tlyricLines.splice(0, tlyricIndex)

  // 使用 Map 存储时间戳与歌词的映射
  interface LyricEntry {
    lyric?: string
    tlyric?: string
  }

  const timeMap = new Map<string, LyricEntry>()

  // 处理原歌词
  lyricLines.forEach((line) => {
    const result = extractTimestamp(line)
    if (result) {
      const normalizedTimestamp = normalizeVttTime(result.timestamp, '.')
      const existing = timeMap.get(normalizedTimestamp) || {}
      timeMap.set(normalizedTimestamp, { ...existing, lyric: line })
    }
  })

  // 处理翻译歌词
  tlyricLines.forEach((line) => {
    const result = extractTimestamp(line)
    if (result) {
      const normalizedTimestamp = normalizeVttTime(result.timestamp, '.')
      const existing = timeMap.get(normalizedTimestamp) || {}
      timeMap.set(normalizedTimestamp, { ...existing, tlyric: line })
    }
  })

  // 按时间顺序排序时间戳
  const sortedTimes = Array.from(timeMap.keys()).sort((a, b) => {
    const timeA = parseTimeStringToSeconds(a)
    const timeB = parseTimeStringToSeconds(b)

    // 处理 NaN 的情况
    if (isNaN(timeA) && isNaN(timeB)) return 0
    if (isNaN(timeA)) return 1
    if (isNaN(timeB)) return -1

    return timeA - timeB
  })

  // 生成合并后的歌词数组
  const mergedLines: string[] = [...metadata] // 先添加元数据

  sortedTimes.forEach((time) => {
    const entry = timeMap.get(time)
    if (!entry) return // 🔧 修复：防止 undefined 访问

    const { lyric, tlyric } = entry
    if (lyric) mergedLines.push(lyric) // 原歌词行
    if (tlyric) mergedLines.push(tlyric) // 翻译歌词行
  })

  return mergedLines.join('\n')
}

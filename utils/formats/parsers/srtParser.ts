import { filter, forEach } from 'lodash-es'
import { v4 as uuidv4 } from 'uuid'
import type { Subtitle } from '~/types/subtitle'
import { classifySubtitleLines } from '~/utils/processing/text/textProcessor'

/**
 * 解析 SRT 文件内容为 Subtitle[]
 */
export function parseSrt(srtText: string): Subtitle[] {
  const blocks = srtText.split(/\r?\n\r?\n/)
  const subtitles: Subtitle[] = []
  let id = 1
  forEach(blocks, (block) => {
    const lines = filter(block.split(/\r?\n/), Boolean)
    if (lines.length < 2) return
    // 第一行为编号（可选）
    let timeLineIdx = 0
    if (/^\d+$/.test(lines[0])) timeLineIdx = 1
    const timeLine = lines[timeLineIdx]
    const match = timeLine.match(/^(\d{2}:\d{2}:\d{2},\d{3})\s*-->\s*(\d{2}:\d{2}:\d{2},\d{3})$/)
    if (!match) return
    const [_, startTime, endTime] = match
    const subtitleLines = lines.slice(timeLineIdx + 1)
    const { text, translationText } = classifySubtitleLines(subtitleLines)
    subtitles.push({
      uuid: uuidv4(),
      id: id++,
      startTime,
      endTime,
      text,
      translationText,
    })
  })
  return subtitles
}

import { describe, it, expect } from 'vitest'
import { parseLrc } from '~/utils/formats/parsers/lrcParser'

describe('解析 LRC', () => {
  it('应正确解析简单的 LRC 格式', () => {
    const lrcSimple = '[00:10.00]Line 1\n[00:12.50]Line 2'
    const result = parseLrc(lrcSimple)

    expect(result.metadata).toEqual({})
    expect(result.plainTextLines).toEqual([])
    expect(result.lrcLines).toHaveLength(2)
    expect(result.lrcLines[0]).toEqual({
      time: 10,
      text: 'Line 1',
      translationText: undefined,
      translationTime: undefined,
    })
    expect(result.lrcLines[1]).toEqual({
      time: 12.5,
      text: 'Line 2',
      translationText: undefined,
      translationTime: undefined,
    })
  })

  it('应正确解析元数据', () => {
    const lrcWithMetadata = '[ar:Artist]\n[ti:Title]\n[al:Album]\n[by:Creator]\n[offset:1000]\n[00:10.00]Line 1'
    const result = parseLrc(lrcWithMetadata)

    expect(result.metadata).toEqual({
      ar: 'Artist',
      ti: 'Title',
      al: 'Album',
      by: 'Creator',
      offset: 1000,
    })
    expect(result.lrcLines).toHaveLength(1)
    expect(result.lrcLines[0].time).toBeCloseTo(11)
  })

  it('应正确地将翻译与原行配对', () => {
    const lrcWithTranslation = '[00:20.00]Original 1\n[00:20.00]翻译 1\n[00:25.00]Original 2\n[00:26.00]Some other line\n[00:25.00]翻译 2'
    const result = parseLrc(lrcWithTranslation)

    expect(result.lrcLines).toHaveLength(3)

    // First pair
    expect(result.lrcLines[0]).toEqual({
      time: 20,
      text: 'Original 1',
      translationText: '翻译 1',
      translationTime: 20,
    })

    // Second pair
    expect(result.lrcLines[1]).toEqual({
      time: 25,
      text: 'Original 2',
      translationText: '翻译 2',
      translationTime: 25,
    })

    // Unpaired line
    expect(result.lrcLines[2]).toEqual({
      time: 26,
      text: 'Some other line',
      translationText: undefined,
      translationTime: undefined,
    })
  })

  it('应处理空输入', () => {
    const result = parseLrc('')
    expect(result.metadata).toEqual({})
    expect(result.lrcLines).toEqual([])
    expect(result.plainTextLines).toEqual([])
  })

  it('应处理无效输入', () => {
    const lrcInvalid = '[invalid tag]\nJust text\n[00:aa.bb]Invalid time'
    const result = parseLrc(lrcInvalid)

    expect(result.metadata).toEqual({})
    expect(result.plainTextLines).toContain('Just text')
    expect(result.lrcLines).toHaveLength(0)
  })

  it('应处理复杂的混合内容', () => {
    const lrcComplex = `[ti:Complex Song]
[ar:Test Artist]
[offset:500]
[00:10.00]Line with <00:10.200>word <00:10.400>timing
[00:12.00]English line
[00:12.00]中文翻译
Some plain text
[invalid]Another plain text
[00:15.00]Final line`

    const result = parseLrc(lrcComplex)

    expect(result.metadata).toEqual({
      ti: 'Complex Song',
      ar: 'Test Artist',
      offset: 500,
    })

    expect(result.plainTextLines).toEqual(['Some plain text', '[invalid]Another plain text'])

    expect(result.lrcLines).toHaveLength(3)

    // Check word timing line
    expect(result.lrcLines[0]).toEqual({
      time: 10.5, // Including offset
      text: 'Line with <00:10.200>word <00:10.400>timing',
      translationText: undefined,
      translationTime: undefined,
    })

    // Check translation pair
    expect(result.lrcLines[1]).toEqual({
      time: 12.5, // Including offset
      text: 'English line',
      translationText: '中文翻译',
      translationTime: 12.5,
    })

    // Check final line
    expect(result.lrcLines[2]).toEqual({
      time: 15.5, // Including offset
      text: 'Final line',
      translationText: undefined,
      translationTime: undefined,
    })
  })
})

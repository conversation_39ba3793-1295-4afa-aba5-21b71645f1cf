import { describe, it, expect } from 'vitest'
import { parseVtt } from '../vttParser'

describe('VTT Parser', () => {
  it('应该正确解析基础 VTT', () => {
    const vtt = `WEBVTT\n\n00:00:01.000 --> 00:00:03.000\nHello world\n\n00:00:04.000 --> 00:00:06.000\nSecond line`
    const result = parseVtt(vtt)
    expect(result.length).toBe(2)
    expect(result[0].text).toBe('Hello world')
    expect(result[0].translationText).toBe('')
    expect(result[0].startTime).toBe('00:00:01,000')
    expect(result[0].endTime).toBe('00:00:03,000')
    expect(result[1].text).toBe('Second line')
    expect(result[1].translationText).toBe('')
  })

  it('应该跳过 WEBVTT 头部', () => {
    const vtt = `WEBVTT\n\n00:00:01.000 --> 00:00:03.000\nTest`
    const result = parseVtt(vtt)
    expect(result.length).toBe(1)
    expect(result[0].text).toBe('Test')
  })

  it('支持中英文分行', () => {
    const vtt = `00:00:01.000 --> 00:00:03.000\nHello\n你好`
    const result = parseVtt(vtt)
    expect(result.length).toBe(1)
    expect(result[0].text).toBe('Hello')
    expect(result[0].translationText).toBe('你好')
  })

  it('遇到无效时间戳应跳过', () => {
    const vtt = `00:00:01.000 --> ???\nHello`
    const result = parseVtt(vtt)
    expect(result.length).toBe(0)
  })

  it('支持多种时间戳格式', () => {
    const vtt = `00:01:00,000 --> 00:01:02,000\nLine1\n\n00:01:03.500 --> 00:01:05.000\nLine2`
    const result = parseVtt(vtt)
    expect(result.length).toBe(2)
    expect(result[0].startTime).toBe('00:01:00,000')
    expect(result[1].startTime).toBe('00:01:03,500')
  })
})

import { describe, it, expect } from 'vitest'
import {
  parseLrc,
  mergeLyrics,
  normalizeLrcItem,
  normalizeLyricsList,
  extractTimestamp,
  isKnownMetadataKey,
  parseMetadata,
} from '../lrcParser'

describe('LRC Parser', () => {
  describe('parseLrc', () => {
    it('parseLrc 能正确解析带时间戳和翻译的歌词', () => {
      const lrc = '[ar:歌手]\n[ti:歌名]\n[00:01.00]你好\n[00:02.00]世界\n[00:01.00]Hello\n[00:02.00]World'
      const result = parseLrc(lrc)
      expect(result.metadata.ar).toBe('歌手')
      expect(result.metadata.ti).toBe('歌名')
      expect(result.lrcLines).toEqual(expect.anything())
      expect(result.lrcLines.length).toBe(2)
      expect(result.lrcLines[0].text).toBe('Hello')
      expect(result.lrcLines[0].translationText).toBe('你好')
      expect(result.lrcLines[1].text).toBe('World')
      expect(result.lrcLines[1].translationText).toBe('世界')
    })

    it('应正确解析合并行格式（原文+翻译在同一行）', () => {
      const lrc = '[00:10.00]Hello 你好\n[00:15.00]World 世界'
      const result = parseLrc(lrc)

      expect(result.lrcLines).toHaveLength(2)
      expect(result.lrcLines[0]).toEqual({
        time: 10,
        text: 'Hello',
        translationText: '你好',
        translationTime: 10,
      })
      expect(result.lrcLines[1]).toEqual({
        time: 15,
        text: 'World',
        translationText: '世界',
        translationTime: 15,
      })
    })

    it('应正确处理带offset的时间戳', () => {
      const lrc = '[offset:500]\n[00:10.00]Test line'
      const result = parseLrc(lrc)

      expect(result.metadata.offset).toBe(500)
      expect(result.lrcLines[0].time).toBeCloseTo(10.5) // 10 + 0.5 offset
    })

    it('应正确处理多种时间戳格式', () => {
      const lrc = '[00:10.00]Format 1\n[00:11:30]Format 2\n[00:12.123]Format 3'
      const result = parseLrc(lrc)

      expect(result.lrcLines).toHaveLength(3)
      expect(result.lrcLines[0].time).toBe(10)
      expect(result.lrcLines[1].time).toBe(11.3) // 11:30 = 11 + 30/100 = 11.3 (30是厘秒)
      expect(result.lrcLines[2].time).toBeCloseTo(12.123)
    })

    it('应将无效行归类为纯文本', () => {
      const lrc = '[ar:Artist]\nPlain text line\n[invalid tag]\n[00:10.00]Valid line'
      const result = parseLrc(lrc)

      expect(result.plainTextLines).toEqual(['Plain text line', '[invalid tag]'])
      expect(result.lrcLines).toHaveLength(1)
      expect(result.metadata.ar).toBe('Artist')
    })

    it('应处理空内容', () => {
      const result = parseLrc('')
      expect(result.lrcLines).toEqual([])
      expect(result.plainTextLines).toEqual([])
      expect(result.metadata).toEqual({})
    })

    it('应处理仅包含空白行的内容', () => {
      const result = parseLrc('   \n\n  \t  \n')
      expect(result.lrcLines).toEqual([])
      expect(result.plainTextLines).toEqual([])
      expect(result.metadata).toEqual({})
    })

    it('应正确处理同一时间戳的多个翻译语言', () => {
      const lrc = '[00:10.00]English\n[00:10.00]中文\n[00:10.00]日本語'
      const result = parseLrc(lrc)

      expect(result.lrcLines).toHaveLength(1)
      expect(result.lrcLines[0].text).toBe('English')
      expect(result.lrcLines[0].translationText).toBe('中文')
    })
  })

  describe('mergeLyrics', () => {
    it('mergeLyrics 能合并原文和翻译歌词', () => {
      const lyric = '[00:01.00]你好\n[00:02.00]世界'
      const tlyric = '[00:01.00]Hello\n[00:02.00]World'
      const merged = mergeLyrics(lyric, tlyric)
      expect(merged).toContain('你好')
      expect(merged).toContain('Hello')
      expect(merged).toContain('世界')
      expect(merged).toContain('World')
    })

    it('mergeLyrics 空输入返回空字符串', () => {
      expect(mergeLyrics('', '')).toBe('')
      expect(mergeLyrics('   ', '   ')).toBe('')
    })

    it('应处理单方面为空的情况', () => {
      const lyric = '[00:01.00]Hello'
      expect(mergeLyrics(lyric, '')).toBe(lyric)
      expect(mergeLyrics('', lyric)).toBe(lyric)
    })

    it('应正确处理非字符串输入', () => {
      expect(mergeLyrics(null as unknown as string, 'test')).toBe('')
      expect(mergeLyrics('test', undefined as unknown as string)).toBe('')
      expect(mergeLyrics(123 as unknown as string, 'test')).toBe('')
    })

    it('应正确提取和保留元数据', () => {
      const lyric = '[00:01.00]Hello\n[00:02.00]World'
      const tlyric = '[ar:Artist]\n[ti:Title]\n[00:01.00]你好\n[00:02.00]世界'
      const merged = mergeLyrics(lyric, tlyric)

      expect(merged).toContain('[ar:Artist]')
      expect(merged).toContain('[ti:Title]')
      expect(merged).toContain('Hello')
      expect(merged).toContain('你好')
    })

    it('应按时间顺序排序合并的歌词', () => {
      const lyric = '[00:05.00]Third\n[00:01.00]First'
      const tlyric = '[00:03.00]Second'
      const merged = mergeLyrics(lyric, tlyric)
      const lines = merged.split('\n')

      // 应该按时间顺序排列
      expect(lines.find((line) => line.includes('First'))).toBeTruthy()
      expect(lines.find((line) => line.includes('Second'))).toBeTruthy()
      expect(lines.find((line) => line.includes('Third'))).toBeTruthy()
    })
  })

  describe('normalizeLrcItem', () => {
    it('normalizeLrcItem 能正确转换对象', () => {
      const obj = { id: 1, trackName: '歌名', artistName: '歌手', duration: 123, syncedLyrics: '歌词内容' }
      const data = normalizeLrcItem(obj)
      expect(data).toEqual({ id: 1, title: '歌名', artist: '歌手', duration: 123, syncedLyrics: '歌词内容' })
    })

    it('应处理使用name而非trackName的对象', () => {
      const obj = { id: 2, name: '歌名2', artistName: '歌手2', syncedLyrics: '歌词2' }
      const data = normalizeLrcItem(obj)
      expect(data).toEqual({ id: 2, title: '歌名2', artist: '歌手2', duration: undefined, syncedLyrics: '歌词2' })
    })

    it('应处理缺少字段的对象', () => {
      const obj = { id: 'test' }
      const data = normalizeLrcItem(obj)
      expect(data).toEqual({ id: 'test', title: '', artist: '', duration: undefined, syncedLyrics: '' })
    })

    it('应处理null和undefined输入', () => {
      expect(normalizeLrcItem(null)).toBeNull()
      expect(normalizeLrcItem(undefined)).toBeNull()
      expect(normalizeLrcItem('string')).toBeNull()
      expect(normalizeLrcItem(123)).toBeNull()
    })

    it('应处理空对象', () => {
      const data = normalizeLrcItem({})
      expect(data).toEqual({ id: '', title: '', artist: '', duration: undefined, syncedLyrics: '' })
    })

    it('应处理字段类型不匹配的情况', () => {
      const obj = {
        id: true, // 非string/number
        trackName: 123, // 非string
        artistName: null, // 非string
        duration: 'invalid', // 非number
        syncedLyrics: {}, // 非string
      }
      const data = normalizeLrcItem(obj)
      expect(data).toEqual({
        id: '',
        title: '',
        artist: '',
        duration: undefined,
        syncedLyrics: '',
      })
    })
  })

  describe('normalizeLyricsList', () => {
    it('normalizeLyricsList 能批量转换', () => {
      const arr = [
        { id: 1, trackName: 'A', artistName: 'B', syncedLyrics: 'X' },
        { id: 2, name: 'C', artistName: 'D', syncedLyrics: 'Y' },
      ]
      const list = normalizeLyricsList(arr)
      expect(list.length).toBe(2)
      expect(list[0].title).toBe('A')
      expect(list[1].title).toBe('C')
    })

    it('应过滤掉无效的数组项', () => {
      const arr = [
        { id: 1, trackName: 'Valid', artistName: 'Artist', syncedLyrics: 'Lyrics' },
        null,
        undefined,
        'invalid',
        { id: 2, trackName: 'Valid2', artistName: 'Artist2', syncedLyrics: 'Lyrics2' },
      ]
      const list = normalizeLyricsList(arr as unknown[])
      expect(list).toHaveLength(2)
      expect(list[0].title).toBe('Valid')
      expect(list[1].title).toBe('Valid2')
    })

    it('应处理非数组输入', () => {
      expect(normalizeLyricsList(null as unknown as unknown[])).toEqual([])
      expect(normalizeLyricsList(undefined as unknown as unknown[])).toEqual([])
      expect(normalizeLyricsList('string' as unknown as unknown[])).toEqual([])
      expect(normalizeLyricsList({} as unknown as unknown[])).toEqual([])
    })

    it('应处理空数组', () => {
      expect(normalizeLyricsList([])).toEqual([])
    })
  })

  describe('extractTimestamp', () => {
    it('应正确提取标准时间戳格式', () => {
      const result = extractTimestamp('[00:10.50]Hello world')
      expect(result).toEqual({
        timestamp: '00:10.50',
        content: 'Hello world',
      })
    })

    it('应支持多种时间戳格式', () => {
      const testCases = [
        { input: '[00:10.50]Test', expected: { timestamp: '00:10.50', content: 'Test' } },
        { input: '[00:10:50]Test', expected: { timestamp: '00:10:50', content: 'Test' } },
        { input: '[00:10.123]Test', expected: { timestamp: '00:10.123', content: 'Test' } },
        { input: '[1:10.50]Test', expected: { timestamp: '1:10.50', content: 'Test' } },
        { input: '  [00:10.50]  Test with spaces  ', expected: { timestamp: '00:10.50', content: 'Test with spaces  ' } },
      ]

      testCases.forEach(({ input, expected }) => {
        expect(extractTimestamp(input)).toEqual(expected)
      })
    })

    it('应处理空内容的时间戳', () => {
      const result = extractTimestamp('[00:10.50]')
      expect(result).toEqual({
        timestamp: '00:10.50',
        content: '',
      })
    })

    it('应拒绝无效格式', () => {
      const invalidCases = [
        'No timestamp here',
        '[invalid]Content',
        '[00:aa.bb]Invalid time',
        'Just text',
        '',
        '[00:10.50', // 缺少结束括号
        '00:10.50]Content', // 缺少开始括号
      ]

      invalidCases.forEach((input) => {
        expect(extractTimestamp(input)).toBeNull()
      })
    })

    it('应处理含有特殊字符的内容', () => {
      const result = extractTimestamp('[00:10.50]Content with [brackets] and (parentheses)')
      expect(result).toEqual({
        timestamp: '00:10.50',
        content: 'Content with [brackets] and (parentheses)',
      })
    })
  })

  describe('isKnownMetadataKey', () => {
    it('应识别有效的元数据键', () => {
      const validKeys = ['ti', 'ar', 'al', 'by', 'offset']
      validKeys.forEach((key) => {
        expect(isKnownMetadataKey(key)).toBe(true)
      })
    })

    it('应拒绝无效的元数据键', () => {
      const invalidKeys = ['invalid', 'unknown', 'test', '', 'TI', 'AR']
      invalidKeys.forEach((key) => {
        expect(isKnownMetadataKey(key)).toBe(false)
      })
    })

    it('应处理空字符串和特殊字符', () => {
      expect(isKnownMetadataKey('')).toBe(false)
      expect(isKnownMetadataKey('ti ')).toBe(false) // 带空格
      expect(isKnownMetadataKey(' ti')).toBe(false) // 带空格
      expect(isKnownMetadataKey('ti!')).toBe(false) // 带特殊字符
    })
  })

  describe('parseMetadata', () => {
    it('应正确解析有效的元数据标签', () => {
      const testCases = [
        { input: '[ti:歌曲标题]', expected: ['ti', '歌曲标题'] },
        { input: '[ar:艺术家]', expected: ['ar', '艺术家'] },
        { input: '[al:专辑名]', expected: ['al', '专辑名'] },
        { input: '[by:编者]', expected: ['by', '编者'] },
        { input: '[offset:1000]', expected: ['offset', '1000'] },
      ]

      testCases.forEach(({ input, expected }) => {
        expect(parseMetadata(input)).toEqual(expected)
      })
    })

    it('应处理大小写不敏感的键', () => {
      expect(parseMetadata('[TI:Title]')).toEqual(['ti', 'Title'])
      expect(parseMetadata('[AR:Artist]')).toEqual(['ar', 'Artist'])
      expect(parseMetadata('[Al:Album]')).toEqual(['al', 'Album'])
    })

    it('应正确处理带空格的值', () => {
      expect(parseMetadata('[ti: 歌曲标题 ]')).toEqual(['ti', '歌曲标题'])
      expect(parseMetadata('[ar:  艺术家  ]')).toEqual(['ar', '艺术家'])
    })

    it('应处理空值', () => {
      expect(parseMetadata('[ti:]')).toEqual(['ti', ''])
      expect(parseMetadata('[ar:   ]')).toEqual(['ar', ''])
    })

    it('应拒绝无效格式', () => {
      const invalidCases = [
        'ti:歌曲标题', // 缺少方括号
        '[ti歌曲标题]', // 缺少冒号
        '[ti:]歌曲标题', // 方括号外有内容
        'ti:歌曲标题]', // 缺少开始方括号
        '[ti:歌曲标题', // 缺少结束方括号
        '', // 空字符串
        '[]', // 空方括号
        '[:]', // 只有冒号
      ]

      invalidCases.forEach((input) => {
        expect(parseMetadata(input)).toBeNull()
      })
    })

    it('应处理特殊字符和多种语言', () => {
      expect(parseMetadata('[ti:Song Title with (parentheses)]')).toEqual(['ti', 'Song Title with (parentheses)'])
      expect(parseMetadata('[ar:歌手 & Artist]')).toEqual(['ar', '歌手 & Artist'])
      expect(parseMetadata('[al:Album - Special Edition]')).toEqual(['al', 'Album - Special Edition'])
    })
  })
})

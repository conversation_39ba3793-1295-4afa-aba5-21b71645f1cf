import { describe, it, expect } from 'vitest'
import fs from 'node:fs'
import path from 'node:path'
import { parseLrc } from '~/utils/formats/parsers/lrcParser'

const lrcDir = path.resolve(__dirname, '../fixtures/lrc_files')
const lrcFiles = fs.readdirSync(lrcDir).filter((f) => f.endsWith('.lrc'))

describe('LRC Parser Batch Tests from .local/lrc_files', () => {
  lrcFiles.forEach((filename) => {
    const filePath = path.join(lrcDir, filename)
    const content = fs.readFileSync(filePath, 'utf-8')
    const isErrorFile = filename.startsWith('error_')

    it(`should parse ${filename} as expected`, () => {
      let result
      try {
        result = parseLrc(content)
      } catch {
        result = undefined
      }
      expect(result).toBeDefined()
      if (!result) return
      expect(result.lrcLines).toBeInstanceOf(Array)
      expect(result.plainTextLines).toBeInstanceOf(Array)
      expect(result.metadata).toBeTypeOf('object')

      if (filename === 'error_encoding.lrc') {
        // 特例：内容有效，应有3行歌词且无plainText
        expect(result.lrcLines.length).toBe(3)
        expect(result.plainTextLines.length).toBe(0)
      } else if (filename === 'error_mixed.lrc') {
        expect(result.lrcLines.length).toBe(2)
        expect(result.plainTextLines.length).toBe(3)
      } else if (filename === 'error_structure_lyric_no_tag.lrc') {
        expect(result.lrcLines.length).toBe(2)
        expect(result.plainTextLines.length).toBe(1)
      } else if (filename === 'error_time_tag_out_of_range.lrc') {
        expect(result.lrcLines.length).toBe(3)
        expect(result.plainTextLines.length).toBe(0)
      } else if (isErrorFile) {
        // 其他错误文件：断言大部分内容被归入 plainTextLines
        expect(result.lrcLines.length).toBeLessThanOrEqual(1)
        expect(result.plainTextLines.length).toBeGreaterThanOrEqual(1)
      } else {
        // 正常文件：应有歌词行
        expect(result.lrcLines.length).toBeGreaterThanOrEqual(1)
      }
    })
  })
})

import { describe, it, expect, vi, beforeEach } from 'vitest'
import { parseSrt } from '../srtParser'

// Mock uuid 模块
vi.mock('uuid', () => ({
  v4: vi.fn(() => 'mocked-uuid-1234'),
}))

// Mock classifySubtitleLines 函数
vi.mock('~/utils/processing/text/textProcessor', () => ({
  classifySubtitleLines: vi.fn((lines: string[]) => {
    // 模拟分类逻辑：英文放入text，中文放入translationText
    const text: string[] = []
    const translationText: string[] = []

    lines.forEach((line) => {
      const englishChars = (line.match(/[a-zA-Z]/g) || []).length
      const chineseChars = (line.match(/[\u4e00-\u9fff\u3400-\u4dbf\u3000-\u303f]/g) || []).length
      const totalLetterChars = englishChars + chineseChars

      if (totalLetterChars === 0) {
        translationText.push(line)
      } else {
        const englishRatio = englishChars / totalLetterChars
        if (englishRatio >= 0.5) {
          text.push(line)
        } else {
          translationText.push(line)
        }
      }
    })

    return {
      text: text.join('\n'),
      translationText: translationText.join('\n'),
    }
  }),
}))

describe('parseSrt 函数测试', () => {
  beforeEach(() => {
    vi.clearAllMocks()
  })

  describe('基础功能', () => {
    it('应该正确解析标准SRT格式（带编号）', () => {
      const srtText = `1
00:00:01,000 --> 00:00:03,000
Hello world

2
00:00:04,000 --> 00:00:06,000
This is a test`

      const result = parseSrt(srtText)

      expect(result).toHaveLength(2)
      expect(result[0]).toEqual({
        uuid: 'mocked-uuid-1234',
        id: 1,
        startTime: '00:00:01,000',
        endTime: '00:00:03,000',
        text: 'Hello world',
        translationText: '',
      })
      expect(result[1]).toEqual({
        uuid: 'mocked-uuid-1234',
        id: 2,
        startTime: '00:00:04,000',
        endTime: '00:00:06,000',
        text: 'This is a test',
        translationText: '',
      })
    })

    it('应该正确解析无编号SRT格式', () => {
      const srtText = `00:00:01,000 --> 00:00:03,000
Hello world

00:00:04,000 --> 00:00:06,000
This is a test`

      const result = parseSrt(srtText)

      expect(result).toHaveLength(2)
      expect(result[0]).toEqual({
        uuid: 'mocked-uuid-1234',
        id: 1,
        startTime: '00:00:01,000',
        endTime: '00:00:03,000',
        text: 'Hello world',
        translationText: '',
      })
      expect(result[1]).toEqual({
        uuid: 'mocked-uuid-1234',
        id: 2,
        startTime: '00:00:04,000',
        endTime: '00:00:06,000',
        text: 'This is a test',
        translationText: '',
      })
    })

    it('应该正确解析多行字幕文本', () => {
      const srtText = `1
00:00:01,000 --> 00:00:03,000
First line
Second line
Third line`

      const result = parseSrt(srtText)

      expect(result).toHaveLength(1)
      expect(result[0]).toEqual({
        uuid: 'mocked-uuid-1234',
        id: 1,
        startTime: '00:00:01,000',
        endTime: '00:00:03,000',
        text: 'First line\nSecond line\nThird line',
        translationText: '',
      })
    })
  })

  describe('字幕分类', () => {
    it('应该正确识别英文字幕', () => {
      const srtText = `1
00:00:01,000 --> 00:00:03,000
Hello world
How are you?`

      const result = parseSrt(srtText)

      expect(result).toHaveLength(1)
      expect(result[0].text).toBe('Hello world\nHow are you?')
      expect(result[0].translationText).toBe('')
    })

    it('应该正确识别中文字幕', () => {
      const srtText = `1
00:00:01,000 --> 00:00:03,000
你好世界
今天天气不错`

      const result = parseSrt(srtText)

      expect(result).toHaveLength(1)
      expect(result[0].text).toBe('')
      expect(result[0].translationText).toBe('你好世界\n今天天气不错')
    })

    it('应该正确处理中英文混合字幕', () => {
      const srtText = `1
00:00:01,000 --> 00:00:03,000
Hello world
你好世界
This is a test
这是一个测试`

      const result = parseSrt(srtText)

      expect(result).toHaveLength(1)
      expect(result[0].text).toBe('Hello world\nThis is a test')
      expect(result[0].translationText).toBe('你好世界\n这是一个测试')
    })
  })

  describe('时间格式测试', () => {
    it('应该正确解析标准时间格式', () => {
      const srtText = `1
00:00:01,000 --> 00:00:03,000
Test text`

      const result = parseSrt(srtText)

      expect(result).toHaveLength(1)
      expect(result[0].startTime).toBe('00:00:01,000')
      expect(result[0].endTime).toBe('00:00:03,000')
    })

    it('应该正确处理时间轴前后的空格', () => {
      const srtText = `1
00:00:01,000  -->  00:00:03,000
Test text`

      const result = parseSrt(srtText)

      expect(result).toHaveLength(1)
      expect(result[0].startTime).toBe('00:00:01,000')
      expect(result[0].endTime).toBe('00:00:03,000')
    })
  })

  describe('边界情况', () => {
    it('应该处理空字符串输入', () => {
      const result = parseSrt('')
      expect(result).toEqual([])
    })

    it('应该跳过无效格式块', () => {
      const srtText = `1
00:00:01,000 --> 00:00:03,000
Valid subtitle

Invalid block without timeline
Some text

2
00:00:04,000 --> 00:00:06,000
Another valid subtitle`

      const result = parseSrt(srtText)

      expect(result).toHaveLength(2)
      expect(result[0].text).toBe('Valid subtitle')
      expect(result[1].text).toBe('Another valid subtitle')
    })

    it('应该跳过仅包含编号的块', () => {
      const srtText = `1

2
00:00:01,000 --> 00:00:03,000
Valid subtitle`

      const result = parseSrt(srtText)

      expect(result).toHaveLength(1)
      expect(result[0].text).toBe('Valid subtitle')
    })

    it('应该处理时间格式错误的情况', () => {
      const srtText = `1
invalid-time-format
Some text

2
00:00:01,000 --> 00:00:03,000
Valid subtitle`

      const result = parseSrt(srtText)

      expect(result).toHaveLength(1)
      expect(result[0].text).toBe('Valid subtitle')
    })
  })

  describe('ID和UUID测试', () => {
    it('应该正确生成自增ID', () => {
      const srtText = `1
00:00:01,000 --> 00:00:03,000
First subtitle

2
00:00:04,000 --> 00:00:06,000
Second subtitle

3
00:00:07,000 --> 00:00:09,000
Third subtitle`

      const result = parseSrt(srtText)

      expect(result).toHaveLength(3)
      expect(result[0].id).toBe(1)
      expect(result[1].id).toBe(2)
      expect(result[2].id).toBe(3)
    })

    it('应该为每个字幕生成UUID', () => {
      const srtText = `1
00:00:01,000 --> 00:00:03,000
Test subtitle`

      const result = parseSrt(srtText)

      expect(result).toHaveLength(1)
      expect(result[0].uuid).toBe('mocked-uuid-1234')
      expect(typeof result[0].uuid).toBe('string')
    })
  })

  describe('复杂SRT文件综合测试', () => {
    it('应该正确处理包含各种格式的复杂SRT文件', () => {
      const srtText = `1
00:00:01,000 --> 00:00:03,000
Hello world
你好世界

2
00:00:04,000 --> 00:00:06,000
This is English text

3
00:00:07,000 --> 00:00:09,000
这是中文文本

invalid block

4
00:00:10,000 --> 00:00:12,000
Multi line English
Another English line
还有中文
更多中文内容`

      const result = parseSrt(srtText)

      expect(result).toHaveLength(4)

      // 第一个字幕：中英文混合
      expect(result[0].id).toBe(1)
      expect(result[0].text).toBe('Hello world')
      expect(result[0].translationText).toBe('你好世界')

      // 第二个字幕：纯英文
      expect(result[1].id).toBe(2)
      expect(result[1].text).toBe('This is English text')
      expect(result[1].translationText).toBe('')

      // 第三个字幕：纯中文
      expect(result[2].id).toBe(3)
      expect(result[2].text).toBe('')
      expect(result[2].translationText).toBe('这是中文文本')

      // 第四个字幕：多行中英文混合
      expect(result[3].id).toBe(4)
      expect(result[3].text).toBe('Multi line English\nAnother English line')
      expect(result[3].translationText).toBe('还有中文\n更多中文内容')
    })
  })
})

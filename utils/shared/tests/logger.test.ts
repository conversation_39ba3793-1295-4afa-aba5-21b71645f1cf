import { describe, it, expect, beforeEach, vi } from 'vitest'
import {
  TranslationLogger,
  translationLogger,
  Logger,
  NewTranslationLogger,
  TranslationService,
  logger,
  newTranslationLogger,
  translationService,
} from '~/utils/shared/logger'

describe('新架构测试', () => {
  describe('Logger类', () => {
    let testLogger: Logger

    beforeEach(() => {
      testLogger = new Logger()
      vi.spyOn(console, 'info').mockImplementation(() => {})
      vi.spyOn(console, 'warn').mockImplementation(() => {})
      vi.spyOn(console, 'error').mockImplementation(() => {})
    })

    it('应正确记录info日志', () => {
      testLogger.info('测试信息', { test: 'data' })

      const entries = testLogger.getEntries()
      expect(entries).toHaveLength(1)
      expect(entries[0]).toMatchObject({
        level: 'info',
        message: '测试信息',
        data: { test: 'data' },
      })
    })

    it('应正确记录warn日志', () => {
      testLogger.warn('测试警告')

      const entries = testLogger.getEntries()
      expect(entries[0].level).toBe('warn')
      expect(entries[0].message).toBe('测试警告')
    })

    it('应正确记录error日志', () => {
      testLogger.error('测试错误')

      const entries = testLogger.getEntries()
      expect(entries[0].level).toBe('error')
      expect(entries[0].message).toBe('测试错误')
    })

    it('应正确清除日志', () => {
      testLogger.info('测试')
      expect(testLogger.getEntries()).toHaveLength(1)

      testLogger.clear()
      expect(testLogger.getEntries()).toHaveLength(0)
    })

    it('应返回日志副本', () => {
      testLogger.info('测试')
      const entries = testLogger.getEntries()

      entries.push({ timestamp: '', level: 'info', message: 'fake' })
      expect(testLogger.getEntries()).toHaveLength(1)
    })
  })

  describe('NewTranslationLogger类', () => {
    let testTranslationLogger: NewTranslationLogger

    beforeEach(() => {
      testTranslationLogger = new NewTranslationLogger()
    })

    it('应正确创建翻译日志', () => {
      const entry = { source: '原文', translation: '译文', method: 'test' }
      const id = testTranslationLogger.log(entry)

      expect(id).toBeDefined()
      expect(typeof id).toBe('string')

      const logs = testTranslationLogger.getLogs()
      expect(logs).toHaveLength(1)
      expect(logs[0]).toMatchObject(entry)
      expect(logs[0].id).toBe(id)
    })

    it('应正确根据ID查找日志', () => {
      const id = testTranslationLogger.log({ source: '测试', translation: '翻译', method: 'test' })

      const found = testTranslationLogger.getLogById(id)
      expect(found).toBeDefined()
      expect(found?.id).toBe(id)

      const notFound = testTranslationLogger.getLogById('nonexistent')
      expect(notFound).toBeUndefined()
    })

    it('应正确清除日志', () => {
      testTranslationLogger.log({ source: '测试', translation: '翻译', method: 'test' })
      expect(testTranslationLogger.getLogs()).toHaveLength(1)

      testTranslationLogger.clear()
      expect(testTranslationLogger.getLogs()).toHaveLength(0)
    })

    it('应正确导出JSON', () => {
      const entry = { source: '测试', translation: '翻译', method: 'test' }
      testTranslationLogger.log(entry)

      const exported = testTranslationLogger.exportLogs()
      const parsed = JSON.parse(exported)

      expect(Array.isArray(parsed)).toBe(true)
      expect(parsed).toHaveLength(1)
      expect(parsed[0]).toMatchObject(entry)
    })
  })

  describe('TranslationService类', () => {
    let testLogger: Logger
    let testTranslationLogger: NewTranslationLogger
    let service: TranslationService

    beforeEach(() => {
      testLogger = new Logger()
      testTranslationLogger = new NewTranslationLogger()
      service = new TranslationService(testLogger, testTranslationLogger)

      vi.spyOn(console, 'info').mockImplementation(() => {})
      vi.spyOn(console, 'warn').mockImplementation(() => {})
      vi.spyOn(console, 'error').mockImplementation(() => {})
    })

    it('应正确委托翻译日志操作', () => {
      const entry = { source: '原文', translation: '译文', method: 'test' }
      const id = service.logTranslation(entry)

      expect(id).toBeDefined()
      expect(service.getTranslationLogs()).toHaveLength(1)
      expect(service.getTranslationById(id)).toMatchObject(entry)
    })

    it('应正确记录批次统计', () => {
      service.logBatchStatistics(10, 8, 2, 0.9, 5000)

      const systemLogs = service.getSystemLogs()
      expect(systemLogs).toHaveLength(1)
      expect(systemLogs[0]).toMatchObject({
        level: 'info',
        message: '批次统计',
      })
    })

    it('应正确记录翻译开始', () => {
      const task = { id: 'test-task' }
      service.logTranslationStart(task)

      const systemLogs = service.getSystemLogs()
      expect(systemLogs[0]).toMatchObject({
        level: 'info',
        message: '开始翻译',
        data: task,
      })
    })

    it('应正确记录重试尝试', () => {
      service.logRetryAttempt(1, 2, 1000)

      const systemLogs = service.getSystemLogs()
      expect(systemLogs[0]).toMatchObject({
        level: 'warn',
        message: '重试尝试',
      })
    })

    it('应正确清除所有日志', () => {
      service.logTranslation({ source: '测试', translation: '翻译', method: 'test' })
      service.logBatchStatistics(1, 1, 0, 1, 100)

      expect(service.getTranslationLogs()).toHaveLength(1)
      expect(service.getSystemLogs()).toHaveLength(1)

      service.clearAllLogs()

      expect(service.getTranslationLogs()).toHaveLength(0)
      expect(service.getSystemLogs()).toHaveLength(0)
    })
  })

  describe('默认实例', () => {
    beforeEach(() => {
      vi.spyOn(console, 'info').mockImplementation(() => {})
      vi.spyOn(console, 'warn').mockImplementation(() => {})
      vi.spyOn(console, 'error').mockImplementation(() => {})
    })

    it('应提供可用的默认实例', () => {
      expect(logger).toBeInstanceOf(Logger)
      expect(newTranslationLogger).toBeInstanceOf(NewTranslationLogger)
      expect(translationService).toBeInstanceOf(TranslationService)
    })

    it('默认实例应正常工作', () => {
      logger.info('测试')
      expect(logger.getEntries()).toHaveLength(1)

      const id = newTranslationLogger.log({ source: '测试', translation: '翻译', method: 'test' })
      expect(newTranslationLogger.getLogById(id)).toBeDefined()

      translationService.logBatchStatistics(1, 1, 0, 1, 100)
      expect(translationService.getSystemLogs().length).toBeGreaterThan(0)
    })
  })
})

describe('向后兼容性测试', () => {
  // 将原有的TranslationLogger测试移到这里以确保向后兼容性
  describe('TranslationLogger (已弃用)', () => {
    let logger: TranslationLogger

    beforeEach(() => {
      logger = new TranslationLogger()

      // Mock console methods to avoid test output
      vi.spyOn(console, 'info').mockImplementation(() => {})
      vi.spyOn(console, 'warn').mockImplementation(() => {})
      vi.spyOn(console, 'error').mockImplementation(() => {})
    })

    describe('核心日志功能', () => {
      it('应正确创建翻译日志并返回ID', () => {
        const entry = {
          source: '原文',
          translation: '译文',
          method: 'test-method',
          quality: 0.95,
        }

        const id = logger.log(entry)

        expect(id).toBeDefined()
        expect(typeof id).toBe('string')

        const logs = logger.getLogs()
        expect(logs).toHaveLength(1)
        expect(logs[0]).toMatchObject({
          id,
          source: entry.source,
          translation: entry.translation,
          method: entry.method,
          quality: entry.quality,
        })
        expect(logs[0].timestamp).toBeInstanceOf(Date)
      })

      it('应正确处理不带quality的翻译日志', () => {
        const entry = {
          source: '原文',
          translation: '译文',
          method: 'test-method',
        }

        logger.log(entry)
        const logs = logger.getLogs()

        expect(logs[0]).toMatchObject(entry)
        expect(logs[0].quality).toBeUndefined()
      })

      it('应正确创建info级别日志', () => {
        const message = '测试信息'
        const data = { test: 'data' }

        logger.info(message, data)

        const entries = logger.getLogEntries()
        expect(entries).toHaveLength(1)
        expect(entries[0]).toMatchObject({
          level: 'info',
          message,
          data,
        })
        expect(entries[0].timestamp).toBeDefined()
        expect(console.info).toHaveBeenCalledWith(`[TranslationLogger] ${message}`, data)
      })

      it('应正确创建warn级别日志', () => {
        const message = '测试警告'
        const data = { warning: 'test' }

        logger.warn(message, data)

        const entries = logger.getLogEntries()
        expect(entries[0]).toMatchObject({
          level: 'warn',
          message,
          data,
        })
        expect(console.warn).toHaveBeenCalledWith(`[TranslationLogger] ${message}`, data)
      })

      it('应正确创建error级别日志', () => {
        const message = '测试错误'
        const data = { error: 'test' }

        logger.error(message, data)

        const entries = logger.getLogEntries()
        expect(entries[0]).toMatchObject({
          level: 'error',
          message,
          data,
        })
        expect(console.error).toHaveBeenCalledWith(`[TranslationLogger] ${message}`, data)
      })

      it('应正确处理不带data的日志', () => {
        const message = '无数据日志'

        logger.info(message)

        const entries = logger.getLogEntries()
        expect(entries[0]).toMatchObject({
          level: 'info',
          message,
        })
        expect(entries[0].data).toBeUndefined()
        expect(console.info).toHaveBeenCalledWith(`[TranslationLogger] ${message}`, '')
      })
    })

    describe('特定业务日志方法', () => {
      it('应正确记录批次统计', () => {
        logger.logBatchStatistics(10, 8, 2, 0.85, 5000)

        const entries = logger.getLogEntries()
        expect(entries).toHaveLength(1)
        expect(entries[0]).toMatchObject({
          level: 'info',
          message: '批次统计',
          data: {
            totalChunks: 10,
            successCount: 8,
            failedCount: 2,
            avgSimilarity: 0.85,
            totalDuration: 5000,
          },
        })
      })

      it('应正确记录翻译开始', () => {
        const task = { id: 'task-1', text: '测试任务' }

        logger.logTranslationStart(task)

        const entries = logger.getLogEntries()
        expect(entries[0]).toMatchObject({
          level: 'info',
          message: '开始翻译',
          data: task,
        })
      })

      it('应正确记录重试尝试', () => {
        logger.logRetryAttempt(5, 2, 1000)

        const entries = logger.getLogEntries()
        expect(entries[0]).toMatchObject({
          level: 'warn',
          message: '重试尝试',
          data: { index: 5, attempt: 2, delay: 1000 },
        })
      })

      it('应正确记录API调用', () => {
        logger.logApiCall(1, 'https://api.test.com', 'POST')

        const entries = logger.getLogEntries()
        expect(entries[0]).toMatchObject({
          level: 'info',
          message: 'API调用',
          data: { index: 1, url: 'https://api.test.com', method: 'POST' },
        })
      })

      it('应正确记录质量检查', () => {
        const qualityIssues = ['length_mismatch', 'format_error']

        logger.logQualityCheck(3, qualityIssues, false)

        const entries = logger.getLogEntries()
        expect(entries[0]).toMatchObject({
          level: 'info',
          message: '质量检查',
          data: { index: 3, qualityIssues, passed: false },
        })
      })

      it('应正确记录翻译成功', () => {
        const result = { translation: '成功的翻译', confidence: 0.9 }

        logger.logTranslationSuccess(result)

        const entries = logger.getLogEntries()
        expect(entries[0]).toMatchObject({
          level: 'info',
          message: '翻译成功',
          data: result,
        })
      })

      it('应正确记录翻译失败', () => {
        const error = new Error('翻译失败')

        logger.logTranslationError(2, error, 3)

        const entries = logger.getLogEntries()
        expect(entries[0]).toMatchObject({
          level: 'error',
          message: '翻译失败',
          data: { index: 2, error, attempts: 3 },
        })
      })

      it('应正确记录相似度匹配', () => {
        logger.logSimilarityMatch(4, 0.92, 0.8, true)

        const entries = logger.getLogEntries()
        expect(entries[0]).toMatchObject({
          level: 'info',
          message: '相似度匹配',
          data: { index: 4, similarity: 0.92, threshold: 0.8, passed: true },
        })
      })
    })

    describe('数据获取和管理', () => {
      it('应返回所有翻译日志的副本', () => {
        logger.log({ source: '测试1', translation: '翻译1', method: 'method1' })
        logger.log({ source: '测试2', translation: '翻译2', method: 'method2' })

        const logs = logger.getLogs()
        expect(logs).toHaveLength(2)

        // 确保返回的是副本，不是原数组
        logs.push({ id: 'fake', timestamp: new Date(), source: '', translation: '', method: '' })
        expect(logger.getLogs()).toHaveLength(2)
      })

      it('应返回所有日志条目的副本', () => {
        logger.info('测试1')
        logger.warn('测试2')

        const entries = logger.getLogEntries()
        expect(entries).toHaveLength(2)

        // 确保返回的是副本
        entries.push({ timestamp: '', level: 'info', message: 'fake' })
        expect(logger.getLogEntries()).toHaveLength(2)
      })

      it('应根据ID正确查找日志', () => {
        const id1 = logger.log({ source: '测试1', translation: '翻译1', method: 'method1' })
        const id2 = logger.log({ source: '测试2', translation: '翻译2', method: 'method2' })

        const log1 = logger.getLogById(id1)
        const log2 = logger.getLogById(id2)
        const notFound = logger.getLogById('nonexistent')

        expect(log1).toBeDefined()
        expect(log1?.id).toBe(id1)
        expect(log1?.source).toBe('测试1')

        expect(log2).toBeDefined()
        expect(log2?.id).toBe(id2)
        expect(log2?.source).toBe('测试2')

        expect(notFound).toBeUndefined()
      })

      it('应正确清除所有日志', () => {
        logger.log({ source: '测试', translation: '翻译', method: 'method' })
        logger.info('测试信息')

        expect(logger.getLogs()).toHaveLength(1)
        expect(logger.getLogEntries()).toHaveLength(1)

        logger.clearLogs()

        expect(logger.getLogs()).toHaveLength(0)
        expect(logger.getLogEntries()).toHaveLength(0)
      })

      it('应正确导出日志为JSON格式', () => {
        const entry1 = { source: '测试1', translation: '翻译1', method: 'method1' }
        const entry2 = { source: '测试2', translation: '翻译2', method: 'method2' }

        logger.log(entry1)
        logger.log(entry2)

        const exported = logger.exportLogs()
        const parsed = JSON.parse(exported)

        expect(Array.isArray(parsed)).toBe(true)
        expect(parsed).toHaveLength(2)
        expect(parsed[0]).toMatchObject(entry1)
        expect(parsed[1]).toMatchObject(entry2)
      })

      it('应导出空数组当没有日志时', () => {
        const exported = logger.exportLogs()
        const parsed = JSON.parse(exported)

        expect(parsed).toEqual([])
      })
    })

    describe('时间戳和数据格式', () => {
      it('应生成正确格式的时间戳', () => {
        logger.info('测试时间戳')

        const entries = logger.getLogEntries()
        const timestamp = entries[0].timestamp

        expect(typeof timestamp).toBe('string')
        expect(() => new Date(timestamp)).not.toThrow()
        expect(new Date(timestamp).toISOString()).toBe(timestamp)
      })

      it('应为翻译日志生成Date对象时间戳', () => {
        logger.log({ source: '测试', translation: '翻译', method: 'method' })

        const logs = logger.getLogs()
        expect(logs[0].timestamp).toBeInstanceOf(Date)
      })

      it('应生成唯一的日志ID', () => {
        const id1 = logger.log({ source: '测试1', translation: '翻译1', method: 'method1' })
        const id2 = logger.log({ source: '测试2', translation: '翻译2', method: 'method2' })

        expect(id1).not.toBe(id2)
        expect(typeof id1).toBe('string')
        expect(typeof id2).toBe('string')

        // 验证UUID格式
        expect(id1).toMatch(/^[0-9a-f]{8}-[0-9a-f]{4}-4[0-9a-f]{3}-[89ab][0-9a-f]{3}-[0-9a-f]{12}$/i)
        expect(id2).toMatch(/^[0-9a-f]{8}-[0-9a-f]{4}-4[0-9a-f]{3}-[89ab][0-9a-f]{3}-[0-9a-f]{12}$/i)
      })
    })
  })

  describe('默认实例 (已弃用)', () => {
    beforeEach(() => {
      // 清理默认实例的状态
      translationLogger.clearLogs()

      // Mock console methods
      vi.spyOn(console, 'info').mockImplementation(() => {})
      vi.spyOn(console, 'warn').mockImplementation(() => {})
      vi.spyOn(console, 'error').mockImplementation(() => {})
    })

    it('应正确工作作为默认实例', () => {
      const entry = { source: '默认测试', translation: '默认翻译', method: 'default' }

      const id = translationLogger.log(entry)

      expect(id).toBeDefined()
      expect(translationLogger.getLogs()).toHaveLength(1)
      expect(translationLogger.getLogById(id)).toMatchObject(entry)
    })

    it('应支持所有日志级别', () => {
      translationLogger.info('信息')
      translationLogger.warn('警告')
      translationLogger.error('错误')

      const entries = translationLogger.getLogEntries()
      expect(entries).toHaveLength(3)
      expect(entries[0].level).toBe('info')
      expect(entries[1].level).toBe('warn')
      expect(entries[2].level).toBe('error')
    })
  })
})

/**
 * 翻译日志模块
 */

import { v4 as uuidv4 } from 'uuid'

// 兼容原有的LogEntry接口
export interface LogEntry {
  timestamp: string
  level: 'info' | 'warn' | 'error'
  message: string
  data?: unknown
}

export interface TranslationLog {
  id: string
  timestamp: Date
  source: string
  translation: string
  method: string
  quality?: number
}

// 新增：日志器接口
export interface LoggerInterface {
  info(message: string, data?: unknown): void
  warn(message: string, data?: unknown): void
  error(message: string, data?: unknown): void
  getEntries(): LogEntry[]
  clear(): void
}

// 新增：基础Logger类
export class Logger implements LoggerInterface {
  private entries: LogEntry[] = []

  private logWithLevel(level: 'info' | 'warn' | 'error', message: string, data?: unknown) {
    const entry: LogEntry = {
      timestamp: new Date().toISOString(),
      level,
      message,
      data,
    }

    this.entries.push(entry)

    // 控制台输出
    console[level](`[Logger] ${message}`, data || '')
  }

  info(message: string, data?: unknown) {
    this.logWithLevel('info', message, data)
  }

  warn(message: string, data?: unknown) {
    this.logWithLevel('warn', message, data)
  }

  error(message: string, data?: unknown) {
    this.logWithLevel('error', message, data)
  }

  getEntries(): LogEntry[] {
    return [...this.entries]
  }

  clear(): void {
    this.entries = []
  }
}

/**
 * @deprecated 使用新的分离式设计：Logger + TranslationLogger + TranslationService
 * 此类将在未来版本中移除，请迁移到新的API
 */
export class TranslationLogger {
  private logs: TranslationLog[] = []
  private logEntries: LogEntry[] = []

  // 原有的log方法
  log(entry: Omit<TranslationLog, 'id' | 'timestamp'>): string {
    const id = uuidv4()
    const logEntry: TranslationLog = {
      id,
      timestamp: new Date(),
      ...entry,
    }

    this.logs.push(logEntry)
    return id
  }

  // @deprecated 请使用独立的Logger类
  private logWithLevel(level: 'info' | 'warn' | 'error', message: string, data?: unknown) {
    const entry: LogEntry = {
      timestamp: new Date().toISOString(),
      level,
      message,
      data,
    }

    this.logEntries.push(entry)

    // 控制台输出
    console[level](`[TranslationLogger] ${message}`, data || '')
  }

  // @deprecated 请使用独立的Logger类
  info(message: string, data?: unknown) {
    this.logWithLevel('info', message, data)
  }

  // @deprecated 请使用独立的Logger类
  warn(message: string, data?: unknown) {
    this.logWithLevel('warn', message, data)
  }

  // @deprecated 请使用独立的Logger类
  error(message: string, data?: unknown) {
    this.logWithLevel('error', message, data)
  }

  // @deprecated 请使用TranslationService类
  logBatchStatistics(totalChunks: number, successCount: number, failedCount: number, avgSimilarity: number, totalDuration: number) {
    this.info('批次统计', { totalChunks, successCount, failedCount, avgSimilarity, totalDuration })
  }

  // @deprecated 请使用TranslationService类
  logTranslationStart(task: unknown) {
    this.info('开始翻译', task)
  }

  // @deprecated 请使用TranslationService类
  logRetryAttempt(index: number, attempt: number, delay: number) {
    this.warn('重试尝试', { index, attempt, delay })
  }

  // @deprecated 请使用TranslationService类
  logApiCall(index: number, url: string, method: string) {
    this.info('API调用', { index, url, method })
  }

  // @deprecated 请使用TranslationService类
  logQualityCheck(index: number, qualityIssues: unknown, passed: boolean) {
    this.info('质量检查', { index, qualityIssues, passed })
  }

  // @deprecated 请使用TranslationService类
  logTranslationSuccess(result: unknown) {
    this.info('翻译成功', result)
  }

  // @deprecated 请使用TranslationService类
  logTranslationError(index: number, error: unknown, attempts: number) {
    this.error('翻译失败', { index, error, attempts })
  }

  // @deprecated 请使用TranslationService类
  logSimilarityMatch(index: number, similarity: number, threshold: number, passed: boolean) {
    this.info('相似度匹配', { index, similarity, threshold, passed })
  }

  getLogs(): TranslationLog[] {
    return [...this.logs]
  }

  // @deprecated 请使用独立的Logger类的getEntries()方法
  getLogEntries(): LogEntry[] {
    return [...this.logEntries]
  }

  getLogById(id: string): TranslationLog | undefined {
    return this.logs.find((log) => log.id === id)
  }

  clearLogs(): void {
    this.logs = []
    this.logEntries = []
  }

  exportLogs(): string {
    return JSON.stringify(this.logs, null, 2)
  }
}

// 默认实例
export const translationLogger = new TranslationLogger()

// 新增：独立的翻译日志器
export class NewTranslationLogger {
  private logs: TranslationLog[] = []

  log(entry: Omit<TranslationLog, 'id' | 'timestamp'>): string {
    const id = uuidv4()
    const logEntry: TranslationLog = {
      id,
      timestamp: new Date(),
      ...entry,
    }

    this.logs.push(logEntry)
    return id
  }

  getLogs(): TranslationLog[] {
    return [...this.logs]
  }

  getLogById(id: string): TranslationLog | undefined {
    return this.logs.find((log) => log.id === id)
  }

  clear(): void {
    this.logs = []
  }

  exportLogs(): string {
    return JSON.stringify(this.logs, null, 2)
  }
}

// 新增：翻译服务类，组合Logger和TranslationLogger
export class TranslationService {
  constructor(
    private logger: LoggerInterface,
    private translationLogger: NewTranslationLogger,
  ) {}

  // 翻译日志方法
  logTranslation(entry: Omit<TranslationLog, 'id' | 'timestamp'>): string {
    return this.translationLogger.log(entry)
  }

  getTranslationLogs(): TranslationLog[] {
    return this.translationLogger.getLogs()
  }

  getTranslationById(id: string): TranslationLog | undefined {
    return this.translationLogger.getLogById(id)
  }

  // 业务日志方法
  logBatchStatistics(totalChunks: number, successCount: number, failedCount: number, avgSimilarity: number, totalDuration: number) {
    this.logger.info('批次统计', { totalChunks, successCount, failedCount, avgSimilarity, totalDuration })
  }

  logTranslationStart(task: unknown) {
    this.logger.info('开始翻译', task)
  }

  logRetryAttempt(index: number, attempt: number, delay: number) {
    this.logger.warn('重试尝试', { index, attempt, delay })
  }

  logApiCall(index: number, url: string, method: string) {
    this.logger.info('API调用', { index, url, method })
  }

  logQualityCheck(index: number, qualityIssues: unknown, passed: boolean) {
    this.logger.info('质量检查', { index, qualityIssues, passed })
  }

  logTranslationSuccess(result: unknown) {
    this.logger.info('翻译成功', result)
  }

  logTranslationError(index: number, error: unknown, attempts: number) {
    this.logger.error('翻译失败', { index, error, attempts })
  }

  logSimilarityMatch(index: number, similarity: number, threshold: number, passed: boolean) {
    this.logger.info('相似度匹配', { index, similarity, threshold, passed })
  }

  // 获取系统日志
  getSystemLogs(): LogEntry[] {
    return this.logger.getEntries()
  }

  // 清除所有日志
  clearAllLogs(): void {
    this.logger.clear()
    this.translationLogger.clear()
  }
}

// 新的推荐API实例
export const logger = new Logger()
export const newTranslationLogger = new NewTranslationLogger()
export const translationService = new TranslationService(logger, newTranslationLogger)

# Logger模块重构迁移指南

## 重构背景

原有的`TranslationLogger`类存在以下问题：

1. **职责混乱**：同时管理TranslationLog和LogEntry两种不同类型的日志
2. **业务逻辑耦合**：基础日志类包含太多特定业务方法
3. **API不一致性**：getLogs()和getLogEntries()返回不同类型，需要两套API
4. **命名问题**：TranslationLogger处理通用日志，名不副实
5. **扩展性差**：违反开闭原则，添加新日志类型需修改现有类

## 新架构设计

### 分离后的类结构

```typescript
// 1. 基础日志接口
interface LoggerInterface {
  info(message: string, data?: unknown): void
  warn(message: string, data?: unknown): void
  error(message: string, data?: unknown): void
  getEntries(): LogEntry[]
  clear(): void
}

// 2. 基础Logger类 - 处理通用日志
class Logger implements LoggerInterface

// 3. 独立的翻译日志器 - 只处理翻译日志
class NewTranslationLogger

// 4. 翻译服务类 - 组合模式，统一业务接口
class TranslationService
```

## 推荐迁移路径

### 方案1：立即使用新API（推荐）

```typescript
// 旧方式
import { translationLogger } from './logger'

translationLogger.info('系统信息')
translationLogger.logBatchStatistics(10, 8, 2, 0.9, 5000)
const logs = translationLogger.getLogs()
const entries = translationLogger.getLogEntries()

// 新方式
import { translationService } from './logger'

translationService.logBatchStatistics(10, 8, 2, 0.9, 5000)
const translationLogs = translationService.getTranslationLogs()
const systemLogs = translationService.getSystemLogs()
```

### 方案2：渐进式迁移

```typescript
// 第一阶段：继续使用旧API（有弃用警告）
import { translationLogger } from './logger'

// 第二阶段：逐步替换为新API
import { logger, newTranslationLogger, translationService } from './logger'

// 第三阶段：完全使用新API
import { translationService } from './logger'
```

## API对比

### 系统日志

```typescript
// 旧API
translationLogger.info('消息', data)
translationLogger.warn('警告', data)
translationLogger.error('错误', data)
const entries = translationLogger.getLogEntries()

// 新API - 选项1：直接使用Logger
logger.info('消息', data)
logger.warn('警告', data)
logger.error('错误', data)
const entries = logger.getEntries()

// 新API - 选项2：通过TranslationService
const entries = translationService.getSystemLogs()
```

### 翻译日志

```typescript
// 旧API
const id = translationLogger.log({ source: '原文', translation: '译文', method: 'test' })
const logs = translationLogger.getLogs()
const log = translationLogger.getLogById(id)

// 新API - 选项1：直接使用NewTranslationLogger
const id = newTranslationLogger.log({ source: '原文', translation: '译文', method: 'test' })
const logs = newTranslationLogger.getLogs()
const log = newTranslationLogger.getLogById(id)

// 新API - 选项2：通过TranslationService（推荐）
const id = translationService.logTranslation({ source: '原文', translation: '译文', method: 'test' })
const logs = translationService.getTranslationLogs()
const log = translationService.getTranslationById(id)
```

### 业务日志方法

```typescript
// 旧API和新API完全一致
translationLogger.logBatchStatistics(10, 8, 2, 0.9, 5000)
translationService.logBatchStatistics(10, 8, 2, 0.9, 5000)
```

## 迁移检查清单

### ✅ 立即操作（无风险）

- [ ] 导入新的API：`logger`, `newTranslationLogger`, `translationService`
- [ ] 在新代码中使用`translationService`替代`translationLogger`
- [ ] 测试新API功能是否正常

### ⚠️ 需要验证的更改

- [ ] 将`translationLogger.getLogEntries()`替换为`translationService.getSystemLogs()`
- [ ] 将`translationLogger.getLogs()`替换为`translationService.getTranslationLogs()`
- [ ] 更新类型声明（如果有自定义类型）

### 🔄 最终清理（建议下个版本）

- [ ] 移除所有`translationLogger`的使用
- [ ] 删除弃用的`TranslationLogger`类
- [ ] 更新文档和示例

## 优势总结

### 新架构优势

1. **单一职责**：每个类只负责一种类型的日志
2. **更好的扩展性**：可以轻松添加新的日志类型
3. **类型安全**：更清晰的类型定义
4. **测试友好**：更容易进行单元测试
5. **API一致性**：统一的接口设计

### 向后兼容性

- ✅ 所有现有代码继续工作
- ✅ 逐步迁移路径
- ✅ 弃用警告帮助识别需要更新的代码
- ✅ 完整的测试覆盖确保功能正确性

## 示例：完整迁移

```typescript
// 迁移前
import { translationLogger } from './logger'

class TranslationTask {
  async process() {
    translationLogger.logTranslationStart(this.task)

    try {
      const result = await this.translate()
      translationLogger.logTranslationSuccess(result)

      const id = translationLogger.log({
        source: this.source,
        translation: result.text,
        method: 'api',
      })

      translationLogger.info('任务完成', { id })
    } catch (error) {
      translationLogger.logTranslationError(0, error, 1)
    }
  }
}

// 迁移后
import { translationService } from './logger'

class TranslationTask {
  async process() {
    translationService.logTranslationStart(this.task)

    try {
      const result = await this.translate()
      translationService.logTranslationSuccess(result)

      const id = translationService.logTranslation({
        source: this.source,
        translation: result.text,
        method: 'api',
      })

      // 系统日志通过translationService自动处理
    } catch (error) {
      translationService.logTranslationError(0, error, 1)
    }
  }
}
```

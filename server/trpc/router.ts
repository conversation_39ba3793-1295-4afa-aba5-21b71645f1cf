import { adminCourseRouter } from './routers/adminCourse'
import { adminCoursePackRouter } from './routers/adminCoursePack'
import { deepseekRouter } from './routers/deepseek'
import { neteaseRouter } from './routers/netease'
import { translateRouter } from './routers/translate'
// tRPC主路由器
import { createTRPCRouter } from './trpc'

// 主应用路由器
export const appRouter = createTRPCRouter({
  netease: neteaseRouter,
  translate: translateRouter,
  deepseek: deepseekRouter,
  adminCoursePack: adminCoursePackRouter,
  adminCourse: adminCourseRouter,
})

// 导出路由器类型
export type AppRouter = typeof appRouter

// 课程相关的 tRPC Schema 定义
import { z } from "zod"

// 课程包 Schema
export const coursePackSchema = z.object({
  id: z.string(),
  order: z.number(),
  title: z.string(),
  description: z.string(),
  isFree: z.boolean(),
  cover: z.string(),
  creatorId: z.string(),
  shareLevel: z.string(),
  categoryId: z.string().optional(),
  createdAt: z.string(),
  updatedAt: z.string(),
})

// 课程 Schema
export const courseSchema = z.object({
  id: z.string(),
  title: z.string(),
  description: z.string(),
  video: z.string(),
  order: z.number(),
  coursePackId: z.string(),
  type: z.enum(["normal", "music"]),
  mediaUrl: z.string().optional(),
  createdAt: z.string(),
  updatedAt: z.string(),
})

// 语句 Schema
export const statementSchema = z.object({
  id: z.string(),
  order: z.number(),
  chinese: z.string(),
  english: z.string(),
  soundmark: z.string(),
  type: z.string(),
  image: z.record(z.unknown()).optional(),
  courseId: z.string(),
  sentenceId: z.string().optional(),
  startTime: z.number().optional(),
  endTime: z.number().optional(),
  createdAt: z.string(),
  updatedAt: z.string(),
})

// API 输入 Schema
export const courseDetailInputSchema = z.object({
  courseId: z.string(),
  coursePackId: z.string(),
})

export const coursePackInputSchema = z.object({
  coursePackId: z.string(),
})

export const courseCreateInputSchema = z.object({
  coursePackId: z.string(),
  title: z.string().optional(),
})

// API 响应 Schema
export const courseDetailResponseSchema = z.object({
  course: courseSchema,
  statements: z.array(statementSchema),
})

export const coursePackResponseSchema = z.object({
  coursePack: coursePackSchema,
  courses: z.array(courseSchema),
})

export const courseCreateResponseSchema = z.object({
  course: courseSchema,
})

// 类型导出
export type CoursePackSchemaType = z.infer<typeof coursePackSchema>
export type CourseSchemaType = z.infer<typeof courseSchema>
export type StatementSchemaType = z.infer<typeof statementSchema>
export type CourseDetailInputSchemaType = z.infer<typeof courseDetailInputSchema>
export type CoursePackInputSchemaType = z.infer<typeof coursePackInputSchema>
export type CourseCreateInputSchemaType = z.infer<typeof courseCreateInputSchema>
export type CourseDetailResponseSchemaType = z.infer<typeof courseDetailResponseSchema>
export type CoursePackResponseSchemaType = z.infer<typeof coursePackResponseSchema>
export type CourseCreateResponseSchemaType = z.infer<typeof courseCreateResponseSchema>

// 翻译相关的Schema定义
import { z } from 'zod'

// 翻译输入Schema
export const translateInputSchema = z.object({
  sourceLanguage: z.string().optional(), // 源语言，可选，不填则自动检测
  targetLanguage: z.string(), // 目标语言，必填
  textList: z.array(z.string()).max(16), // 待翻译文本列表，最多16个
})

// 翻译结果Schema
export const translationResultSchema = z.object({
  translation: z.string(), // 翻译结果
  detectedSourceLanguage: z.string().optional(), // 自动检测的源语言
})

// 翻译输出Schema
export const translateOutputSchema = z.object({
  translationList: z.array(translationResultSchema), // 翻译结果列表
})

// 类型导出
export type TranslateInputType = z.infer<typeof translateInputSchema>
export type TranslationResultType = z.infer<typeof translationResultSchema>
export type TranslateOutputType = z.infer<typeof translateOutputSchema>

// DeepSeek 聊天相关的Schema定义
import { z } from 'zod'
import { DEFAULT_TEMPERATURE, DEFAULT_TOP_P, SUMMARY_LENGTH } from '~/common/constants'

// 聊天消息 Schema
export const messageSchema = z.object({
  role: z.enum(['system', 'user', 'assistant']), // 消息角色
  content: z.string(), // 消息内容
})

// DeepSeek 聊天输入 Schema
export const deepseekChatInputSchema = z.object({
  messages: z.array(messageSchema).min(1), // 聊天消息列表，至少需要1条
  stream: z.boolean().optional().default(false), // 是否启用流式输出
  temperature: z.number().min(0).max(2).optional().default(DEFAULT_TEMPERATURE), // 温度参数
  max_tokens: z.number().min(1).max(16000).optional().default(SUMMARY_LENGTH), // 最大输出token数
  top_p: z.number().min(0).max(1).optional().default(DEFAULT_TOP_P), // top_p采样参数
})

// DeepSeek 聊天输出消息 Schema
export const deepseekMessageSchema = z.object({
  role: z.enum(['assistant', 'system', 'user']),
  content: z.string(),
})

// DeepSeek 聊天输出选项 Schema
export const deepseekChoiceSchema = z.object({
  index: z.number(),
  message: deepseekMessageSchema,
  finish_reason: z.string().nullable(),
})

// DeepSeek 使用统计 Schema
export const deepseekUsageSchema = z.object({
  prompt_tokens: z.number(),
  completion_tokens: z.number(),
  total_tokens: z.number(),
})

// DeepSeek 聊天输出 Schema (非流式)
export const deepseekChatOutputSchema = z.object({
  id: z.string(),
  object: z.string(),
  created: z.number(),
  model: z.string(),
  choices: z.array(deepseekChoiceSchema),
  usage: deepseekUsageSchema,
})

// DeepSeek 流式输出数据 Schema
export const deepseekStreamChunkSchema = z.object({
  id: z.string(),
  object: z.string(),
  created: z.number(),
  model: z.string(),
  choices: z.array(
    z.object({
      index: z.number(),
      delta: z.object({
        role: z.string().optional(),
        content: z.string().optional(),
      }),
      finish_reason: z.string().nullable(),
    }),
  ),
})

// 类型导出
export type MessageType = z.infer<typeof messageSchema>
export type DeepseekChatInputType = z.infer<typeof deepseekChatInputSchema>
export type DeepseekChatOutputType = z.infer<typeof deepseekChatOutputSchema>
export type DeepseekStreamChunkType = z.infer<typeof deepseekStreamChunkSchema>

// 歌曲Schema定义
import { z } from 'zod'

/**
 * 入参: 搜索歌曲-列表
 */
export const neteaseSearchInputSchema = z.object({
  q: z.string().min(1, '搜索关键词不能为空').max(100, '搜索关键词不能超过100个字符'),
  page: z.number().min(1).max(100).default(1).optional(),
  limit: z.number().min(1).max(100).default(30).optional(),
})

/**
 * 入参: 歌曲ID
 */
export const songIdSchema = z.string().min(1, '歌曲ID不能为空')

/**
 * 入参: 歌曲详情
 */
export const neteaseSongDetailInputSchema = z.object({
  id: songIdSchema,
})

/**
 * 入参: 歌词
 */
export const neteaseLyricInputSchema = z.object({
  id: songIdSchema,
})

/**
 * 音乐平台枚举
 */
export const musicSourceSchema = z.enum(['wy'])

/**
 * 响应: 网易-歌曲 item
 */
export const neteaseSongItemSchema = z.object({
  songId: z.string(),
  name: z.string(),
  singer: z.string(),
  albumName: z.string(),
  albumId: z.string(),
  duration: z.string(),
  source: musicSourceSchema,
})

/**
 * 响应: 网易-歌曲列表
 */
export const neteaseSearchListSchema = z.object({
  list: z.array(neteaseSongItemSchema),
  total: z.number(),
  allPage: z.number(),
  limit: z.number(),
})

/**
 * 响应: 网易-歌曲详情
 */
export const neteaseSongDetailSchema = z.object({
  songId: z.string(),
  name: z.string(),
  singer: z.string(),
  albumName: z.string(),
  albumId: z.string(),
  duration: z.string(),
  img: z.string(),
  source: musicSourceSchema,
})

/**
 * 响应: 歌词
 */
export const neteaseLyricInfoSchema = z.object({
  originalLyric: z.string(),
  translatedLyric: z.string(),
  lyric: z.string(),
})

export type MusicSourceType = z.infer<typeof musicSourceSchema>

// id 类型
export type SongIdType = z.infer<typeof songIdSchema>

// 歌曲列表
export type NeteaseSearchInputSchemaType = z.infer<typeof neteaseSearchInputSchema>
export type NeteaseSongItemSchemaType = z.infer<typeof neteaseSongItemSchema>
export type NeteaseSearchListSchemaType = z.infer<typeof neteaseSearchListSchema>

// 歌曲详情
export type NeteaseSongDetailInputSchemaType = z.infer<typeof neteaseSongDetailInputSchema>
export type NeteaseSongDetailSchemaType = z.infer<typeof neteaseSongDetailSchema>

// 歌词
export type NeteaseLyricInputSchemaType = z.infer<typeof neteaseLyricInputSchema>
export type NeteaseLyricInfoSchemaType = z.infer<typeof neteaseLyricInfoSchema>

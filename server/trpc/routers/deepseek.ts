// DeepSeek tRPC路由器
import { createTRPCRouter, publicProcedure } from '../trpc'
import {
  deepseekChatInputSchema,
  deepseekChatOutputSchema,
  type DeepseekChatInputType,
  type DeepseekChatOutputType,
} from '../schemas/deepseek'
import { TRPCError } from '@trpc/server'
import { DEEP_SEEK_MODEL_ID } from '~/common/constants'

/**
 * 火山引擎 DeepSeek API实现
 */
async function volcengineDeepseekChat(input: DeepseekChatInputType): Promise<DeepseekChatOutputType> {
  const config = useRuntimeConfig()

  const arkApiKey = config.arkApiKey

  if (!arkApiKey || !DEEP_SEEK_MODEL_ID) {
    throw new TRPCError({
      code: 'INTERNAL_SERVER_ERROR',
      message: 'DeepSeek服务配置错误：缺少ARK_API_KEY或DEEP_SEEK_MODEL_ID',
    })
  }

  try {
    // 构建请求体
    const requestBody = {
      model: DEEP_SEEK_MODEL_ID,
      messages: input.messages,
      temperature: input.temperature,
      max_tokens: input.max_tokens,
      top_p: input.top_p,
      stream: false, // 非流式请求
    }

    const payload = JSON.stringify(requestBody)

    // 发送请求到火山引擎Chat API
    const response = await fetch('https://ark.cn-beijing.volces.com/api/v3/chat/completions', {
      method: 'POST',
      headers: {
        Authorization: `Bearer ${arkApiKey}`,
        'Content-Type': 'application/json',
      },
      body: payload,
    })

    if (!response.ok) {
      const errorText = await response.text()
      throw new TRPCError({
        code: 'BAD_REQUEST',
        message: `DeepSeek API错误: ${response.status} ${response.statusText} - ${errorText}`,
      })
    }

    const data = await response.json()

    // 验证返回数据格式
    return deepseekChatOutputSchema.parse(data)
  } catch (error) {
    console.error('火山引擎 DeepSeek API调用失败:', error)

    if (error instanceof TRPCError) {
      throw error
    }

    throw new TRPCError({
      code: 'INTERNAL_SERVER_ERROR',
      message: 'DeepSeek服务暂时不可用，请稍后重试',
    })
  }
}

/**
 * DeepSeek路由器
 */
export const deepseekRouter = createTRPCRouter({
  /**
   * 非流式聊天对话
   */
  chat: publicProcedure
    .input(deepseekChatInputSchema)
    .output(deepseekChatOutputSchema)
    .mutation(async ({ input }) => {
      const result = await volcengineDeepseekChat(input)
      return result
    }),
})

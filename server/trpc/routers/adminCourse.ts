import type {
  AdminCourseDetailResponse,
  BaseResponse,
} from '@/types/course'
import { isEmpty } from 'lodash-es'
import { z } from 'zod'
import { createTRPCRouter, publicProcedure } from '../trpc'

const config = useRuntimeConfig()

export const adminCourseRouter = createTRPCRouter({
  /**
   * 获取课程详情（含statements）
   */
  getCourseDetail: publicProcedure
    .input(z.object({
      coursePackId: z.string(),
      courseId: z.string(),
    }))
    .query(async ({ input }) => {
      const { coursePackId, courseId } = input
      const url = `${config.admin.baseUrl}/admin/course-packs/${coursePackId}/courses/${courseId}/detail?userId=${config.admin.userId}`
      const response = await fetch(url, {
        method: 'GET',
        headers: {
          'Accept': 'application/json',
          'x-admin-secret': config.admin.secretKey,
        },
      })
      if (!response.ok) {
        throw new Error(`API请求失败: ${response.status}`)
      }
      const data = (await response.json()) as BaseResponse<AdminCourseDetailResponse['data']>
      if (isEmpty(data) || data.success !== true) {
        throw new Error(data?.message || '获取课程详情失败')
      }
      return data.data
    }),
})

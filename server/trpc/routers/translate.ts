// 翻译tRPC路由器
import { createTRPCRouter, publicProcedure } from '../trpc'
import { translateInputSchema, translateOutputSchema, type TranslateInputType, type TranslateOutputType } from '../schemas/translate'
import { VolcengineV4Signer } from '../utils/volcengine'
import { TRPCError } from '@trpc/server'

// 火山引擎API响应类型
interface VolcengineTranslationItem {
  Translation: string
  DetectedSourceLanguage?: string
}

interface VolcengineApiResponse {
  TranslationList?: VolcengineTranslationItem[]
  ResponseMetadata?: {
    Error?: {
      Message?: string
    }
  }
}

/**
 * 火山引擎翻译API实现
 */
async function volcengineTranslate(input: TranslateInputType): Promise<TranslateOutputType> {
  const config = useRuntimeConfig()

  const accessKey = config.volcengineAccessKey
  const secretKey = config.volcengineSecretKey

  if (!accessKey || !secretKey) {
    throw new TRPCError({
      code: 'INTERNAL_SERVER_ERROR',
      message: '翻译服务配置错误：缺少API密钥',
    })
  }

  try {
    // 构建请求体
    const requestBody = {
      SourceLanguage: input.sourceLanguage,
      TargetLanguage: input.targetLanguage,
      TextList: input.textList,
    }

    const payload = JSON.stringify(requestBody)

    // 创建签名器
    const signer = new VolcengineV4Signer(accessKey, secretKey)

    // 生成签名头部
    const headers = signer.sign('POST', '/', 'Action=TranslateText&Version=2020-06-01', {}, payload)

    // 发送请求
    const response = await fetch('https://translate.volcengineapi.com/?Action=TranslateText&Version=2020-06-01', {
      method: 'POST',
      headers,
      body: payload,
    })

    if (!response.ok) {
      throw new Error(`HTTP ${response.status}: ${response.statusText}`)
    }

    const data = (await response.json()) as VolcengineApiResponse

    // 检查API响应错误
    if (data.ResponseMetadata?.Error) {
      const error = data.ResponseMetadata.Error
      throw new TRPCError({
        code: 'BAD_REQUEST',
        message: `翻译API错误: ${error.Message || '未知错误'}`,
      })
    }

    // 转换返回数据格式
    const translationList =
      data.TranslationList?.map((item: VolcengineTranslationItem) => ({
        translation: item.Translation,
        detectedSourceLanguage: item.DetectedSourceLanguage,
      })) || []

    return {
      translationList,
    }
  } catch (error) {
    console.error('火山引擎翻译API调用失败:', error)

    if (error instanceof TRPCError) {
      throw error
    }

    throw new TRPCError({
      code: 'INTERNAL_SERVER_ERROR',
      message: '翻译服务暂时不可用，请稍后重试',
    })
  }
}

/**
 * 翻译路由器
 */
export const translateRouter = createTRPCRouter({
  /**
   * 文本翻译
   */
  text: publicProcedure
    .input(translateInputSchema)
    .output(translateOutputSchema)
    .mutation(async ({ input }) => {
      const result = await volcengineTranslate(input)
      return result
    }),
})

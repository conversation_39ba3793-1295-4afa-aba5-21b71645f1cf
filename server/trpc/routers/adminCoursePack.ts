import type {
  AdminCoursePack,
  BaseResponse,
  BatchCreateData,
  CoursePackWithCoursesResponse,
  ProcessAllCoursesData,
  SplitAllCoursesData,
} from '@/types/course'
import { isEmpty } from 'lodash-es'
import { z } from 'zod'
import { createTRPCRouter, publicProcedure } from '../trpc'

const config = useRuntimeConfig()

export const adminCoursePackRouter = createTRPCRouter({
  /**
   * 获取管理员自己的课程包列表
   */
  listMyCoursePacks: publicProcedure.query(async () => {
    const url = `${config.admin.baseUrl}/admin/course-packs?userId=${config.admin.userId}`
    const response = await fetch(url, {
      method: 'GET',
      headers: {
        'Accept': 'application/json',
        'x-admin-secret': config.admin.secretKey,
      },
    })

    if (!response.ok) {
      throw new Error(`API请求失败: ${response.status}`)
    }
    const data = (await response.json()) as BaseResponse<AdminCoursePack[]>
    if (isEmpty(data) || data.success !== true) {
      throw new Error(data?.message || '获取课程包列表失败')
    }
    return data.data
  }),

  /**
   * 批量创建课程包
   */
  batchCreateCoursePacks: publicProcedure.mutation(async ({ input }) => {
    const url = `${config.admin.baseUrl}/admin/course-packs/batch`
    const bodyObj = input ? { ...input as object, userId: config.admin.userId } : { userId: config.admin.userId }
    const response = await fetch(url, {
      method: 'POST',
      headers: {
        'Accept': 'application/json',
        'Content-Type': 'application/json',
        'x-admin-secret': config.admin.secretKey,
      },
      body: JSON.stringify(bodyObj),
    })
    if (!response.ok) {
      throw new Error(`API请求失败: ${response.status}`)
    }
    const data = (await response.json()) as unknown
    const result = data as BaseResponse<BatchCreateData[]>
    if (typeof result !== 'object' || result == null || result.success !== true) {
      throw new Error(result?.message || '批量创建课程包失败')
    }
    return result.data
  }),

  /**
   * 批量处理课程包中所有课程
   */
  processAllCoursesInPack: publicProcedure.mutation(async ({ input }) => {
    const url = `${config.admin.baseUrl}/admin/course-packs/process-all`
    const bodyObj = input ? { ...input as object, userId: config.admin.userId } : { userId: config.admin.userId }
    const response = await fetch(url, {
      method: 'POST',
      headers: {
        'Accept': 'application/json',
        'Content-Type': 'application/json',
        'x-admin-secret': config.admin.secretKey,
      },
      body: JSON.stringify(bodyObj),
    })
    if (!response.ok) {
      throw new Error(`API请求失败: ${response.status}`)
    }
    const data = (await response.json()) as unknown
    const result = data as BaseResponse<ProcessAllCoursesData>
    if (typeof result !== 'object' || result == null || result.success !== true) {
      throw new Error(result?.message || '批量处理课程包失败')
    }
    return result.data
  }),

  /**
   * 批量拆分课程包中所有课程
   */
  splitAllCoursesInPack: publicProcedure.mutation(async ({ input }) => {
    const url = `${config.admin.baseUrl}/admin/course-packs/split-all`
    const bodyObj = input ? { ...input as object, userId: config.admin.userId } : { userId: config.admin.userId }
    const response = await fetch(url, {
      method: 'POST',
      headers: {
        'Accept': 'application/json',
        'Content-Type': 'application/json',
        'x-admin-secret': config.admin.secretKey,
      },
      body: JSON.stringify(bodyObj),
    })
    if (!response.ok) {
      throw new Error(`API请求失败: ${response.status}`)
    }
    const data = (await response.json()) as unknown
    const result = data as BaseResponse<SplitAllCoursesData>
    if (typeof result !== 'object' || result == null || result.success !== true) {
      throw new Error(result?.message || '批量拆分课程包失败')
    }
    return result.data
  }),

  /**
   * 获取指定课程包下课程列表
   */
  getCoursePackWithCourses: publicProcedure
    .input(z.object({
      coursePackId: z.string(),
    }))
    .query(async ({ input }) => {
      const { coursePackId } = input
      const url = `${config.admin.baseUrl}/admin/course-packs/${coursePackId}?userId=${config.admin.userId}`
      const response = await fetch(url, {
        method: 'GET',
        headers: {
          'Accept': 'application/json',
          'x-admin-secret': config.admin.secretKey,
        },
      })
      if (!response.ok) {
        throw new Error(`API请求失败: ${response.status}`)
      }
      const data = (await response.json()) as BaseResponse<CoursePackWithCoursesResponse>
      if (isEmpty(data) || data.success !== true) {
        throw new Error(data?.message || '获取课程包详情失败')
      }
      return data.data
    }),
})

// 网易云音乐tRPC路由器
import { createTRPCRouter, publicProcedure } from '../trpc'
import {
  musicSourceSchema,
  neteaseLyricInfoSchema,
  neteaseLyricInputSchema,
  neteaseSearchInputSchema,
  neteaseSearchListSchema,
  neteaseSongDetailInputSchema,
  neteaseSongDetailSchema,
  type NeteaseLyricInfoSchemaType,
  type NeteaseSearchInputSchemaType,
  type NeteaseSearchListSchemaType,
  type NeteaseSongDetailSchemaType,
  type NeteaseSongItemSchemaType,
  type SongIdType,
} from '../schemas/api/song'
import { createSearchFailedError, createSongNotFoundError, createLyricNotFoundError } from '../utils/errorHandler'
import { map } from 'lodash-es'
import { formatDurationToMinute } from '~/utils/processing/time/timeFormatter'
import { mergeLyrics } from '~/utils/formats/parsers/lrcParser'

// 网易云音乐API基础URL（使用公开的网易云音乐API）
const NETEASE_API_BASE = 'https://netease-cloud-music-api-three-rho.vercel.app'

// 网易云音乐搜索API实现
async function neteaseSearch({ q, page = 1, limit = 30 }: NeteaseSearchInputSchemaType): Promise<NeteaseSearchListSchemaType> {
  try {
    // 计算偏移量
    const offset = (page - 1) * limit

    // 构建搜索URL
    const url = `${NETEASE_API_BASE}/search?keywords=${encodeURIComponent(q)}&limit=${limit}&offset=${offset}`

    const response = await fetch(url)

    if (!response.ok) {
      throw new Error(`网易云音乐API请求失败: ${response.status}`)
    }

    const data = await response.json()

    if (data.code !== 200) {
      throw new Error(`网易云音乐API错误: ${data.message || '未知错误'}`)
    }

    // 转换数据格式
    const songs = data.result?.songs || []
    const list: NeteaseSongItemSchemaType[] = map(songs, (song: Record<string, unknown>) => ({
      songId: String(song.id),
      name: song.name as string,
      singer: map(song.artists as { name: string }[], (artist) => artist.name).join('/') || '',
      albumName: (song.album as { name?: string })?.name || '',
      albumId: String((song.album as { id?: string })?.id || ''),
      duration: formatDurationToMinute((song.duration as number) || 0),
      source: musicSourceSchema.Enum.wy,
    }))

    return {
      list,
      total: data.result?.songCount || 0,
      allPage: Math.ceil((data.result?.songCount || 0) / limit),
      limit,
    }
  } catch (error) {
    console.error('网易云音乐搜索失败:', error)
    throw createSearchFailedError(musicSourceSchema.Enum.wy)
  }
}

// 网易云音乐歌曲详情API实现
async function neteaseSongDetail(id: SongIdType): Promise<NeteaseSongDetailSchemaType> {
  try {
    const url = `${NETEASE_API_BASE}/song/detail?ids=${id}`

    const response = await fetch(url)

    if (!response.ok) {
      throw new Error(`网易云音乐API请求失败: ${response.status}`)
    }

    const data = await response.json()
    console.log('%c AT 🥝 data 🥝-71', 'font-size:13px; background:#9969cf; color:#ddadff;', data)

    if (data.code !== 200 || !data.songs?.[0]) {
      throw createSongNotFoundError(id, musicSourceSchema.Enum.wy)
    }

    const song = data.songs[0] as Record<string, unknown>

    return {
      songId: song.id as string,
      name: song.name as string,
      singer: map(song.ar as Array<{ name: string }>, (artist) => artist.name).join('/') || '',
      albumName: (song.al as { name?: string })?.name || '',
      albumId: (song.al as { id?: string })?.id || '',
      duration: formatDurationToMinute((song.dt as number) || 0),
      img: (song.al as { picUrl?: string })?.picUrl || '',
      source: musicSourceSchema.Enum.wy,
    }
  } catch (error) {
    if ((error as { code?: string }).code === 'NOT_FOUND') {
      throw error
    }
    console.error('获取网易云音乐歌曲详情失败:', error)
    throw createSongNotFoundError(id, musicSourceSchema.Enum.wy)
  }
}

// 网易云音乐歌词API实现
async function neteaseLyric(id: SongIdType): Promise<NeteaseLyricInfoSchemaType> {
  try {
    const url = `${NETEASE_API_BASE}/lyric?id=${id}`

    const response = await fetch(url)

    if (!response.ok) {
      throw new Error(`网易云音乐API请求失败: ${response.status}`)
    }

    const data = await response.json()

    if (data.code !== 200) {
      throw createLyricNotFoundError(id, musicSourceSchema.Enum.wy)
    }

    return {
      originalLyric: data.lrc?.lyric || '',
      translatedLyric: data.tlyric?.lyric || '',
      lyric: mergeLyrics(data.lrc?.lyric || '', data.tlyric?.lyric || ''),
    }
  } catch (error) {
    if ((error as { code?: string }).code === 'NOT_FOUND') {
      throw error
    }
    console.error('获取网易云音乐歌词失败:', error)
    throw createLyricNotFoundError(id, musicSourceSchema.Enum.wy)
  }
}

/**
 * 网易云音乐路由器
 */
export const neteaseRouter = createTRPCRouter({
  /**
   * 搜索歌曲
   */
  search: publicProcedure
    .input(neteaseSearchInputSchema)
    .output(neteaseSearchListSchema)
    .query(async ({ input }) => {
      const result = await neteaseSearch(input)
      return result
    }),

  /**
   * 获取歌曲详情
   */
  detail: publicProcedure
    .input(neteaseSongDetailInputSchema)
    .output(neteaseSongDetailSchema)
    .query(async ({ input }) => {
      const result = await neteaseSongDetail(input.id)
      return result
    }),

  /**
   * 获取歌词
   */
  lyric: publicProcedure
    .input(neteaseLyricInputSchema)
    .output(neteaseLyricInfoSchema)
    .query(async ({ input }) => {
      const result = await neteaseLyric(input.id)
      return result
    }),
})

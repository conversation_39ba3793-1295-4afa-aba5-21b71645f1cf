// 火山引擎V4签名算法实现
import { createHmac, createHash } from 'crypto'

/**
 * 火山引擎API V4签名算法
 */
export class VolcengineV4Signer {
  private accessKey: string
  private secretKey: string
  private service: string
  private region: string

  constructor(accessKey: string, secretKey: string, service: string = 'translate', region: string = 'cn-north-1') {
    this.accessKey = accessKey
    this.secretKey = secretKey
    this.service = service
    this.region = region
  }

  /**
   * 生成签名头部
   */
  sign(method: string, uri: string, query: string, headers: Record<string, string>, payload: string): Record<string, string> {
    const now = new Date()
    const dateStamp = now.toISOString().slice(0, 10).replace(/-/g, '')
    const timeStamp = now.toISOString().slice(0, 19).replace(/[-:]/g, '') + 'Z'

    // 添加必要的头部
    const signedHeaders = {
      ...headers,
      'X-Date': timeStamp,
      Host: 'translate.volcengineapi.com',
      'Content-Type': 'application/json',
    }

    // 计算签名
    const canonicalRequest = this.createCanonicalRequest(method, uri, query, signedHeaders, payload)
    const stringToSign = this.createStringToSign(timeStamp, dateStamp, canonicalRequest)
    const signature = this.calculateSignature(dateStamp, stringToSign)

    // 生成授权头部
    const signedHeaderNames = Object.keys(signedHeaders)
      .map((k) => k.toLowerCase())
      .sort()
      .join(';')
    const authorizationHeader = `HMAC-SHA256 Credential=${this.accessKey}/${dateStamp}/${this.region}/${this.service}/request, SignedHeaders=${signedHeaderNames}, Signature=${signature}`

    return {
      ...signedHeaders,
      Authorization: authorizationHeader,
    }
  }

  private createCanonicalRequest(method: string, uri: string, query: string, headers: Record<string, string>, payload: string): string {
    const canonicalHeaders = Object.entries(headers)
      .map(([key, value]) => `${key.toLowerCase()}:${value.trim()}`)
      .sort()
      .join('\n')

    const signedHeaders = Object.keys(headers)
      .map((k) => k.toLowerCase())
      .sort()
      .join(';')

    const hashedPayload = createHash('sha256').update(payload, 'utf8').digest('hex')

    return [method.toUpperCase(), uri, query, canonicalHeaders, '', signedHeaders, hashedPayload].join('\n')
  }

  private createStringToSign(timeStamp: string, dateStamp: string, canonicalRequest: string): string {
    const scope = `${dateStamp}/${this.region}/${this.service}/request`
    const hashedCanonicalRequest = createHash('sha256').update(canonicalRequest, 'utf8').digest('hex')

    return ['HMAC-SHA256', timeStamp, scope, hashedCanonicalRequest].join('\n')
  }

  private calculateSignature(dateStamp: string, stringToSign: string): string {
    const kDate = createHmac('sha256', this.secretKey).update(dateStamp).digest()
    const kRegion = createHmac('sha256', kDate).update(this.region).digest()
    const kService = createHmac('sha256', kRegion).update(this.service).digest()
    const kSigning = createHmac('sha256', kService).update('request').digest()
    const signature = createHmac('sha256', kSigning).update(stringToSign).digest('hex')

    return signature
  }
}

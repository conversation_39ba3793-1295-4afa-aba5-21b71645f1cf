// tRPC错误处理工具
import { TRPCError } from '@trpc/server'
import type { MusicSourceType } from '../schemas/api/song'

/**
 * 错误码枚举
 */
export enum ErrorCodes {
  PLATFORM_UNAVAILABLE = 'PLATFORM_UNAVAILABLE',
  INVALID_PARAMS = 'INVALID_PARAMS',
  SEARCH_FAILED = 'SEARCH_FAILED',
  SONG_NOT_FOUND = 'SONG_NOT_FOUND',
  LYRIC_NOT_FOUND = 'LYRIC_NOT_FOUND',
  RATE_LIMITED = 'RATE_LIMITED',
  UNKNOWN_ERROR = 'UNKNOWN_ERROR',
}

/**
 * 创建平台不可用错误
 */
export function createPlatformUnavailableError(platform: MusicSourceType): TRPCError {
  return new TRPCError({
    code: 'INTERNAL_SERVER_ERROR',
    message: `平台 ${platform} 当前不可用`,
    cause: {
      code: ErrorCodes.PLATFORM_UNAVAILABLE,
      platform,
    },
  })
}

/**
 * 创建参数无效错误
 */
export function createInvalidParamsError(message: string, platform?: MusicSourceType): TRPCError {
  return new TRPCError({
    code: 'BAD_REQUEST',
    message,
    cause: {
      code: ErrorCodes.INVALID_PARAMS,
      platform,
    },
  })
}

/**
 * 创建搜索失败错误
 */
export function createSearchFailedError(platform: MusicSourceType): TRPCError {
  return new TRPCError({
    code: 'INTERNAL_SERVER_ERROR',
    message: `在 ${platform} 平台搜索失败`,
    cause: {
      code: ErrorCodes.SEARCH_FAILED,
      platform,
    },
  })
}

/**
 * 创建歌曲未找到错误
 */
export function createSongNotFoundError(songId: string, platform: MusicSourceType): TRPCError {
  return new TRPCError({
    code: 'NOT_FOUND',
    message: `在 ${platform} 平台未找到歌曲 ${songId}`,
    cause: {
      code: ErrorCodes.SONG_NOT_FOUND,
      platform,
      songId,
    },
  })
}

/**
 * 创建歌词未找到错误
 */
export function createLyricNotFoundError(songId: string, platform: MusicSourceType): TRPCError {
  return new TRPCError({
    code: 'NOT_FOUND',
    message: `在 ${platform} 平台未找到歌曲 ${songId} 的歌词`,
    cause: {
      code: ErrorCodes.LYRIC_NOT_FOUND,
      platform,
      songId,
    },
  })
}

/**
 * 创建速率限制错误
 */
export function createRateLimitError(platform: MusicSourceType): TRPCError {
  return new TRPCError({
    code: 'TOO_MANY_REQUESTS',
    message: `${platform} 平台请求过于频繁，请稍后再试`,
    cause: {
      code: ErrorCodes.RATE_LIMITED,
      platform,
    },
  })
}

export default defineEventHandler(async (event) => {
  try {
    // 获取查询参数
    const query = getQuery(event)
    
    // 读取multipart表单数据
    const formData = await readMultipartFormData(event)
    
    if (!formData || formData.length === 0) {
      throw createError({
        statusCode: 400,
        statusMessage: 'Missing form data'
      })
    }

    // 查找名为"data"的文件字段
    const fileField = formData.find(field => field.name === 'data')
    if (!fileField || !fileField.data) {
      throw createError({
        statusCode: 400,
        statusMessage: 'Missing audio file in form data'
      })
    }

    // 添加调试日志
    console.log('🔍 Multipart form data info:', {
      fieldCount: formData.length,
      fileName: fileField.filename,
      fileSize: fileField.data.length,
      queryParams: query
    })
    
    // 构建火山引擎API的URL
    const apiUrl = new URL('https://openspeech.bytedance.com/api/v1/vc/submit')
    Object.entries(query).forEach(([key, value]) => {
      if (value) apiUrl.searchParams.append(key, String(value))
    })

    console.log('🚀 Sending to:', apiUrl.toString())

    // 构造新的FormData发送给火山引擎
    const volcFormData = new FormData()
    const blob = new Blob([fileField.data], { 
      type: fileField.type || 'audio/wav' 
    })
    volcFormData.append('data', blob, fileField.filename || 'audio')

    // 转发请求到火山引擎API
    const response = await fetch(apiUrl.toString(), {
      method: 'POST',
      headers: {
        'Authorization': getHeader(event, 'authorization') || '',
      },
      body: volcFormData,
    })

    console.log('📡 Response status:', response.status, response.statusText)

    if (!response.ok) {
      const errorText = await response.text()
      console.error('❌ API Error:', errorText)
      throw createError({
        statusCode: response.status,
        statusMessage: `API request failed: ${response.statusText} - ${errorText}`
      })
    }

    const result = await response.json()
    console.log('✅ Success:', result)
    return result
    
  } catch (error) {
    console.error('💥 Speech recognition API error:', error)
    throw createError({
      statusCode: 500,
      statusMessage: error instanceof Error ? error.message : 'Internal server error'
    })
  }
})

export default defineEventHandler(async (event) => {
  try {
    // 获取查询参数
    const query = getQuery(event)
    
    console.log('🔍 Query params received:', query)
    console.log('🔑 Authorization header:', getHeader(event, 'authorization'))
    
    // 构建火山引擎API的URL
    const apiUrl = new URL('https://openspeech.bytedance.com/api/v1/vc/query')
    Object.entries(query).forEach(([key, value]) => {
      if (value) apiUrl.searchParams.append(key, String(value))
    })

    console.log('🚀 Requesting URL:', apiUrl.toString())

    // 转发请求到火山引擎API
    const response = await fetch(apiUrl.toString(), {
      method: 'GET',
      headers: {
        'Authorization': getHeader(event, 'authorization') || '',
      },
    })

    console.log('📡 Response from VolcEngine:', {
      status: response.status,
      statusText: response.statusText,
      headers: Object.fromEntries(response.headers.entries())
    })

    if (!response.ok) {
      const errorText = await response.text()
      console.error('❌ VolcEngine API Error:', errorText)
      
      throw createError({
        statusCode: response.status,
        statusMessage: `API request failed: ${response.statusText} - ${errorText}`
      })
    }

    const result = await response.json()
    console.log('✅ VolcEngine API Success:', result)
    return result
    
  } catch (error) {
    console.error('💥 Speech recognition query API error:', error)
    throw createError({
      statusCode: 500,
      statusMessage: error instanceof Error ? error.message : 'Internal server error'
    })
  }
}) 
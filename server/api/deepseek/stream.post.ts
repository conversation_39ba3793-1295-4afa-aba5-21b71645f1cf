// DeepSeek 流式聊天 API
import { z } from 'zod'
import { DEEP_SEEK_MODEL_ID, DEFAULT_TEMPERATURE, DEFAULT_TOP_P, SUMMARY_LENGTH } from '~/common/constants'

// 输入验证 Schema
const streamInputSchema = z.object({
  messages: z
    .array(
      z.object({
        role: z.enum(['system', 'user', 'assistant']),
        content: z.string(),
      }),
    )
    .min(1),
  temperature: z.number().min(0).max(2).optional().default(DEFAULT_TEMPERATURE),
  max_tokens: z.number().min(1).max(16000).optional().default(SUMMARY_LENGTH),
  top_p: z.number().min(0).max(1).optional().default(DEFAULT_TOP_P),
  stream: z.boolean().optional().default(true),
})

export default defineEventHandler(async (event) => {
  try {
    // 读取请求体
    const body = await readBody(event)

    // 验证输入数据
    const input = streamInputSchema.parse(body)

    // 获取配置
    const config = useRuntimeConfig()
    const arkApiKey = config.arkApiKey

    if (!arkApiKey || !DEEP_SEEK_MODEL_ID) {
      throw createError({
        statusCode: 500,
        statusMessage: 'DeepSeek服务配置错误：缺少ARK_API_KEY或DEEP_SEEK_MODEL_ID',
      })
    }

    // 构建请求体
    const requestBody = {
      model: DEEP_SEEK_MODEL_ID,
      messages: input.messages,
      temperature: input.temperature,
      max_tokens: input.max_tokens,
      top_p: input.top_p,
      stream: input.stream,
    }

    // 如果不需要流式输出，直接返回完整响应
    if (!input.stream) {
      const response = await fetch('https://ark.cn-beijing.volces.com/api/v3/chat/completions', {
        method: 'POST',
        headers: {
          Authorization: `Bearer ${arkApiKey}`,
          'Content-Type': 'application/json',
        },
        body: JSON.stringify(requestBody),
      })

      if (!response.ok) {
        const errorText = await response.text()
        throw createError({
          statusCode: response.status,
          statusMessage: `DeepSeek API错误: ${response.status} ${response.statusText} - ${errorText}`,
        })
      }

      const data = await response.json()
      const content = data.choices?.[0]?.message?.content || ''

      return content
    }

    // 设置 CORS 和 SSE 头部
    setHeaders(event, {
      'Content-Type': 'text/event-stream',
      'Cache-Control': 'no-cache',
      Connection: 'keep-alive',
      'Access-Control-Allow-Origin': '*',
      'Access-Control-Allow-Methods': 'POST',
      'Access-Control-Allow-Headers': 'Content-Type',
    })

    // 发送流式请求到火山引擎Chat API
    const response = await fetch('https://ark.cn-beijing.volces.com/api/v3/chat/completions', {
      method: 'POST',
      headers: {
        Authorization: `Bearer ${arkApiKey}`,
        'Content-Type': 'application/json',
      },
      body: JSON.stringify(requestBody),
    })

    if (!response.ok) {
      const errorText = await response.text()
      throw createError({
        statusCode: response.status,
        statusMessage: `DeepSeek API错误: ${response.status} ${response.statusText} - ${errorText}`,
      })
    }

    if (!response.body) {
      throw createError({
        statusCode: 500,
        statusMessage: '未收到流式响应体',
      })
    }

    // 创建可读流处理器
    const reader = response.body.getReader()
    const decoder = new TextDecoder()

    // 创建响应流
    return new ReadableStream({
      async start(controller) {
        try {
          // eslint-disable-next-line no-constant-condition
          while (true) {
            const { done, value } = await reader.read()

            if (done) {
              controller.enqueue('data: [DONE]\n\n')
              controller.close()
              break
            }

            const chunk = decoder.decode(value, { stream: true })
            const lines = chunk.split('\n')

            for (const line of lines) {
              const trimmedLine = line.trim()

              if (trimmedLine.startsWith('data: ')) {
                const dataStr = trimmedLine.slice(6)

                if (dataStr === '[DONE]') {
                  controller.enqueue('data: [DONE]\n\n')
                  controller.close()
                  return
                }

                try {
                  const data = JSON.parse(dataStr)
                  const content = data.choices?.[0]?.delta?.content

                  if (content) {
                    // 发送内容块
                    controller.enqueue(`data: ${JSON.stringify({ content })}\n\n`)
                  }
                } catch (parseError) {
                  console.error('解析流式数据错误:', parseError, dataStr)
                }
              }
            }
          }
        } catch (error) {
          console.error('流式处理错误:', error)
          controller.enqueue(`data: ${JSON.stringify({ error: '流式处理失败' })}\n\n`)
          controller.close()
        } finally {
          reader.releaseLock()
        }
      },
    })
  } catch (error) {
    console.error('DeepSeek 流式API错误:', error)

    const errorMessage = error instanceof Error ? error.message : '服务器内部错误'

    // 对于非流式请求，返回简单的错误响应
    const body = await readBody(event).catch(() => ({}))
    if (body.stream === false) {
      throw createError({
        statusCode: 500,
        statusMessage: errorMessage,
      })
    }

    return new Response(`data: ${JSON.stringify({ error: errorMessage })}\n\n`, {
      status: 500,
      headers: {
        'Content-Type': 'text/event-stream',
      },
    })
  }
})

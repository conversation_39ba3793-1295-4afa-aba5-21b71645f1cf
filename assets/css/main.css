@tailwind base;
@tailwind components;
@tailwind utilities;

/* 字体配置 */
@font-face {
  font-family: 'Geist';
  src: url('https://fonts.googleapis.com/css2?family=Geist:wght@400;500;600;700&display=swap');
  font-display: swap;
}

@font-face {
  font-family: 'Geist Mono';
  src: url('https://fonts.googleapis.com/css2?family=Geist+Mono:wght@400;500;600;700&display=swap');
  font-display: swap;
}

@layer base {
  :root {
    --background: 0 0% 100%;
    --foreground: 0 0% 3.9%;
    --card: 0 0% 100%;
    --card-foreground: 0 0% 3.9%;
    --popover: 0 0% 100%;
    --popover-foreground: 0 0% 3.9%;
    --primary: 0 0% 9%;
    --primary-foreground: 0 0% 98%;
    --secondary: 0 0% 96.1%;
    --secondary-foreground: 0 0% 9%;
    --muted: 0 0% 96.1%;
    --muted-foreground: 0 0% 45.1%;
    --accent: 0 0% 96.1%;
    --accent-foreground: 0 0% 9%;
    --destructive: 0 84.2% 60.2%;
    --destructive-foreground: 0 0% 98%;
    --border: 0 0% 89.8%;
    --input: 0 0% 89.8%;
    --ring: 0 0% 3.9%;
    --chart-1: 12 76% 61%;
    --chart-2: 173 58% 39%;
    --chart-3: 197 37% 24%;
    --chart-4: 43 74% 66%;
    --chart-5: 27 87% 67%;
    --radius: 0.5rem;
  }

  .dark {
    --background: 0 0% 3.9%;
    --foreground: 0 0% 98%;
    --card: 0 0% 3.9%;
    --card-foreground: 0 0% 98%;
    --popover: 0 0% 3.9%;
    --popover-foreground: 0 0% 98%;
    --primary: 0 0% 98%;
    --primary-foreground: 0 0% 9%;
    --secondary: 0 0% 14.9%;
    --secondary-foreground: 0 0% 98%;
    --muted: 0 0% 14.9%;
    --muted-foreground: 0 0% 63.9%;
    --accent: 0 0% 14.9%;
    --accent-foreground: 0 0% 98%;
    --destructive: 0 62.8% 30.6%;
    --destructive-foreground: 0 0% 98%;
    --border: 0 0% 14.9%;
    --input: 0 0% 14.9%;
    --ring: 0 0% 83.1%;
    --chart-1: 220 70% 50%;
    --chart-2: 160 60% 45%;
    --chart-3: 30 80% 55%;
    --chart-4: 280 65% 60%;
    --chart-5: 340 75% 55%;
  }
}


/* 动画 */
@keyframes accordion-down {
  from {
    height: 0;
  }

  to {
    height: var(--radix-accordion-content-height);
  }
}

@keyframes accordion-up {
  from {
    height: var(--radix-accordion-content-height);
  }

  to {
    height: 0;
  }
}

.animate-accordion-down {
  animation: accordion-down 0.2s ease-out;
}

.animate-accordion-up {
  animation: accordion-up 0.2s ease-out;
}

/* nuxt-ui-colors 变量定义 */
:root {
--color-primary-50: 250 245 255;
--color-primary-100: 243 232 255;
--color-primary-200: 233 213 255;
--color-primary-300: 216 180 254;
--color-primary-400: 192 132 252;
--color-primary-500: 168 85 247;
--color-primary-600: 147 51 234;
--color-primary-700: 126 34 206;
--color-primary-800: 107 33 168;
--color-primary-900: 88 28 135;
--color-primary-950: 59 7 100;
--color-primary-DEFAULT: var(--color-primary-500);

--color-gray-50: 249 250 251;
--color-gray-100: 243 244 246;
--color-gray-200: 229 231 235;
--color-gray-300: 209 213 219;
--color-gray-400: 156 163 175;
--color-gray-500: 107 114 128;
--color-gray-600: 75 85 99;
--color-gray-700: 55 65 81;
--color-gray-800: 31 41 55;
--color-gray-900: 17 24 39;
--color-gray-950: 3 7 18;
}

.dark {
  --color-primary-DEFAULT: var(--color-primary-400);
}

body {
  background-color: rgb(var(--color-gray-900));
  color: rgb(var(--color-gray-100));
}

.dark body {
  background-color: rgb(var(--color-gray-900));
  color: rgb(var(--color-gray-100));
}
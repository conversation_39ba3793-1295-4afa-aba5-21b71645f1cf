import { DEFAULT_TEMPERATURE, DEFAULT_TOP_P, SUMMARY_LENGTH } from '~/common/constants'
import { getSummaryPrompt } from '~/common/prompts'
import type { CustomTermsJson, SummaryResult } from '~/common/prompts'
import { validSummary } from '~/utils/validation/validators/summaryValidator'

/**
 * 使用DeepSeek API调用摘要提取功能
 * @param sourceContent 源文本内容
 * @param customTermsJson 可选的已存在术语
 * @returns Promise<SummaryResult>
 */
export async function extractSummary(sourceContent: string, customTermsJson?: CustomTermsJson): Promise<SummaryResult> {
  try {
    // 获取提示词
    const prompt = getSummaryPrompt(sourceContent, customTermsJson)

    // 使用Nuxt的$trpc客户端调用DeepSeek API
    const { $trpc } = useNuxtApp()

    const response = await $trpc.deepseek.chat.mutate({
      messages: [
        {
          role: 'user',
          content: prompt,
        },
      ],
      temperature: DEFAULT_TEMPERATURE,
      top_p: DEFAULT_TOP_P,
      max_tokens: SUMMARY_LENGTH,
    })

    // 提取JSON内容
    const content = response.choices[0]?.message?.content || ''
    const jsonMatch = content.match(/```json\n([\s\S]*?)\n```/)

    if (!jsonMatch) {
      throw new Error('无法解析AI返回的JSON格式')
    }

    const result = JSON.parse(jsonMatch[1]) as SummaryResult
    console.log('%c AT 🥝 result 🥝-41', 'font-size:13px; background:#ccf54f; color:#ffff93;', result)

    // 校验返回数据格式
    const validation = validSummary(result)
    if (validation.status === 'error') {
      throw new Error(`数据格式校验失败: ${validation.message}`)
    }

    return result
  } catch (error) {
    console.error('提取摘要失败:', error)
    throw new Error(`摘要提取失败: ${error instanceof Error ? error.message : '未知错误'}`)
  }
}

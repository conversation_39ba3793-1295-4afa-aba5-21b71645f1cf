<script setup>
import { onMounted, ref, watch } from 'vue'
import { usePlayerPageLogic } from '~/composables/player/usePlayerPageLogic'
import { usePlayerStore } from '~/stores/playerStore'

const playerStore = usePlayerStore()
const audioRef = ref(null)

const {
  currentStatement,
  isLoading,
  error,
  hasData,
  isFirstStatement,
  isLastStatement,
  loadFromCache,
  goToNext,
  goToPrevious,
  goToFirst,
  replayCurrent,
  handleAudioUpload,
} = usePlayerPageLogic()

// 监听 playerStore 状态变化来控制音频播放（仅在客户端）
if (import.meta.client) {
  watch(
    () => playerStore.status,
    (newStatus) => {
      if (audioRef.value) {
        if (newStatus === 'playing') {
          audioRef.value.play()
        } else if (newStatus === 'paused') {
          audioRef.value.pause()
        }
      }
    },
  )

  // 监听跳转请求
  watch(
    () => playerStore.seekRequest,
    (seekRequest) => {
      if (seekRequest && audioRef.value) {
        audioRef.value.currentTime = seekRequest.time
      }
    },
  )
}

// 处理文件上传
const handleFileUpload = (event) => {
  const file = event.target.files?.[0]
  if (file) {
    handleAudioUpload(file)
  }
}

onMounted(() => {
  loadFromCache()
})

useHead({
  title: '字幕播放器',
  meta: [{ name: 'description', content: '字幕播放器，支持音频播放和字幕显示' }],
})
</script>

<template>
  <div class="min-h-screen bg-gray-50 flex items-center justify-center p-4">
    <div class="max-w-4xl w-full">
      <div class="bg-white rounded-lg shadow-lg p-8">
        <!-- 页面标题 -->
        <div class="text-center mb-8">
          <h1 class="text-3xl font-bold text-gray-800 mb-4">字幕播放器</h1>
        </div>

        <!-- 内容区域 -->
        <div class="space-y-6">
          <!-- 加载状态 -->
          <div v-if="isLoading" class="text-center py-8">
            <div class="text-gray-600">正在加载...</div>
          </div>

          <!-- 错误状态 -->
          <div v-else-if="error" class="text-center py-8">
            <div class="text-red-600 mb-4">{{ error }}</div>
            <NuxtLink to="/" class="text-blue-600 hover:text-blue-800 underline">
              返回主页
            </NuxtLink>
          </div>

          <!-- 音频文件上传提示 -->
          <div v-else-if="hasData && !playerStore.mediaUrl" class="text-center py-8 text-gray-500">
            <div class="text-gray-600 mb-4">请选择音频文件以开始播放</div>
            <input type="file" accept="audio/*,video/*" class="block mx-auto mb-4" @change="handleFileUpload" />
          </div>

          <!-- 字幕显示区域 -->
          <div v-else-if="hasData && currentStatement" class="text-center py-12">
            <!-- 音频播放器（隐藏但需要存在） -->
            <ClientOnly>
              <audio
                v-if="playerStore.mediaUrl"
                ref="audioRef"
                :src="playerStore.mediaUrl"
                class="hidden"
                @loadedmetadata="playerStore.setDuration($event.target.duration)"
                @timeupdate="playerStore.currentTime = $event.target.currentTime"
              ></audio>
            </ClientOnly>

            <!-- 英文字幕 -->
            <div class="text-2xl font-medium text-gray-800 mb-6 leading-relaxed">
              {{ currentStatement.english }}
            </div>

            <!-- 中文字幕 -->
            <div class="text-xl text-gray-600 leading-relaxed">
              {{ currentStatement.chinese }}
            </div>
          </div>

          <!-- 控制按钮区域 -->
          <div v-if="hasData" class="flex justify-center items-center space-x-4 py-8">
            <!-- 重来按钮 -->
            <button class="px-6 py-3 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors" @click="goToFirst">
              重来
            </button>

            <!-- 上一句按钮 -->
            <button
              class="px-6 py-3 bg-gray-600 text-white rounded-lg hover:bg-gray-700 transition-colors disabled:bg-gray-400 disabled:cursor-not-allowed"
              :disabled="isFirstStatement"
              @click="goToPrevious"
            >
              上一句
            </button>

            <!-- 重播按钮 -->
            <button class="px-6 py-3 bg-green-600 text-white rounded-lg hover:bg-green-700 transition-colors" @click="replayCurrent">
              重播
            </button>

            <!-- 下一句按钮 -->
            <button
              class="px-6 py-3 bg-gray-600 text-white rounded-lg hover:bg-gray-700 transition-colors disabled:bg-gray-400 disabled:cursor-not-allowed"
              :disabled="isLastStatement"
              @click="goToNext"
            >
              下一句
            </button>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

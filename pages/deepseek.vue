<script setup>
import { SUMMARY_LENGTH, DEFAULT_TEMPERATURE, DEFAULT_TOP_P } from '~/common/constants'

// 页面元信息
useHead({
  title: 'DeepSeek V3 演示 - Julebu SRT Editor',
  meta: [{ name: 'description', content: '基于火山引擎方舟平台的 DeepSeek V3 模型聊天功能演示页面' }],
})

// tRPC 客户端
const { $trpc } = useNuxtApp()

// 响应式状态
const messages = ref([])
const userInput = ref('')
const systemPrompt = ref('你是一个有用的AI助手，能够帮助用户解答各种问题。请用中文回答。')
const isLoading = ref(false)
const isStreaming = ref(false)
const streamingContent = ref('')
const lastResponse = ref(null)
const lastError = ref(null)
const lastTokenUsage = ref(null)

// 配置参数
const config = ref({
  temperature: DEFAULT_TEMPERATURE,
  max_tokens: SUMMARY_LENGTH,
  top_p: DEFAULT_TOP_P,
})

// 计算属性：显示的消息列表（包含系统消息）
const displayMessages = computed(() => {
  const systemMessage = { role: 'system', content: systemPrompt.value }
  return [systemMessage, ...messages.value]
})

// 获取完整的消息列表（用于API调用）
const getFullMessages = () => {
  return [{ role: 'system', content: systemPrompt.value }, ...messages.value]
}

// 清空对话
const clearChat = () => {
  messages.value = []
  streamingContent.value = ''
  lastResponse.value = null
  lastError.value = null
  lastTokenUsage.value = null
}

// 非流式发送消息
const sendMessage = async () => {
  if (!userInput.value.trim() || isLoading.value) return

  const userMessage = { role: 'user', content: userInput.value.trim() }
  messages.value.push(userMessage)

  const inputText = userInput.value.trim()
  userInput.value = ''
  isLoading.value = true
  lastError.value = null

  try {
    const response = await $trpc.deepseek.chat.mutate({
      messages: getFullMessages(),
      temperature: config.value.temperature,
      max_tokens: config.value.max_tokens,
      top_p: config.value.top_p,
    })

    const assistantMessage = {
      role: 'assistant',
      content: response.choices[0]?.message?.content || '没有收到回复',
    }

    messages.value.push(assistantMessage)
    lastResponse.value = response
    lastTokenUsage.value = response.usage
  } catch (error) {
    console.error('发送消息失败:', error)
    lastError.value = error.message || error.toString()

    // 如果发生错误，移除用户消息
    if (messages.value.length > 0 && messages.value[messages.value.length - 1].content === inputText) {
      messages.value.pop()
    }

    // 恢复用户输入
    userInput.value = inputText
  } finally {
    isLoading.value = false
  }
}

// 流式发送消息
const sendStreamMessage = async () => {
  if (!userInput.value.trim() || isLoading.value) return

  const userMessage = { role: 'user', content: userInput.value.trim() }
  messages.value.push(userMessage)

  const inputText = userInput.value.trim()
  userInput.value = ''
  isLoading.value = true
  isStreaming.value = true
  streamingContent.value = ''
  lastError.value = null

  try {
    // 使用标准 fetch API 调用流式接口
    const response = await fetch('/api/deepseek/stream', {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify({
        messages: getFullMessages(),
        temperature: config.value.temperature,
        max_tokens: config.value.max_tokens,
        top_p: config.value.top_p,
      }),
    })

    if (!response.ok) {
      throw new Error(`HTTP ${response.status}: ${response.statusText}`)
    }

    if (!response.body) {
      throw new Error('未收到响应流')
    }

    // 创建流读取器
    const reader = response.body.getReader()
    const decoder = new TextDecoder()

    try {
      // eslint-disable-next-line no-constant-condition
      while (true) {
        const { done, value } = await reader.read()

        if (done) break

        const chunk = decoder.decode(value, { stream: true })
        const lines = chunk.split('\n')

        for (const line of lines) {
          const trimmedLine = line.trim()

          if (trimmedLine.startsWith('data: ')) {
            const dataStr = trimmedLine.slice(6)

            if (dataStr === '[DONE]') {
              // 流式完成，将内容添加到消息列表
              if (streamingContent.value) {
                const assistantMessage = {
                  role: 'assistant',
                  content: streamingContent.value,
                }
                messages.value.push(assistantMessage)
              }

              streamingContent.value = ''
              isLoading.value = false
              isStreaming.value = false
              return
            }

            try {
              const data = JSON.parse(dataStr)

              if (data.error) {
                throw new Error(data.error)
              }

              if (data.content) {
                streamingContent.value += data.content
              }
            } catch (parseError) {
              console.error('解析流式数据错误:', parseError, dataStr)
            }
          }
        }
      }
    } finally {
      reader.releaseLock()
    }

    // 如果循环正常结束，也要处理剩余内容
    if (streamingContent.value) {
      const assistantMessage = {
        role: 'assistant',
        content: streamingContent.value,
      }
      messages.value.push(assistantMessage)
    }
  } catch (error) {
    console.error('流式聊天失败:', error)
    lastError.value = error.message || error.toString()

    // 如果发生错误，移除用户消息并恢复输入
    if (messages.value.length > 0 && messages.value[messages.value.length - 1].content === inputText) {
      messages.value.pop()
    }
    userInput.value = inputText
  } finally {
    streamingContent.value = ''
    isLoading.value = false
    isStreaming.value = false
  }
}

// 页面加载时的初始化
onMounted(() => {
  // 可以在这里添加一些初始化逻辑
  console.log('DeepSeek V3 演示页面已加载')
})
</script>

<template>
  <div class="min-h-screen bg-gray-50">
    <UContainer class="py-8">
      <!-- 页面标题 -->
      <div class="text-center mb-8">
        <h1 class="text-3xl font-bold text-gray-900 mb-2">DeepSeek V3 聊天演示</h1>
        <p class="text-gray-600">基于火山引擎方舟平台的 DeepSeek V3 模型聊天功能测试</p>
      </div>

      <div class="grid grid-cols-1 lg:grid-cols-4 gap-6">
        <!-- 参数配置面板 -->
        <div class="lg:col-span-1">
          <UCard>
            <template #header>
              <h3 class="text-lg font-semibold">参数配置</h3>
            </template>

            <div class="space-y-4">
              <div>
                <label class="block text-sm font-medium text-gray-700 mb-1">Temperature</label>
                <URange v-model="config.temperature" :min="0" :max="2" :step="0.1" />
                <div class="text-xs text-gray-500 mt-1">当前值: {{ config.temperature }}</div>
              </div>

              <div>
                <label class="block text-sm font-medium text-gray-700 mb-1">Max Tokens</label>
                <URange v-model="config.max_tokens" :min="100" :max="8000" :step="100" />
                <div class="text-xs text-gray-500 mt-1">当前值: {{ config.max_tokens }}</div>
              </div>

              <div>
                <label class="block text-sm font-medium text-gray-700 mb-1">Top P</label>
                <URange v-model="config.top_p" :min="0" :max="1" :step="0.05" />
                <div class="text-xs text-gray-500 mt-1">当前值: {{ config.top_p }}</div>
              </div>

              <div>
                <label class="block text-sm font-medium text-gray-700 mb-1">系统提示词</label>
                <UTextarea v-model="systemPrompt" placeholder="输入系统提示词..." :rows="3" />
              </div>

              <UButton color="red" variant="outline" block :disabled="isLoading" @click="clearChat"> 清空对话 </UButton>
            </div>
          </UCard>
        </div>

        <!-- 聊天界面 -->
        <div class="lg:col-span-3">
          <UCard>
            <template #header>
              <div class="flex justify-between items-center">
                <h3 class="text-lg font-semibold">聊天对话</h3>
                <div class="flex items-center space-x-2">
                  <UBadge v-if="isLoading" color="yellow">处理中...</UBadge>
                  <UBadge v-if="isStreaming" color="blue">流式输出中...</UBadge>
                  <UBadge v-if="lastTokenUsage" color="green"> {{ lastTokenUsage.total_tokens }} tokens </UBadge>
                </div>
              </div>
            </template>

            <!-- 聊天消息列表 -->
            <div class="space-y-4 mb-6 max-h-96 overflow-y-auto">
              <div
                v-for="(message, index) in displayMessages"
                :key="index"
                class="flex"
                :class="message.role === 'user' ? 'justify-end' : 'justify-start'"
              >
                <div
                  class="max-w-xs lg:max-w-md px-4 py-2 rounded-lg"
                  :class="{
                    'bg-blue-500 text-white': message.role === 'user',
                    'bg-gray-200 text-gray-800': message.role === 'assistant',
                    'bg-green-100 text-green-800 text-sm': message.role === 'system',
                  }"
                >
                  <div class="text-xs opacity-75 mb-1 capitalize">{{ message.role }}</div>
                  <div class="whitespace-pre-wrap">{{ message.content }}</div>
                </div>
              </div>

              <!-- 流式输出显示 -->
              <div v-if="streamingContent" class="flex justify-start">
                <div class="max-w-xs lg:max-w-md px-4 py-2 rounded-lg bg-yellow-100 text-yellow-800">
                  <div class="text-xs opacity-75 mb-1">Assistant (流式输出)</div>
                  <div class="whitespace-pre-wrap">{{ streamingContent }}<span class="animate-pulse">▊</span></div>
                </div>
              </div>
            </div>

            <!-- 输入区域 -->
            <div class="border-t pt-4">
              <div class="flex space-x-2 mb-3">
                <UTextarea
                  v-model="userInput"
                  placeholder="输入您的消息..."
                  :rows="3"
                  class="flex-1"
                  @keydown.ctrl.enter="sendMessage"
                  @keydown.meta.enter="sendMessage"
                />
              </div>

              <div class="flex space-x-2">
                <UButton
                  :disabled="!userInput.trim() || isLoading"
                  :loading="isLoading && !isStreaming"
                  class="flex-1"
                  @click="sendMessage"
                >
                  发送消息
                </UButton>

                <UButton
                  :disabled="!userInput.trim() || isLoading"
                  :loading="isStreaming"
                  color="green"
                  class="flex-1"
                  @click="sendStreamMessage"
                >
                  流式发送
                </UButton>
              </div>

              <div class="text-xs text-gray-500 mt-2">按 Ctrl+Enter (Mac: Cmd+Enter) 快速发送</div>
            </div>
          </UCard>

          <!-- 调试信息 -->
          <UCard v-if="lastResponse || lastError" class="mt-4">
            <template #header>
              <h3 class="text-lg font-semibold">调试信息</h3>
            </template>

            <div class="space-y-4">
              <div v-if="lastError" class="p-3 bg-red-50 border border-red-200 rounded">
                <h4 class="font-medium text-red-800 mb-2">错误信息</h4>
                <pre class="text-sm text-red-700 whitespace-pre-wrap">{{ lastError }}</pre>
              </div>

              <div v-if="lastResponse" class="p-3 bg-green-50 border border-green-200 rounded">
                <h4 class="font-medium text-green-800 mb-2">最后响应</h4>
                <pre class="text-sm text-green-700 whitespace-pre-wrap max-h-40 overflow-y-auto">{{
                  JSON.stringify(lastResponse, null, 2)
                }}</pre>
              </div>
            </div>
          </UCard>
        </div>
      </div>
    </UContainer>
  </div>
</template>

<style scoped>
/* 自定义滚动条样式 */
.overflow-y-auto::-webkit-scrollbar {
  width: 6px;
}

.overflow-y-auto::-webkit-scrollbar-track {
  background: #f1f1f1;
  border-radius: 3px;
}

.overflow-y-auto::-webkit-scrollbar-thumb {
  background: #c1c1c1;
  border-radius: 3px;
}

.overflow-y-auto::-webkit-scrollbar-thumb:hover {
  background: #a8a8a8;
}

/* 流式输出动画 */
@keyframes pulse {
  0%,
  50% {
    opacity: 1;
  }
  51%,
  100% {
    opacity: 0;
  }
}

.animate-pulse {
  animation: pulse 1s infinite;
}
</style>

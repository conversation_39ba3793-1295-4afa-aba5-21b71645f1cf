<script setup lang="ts">
// 页面元信息配置
useHead({
  title: '功能导航 - Julebu SRT Editor',
  meta: [{ name: 'description', content: '字幕编辑器功能导航页面，包含AI智能服务、视频处理、文本翻译等多种功能' }],
})

// 页面数据结构
const pages = ref([
  {
    id: 'index',
    title: '字幕编辑器',
    description: '专业的字幕编辑工具，支持音频波形显示和实时预览',
    icon: 'i-heroicons-document-text',
    category: 'core',
    path: '/',
    featured: true,
  },
  {
    id: 'demo',
    title: '视频演示',
    description: 'Video.js视频播放器演示，支持字幕切换和加载',
    icon: 'i-heroicons-film',
    category: 'core',
    path: '/demo',
    featured: true,
  },
  {
    id: 'translation-demo',
    title: '翻译主题分析系统',
    description: '完整的翻译主题分析系统演示，包含多用户会话、文件锁、术语提取等功能',
    icon: 'i-heroicons-globe-alt',
    category: 'ai',
    path: '/translation-demo',
    featured: true,
  },
  {
    id: 'deepseek',
    title: 'DeepSeek AI聊天',
    description: '基于火山引擎方舟平台的DeepSeek V3模型聊天功能',
    icon: 'i-heroicons-chat-bubble-left-right',
    category: 'ai',
    path: '/deepseek',
    featured: false,
  },
  {
    id: 'smartsubtitles',
    title: '智能字幕生成',
    description: '使用火山引擎AI技术自动生成字幕，支持多语言识别',
    icon: 'i-heroicons-sparkles',
    category: 'ai',
    path: '/smartsubtitles',
    featured: true,
  },
  {
    id: 'summary-demo',
    title: '文本摘要提取',
    description: '基于AI的智能文本内容分析和术语提取工具',
    icon: 'i-heroicons-document-magnifying-glass',
    category: 'ai',
    path: '/summary-demo',
    featured: false,
  },
  {
    id: 'translate',
    title: '文本翻译',
    description: '基于火山引擎的智能文本翻译服务',
    icon: 'i-heroicons-language',
    category: 'ai',
    path: '/translate',
    featured: false,
  },
  {
    id: 'music-service',
    title: '音乐服务',
    description: '网易云音乐API演示，支持歌曲搜索和歌词获取',
    icon: 'i-heroicons-musical-note',
    category: 'extension',
    path: '/music-service',
    featured: false,
  },
])

// 搜索和筛选功能
const searchQuery = ref('')
const selectedCategory = ref('all')

// 分类选项
const categories = [
  { label: '全部', value: 'all' },
  { label: '核心编辑', value: 'core' },
  { label: 'AI智能服务', value: 'ai' },
  { label: '扩展服务', value: 'extension' },
]

// 过滤后的页面列表
const filteredPages = computed(() => {
  let filtered = pages.value

  // 按分类筛选
  if (selectedCategory.value !== 'all') {
    filtered = filtered.filter((page) => page.category === selectedCategory.value)
  }

  // 按搜索关键词筛选
  if (searchQuery.value.trim()) {
    const query = searchQuery.value.toLowerCase()
    filtered = filtered.filter((page) => page.title.toLowerCase().includes(query) || page.description.toLowerCase().includes(query))
  }

  return filtered
})

// 分类标题映射
const categoryTitles = {
  core: '📝 核心编辑功能',
  ai: '🤖 AI智能服务',
  extension: '🔧 扩展服务',
}

// 按分类分组的页面
const pagesByCategory = computed(() => {
  const grouped: Record<string, typeof pages.value> = {}

  filteredPages.value.forEach((page) => {
    if (!grouped[page.category]) {
      grouped[page.category] = []
    }
    grouped[page.category].push(page)
  })

  return grouped
})

// 页面跳转处理
const handlePageNavigation = (path: string) => {
  navigateTo(path)
}
</script>

<template>
  <div class="min-h-screen bg-gradient-to-b from-gray-900 to-gray-800">
    <div class="container mx-auto px-6 py-8 max-w-7xl">
      <!-- 页面标题区域 -->
      <div class="text-center mb-12">
        <h1 class="text-4xl font-bold text-white mb-4">功能导航</h1>
        <p class="text-xl text-gray-300 max-w-2xl mx-auto">
          探索字幕编辑器的强大功能，包含AI智能服务、视频处理、文本翻译等多种工具
        </p>
      </div>

      <!-- 搜索和筛选区域 -->
      <div class="mb-8 space-y-4">
        <!-- 搜索框 -->
        <div class="flex justify-center">
          <div class="w-full max-w-md">
            <UInput
              v-model="searchQuery"
              icon="i-heroicons-magnifying-glass"
              placeholder="搜索功能..."
              size="lg"
              color="white"
              variant="outline"
            />
          </div>
        </div>

        <!-- 分类筛选标签 -->
        <div class="flex justify-center">
          <div class="flex flex-wrap gap-2">
            <UButton
              v-for="category in categories"
              :key="category.value"
              :variant="selectedCategory === category.value ? 'solid' : 'outline'"
              :color="selectedCategory === category.value ? 'primary' : 'white'"
              size="sm"
              @click="selectedCategory = category.value"
            >
              {{ category.label }}
            </UButton>
          </div>
        </div>
      </div>

      <!-- 功能卡片区域 -->
      <div class="space-y-12">
        <!-- 按分类显示 -->
        <div v-for="(categoryPages, category) in pagesByCategory" :key="category" class="space-y-6">
          <h2 class="text-2xl font-semibold text-white text-center">
            {{ categoryTitles[category as keyof typeof categoryTitles] }}
          </h2>

          <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
            <div
              v-for="page in categoryPages"
              :key="page.id"
              class="group cursor-pointer transform transition-all duration-300 hover:scale-105"
              tabindex="0"
              :aria-label="`导航到${page.title}`"
              role="button"
              @click="handlePageNavigation(page.path)"
              @keypress.enter="handlePageNavigation(page.path)"
            >
              <div
                class="bg-white dark:bg-gray-800 rounded-xl shadow-lg hover:shadow-2xl transition-all duration-300 p-6 h-full border border-gray-200 dark:border-gray-700"
              >
                <!-- 特色标记 -->
                <div v-if="page.featured" class="flex justify-end mb-2">
                  <span
                    class="inline-flex items-center px-2 py-1 rounded-full text-xs font-medium bg-blue-100 text-blue-800 dark:bg-blue-900 dark:text-blue-200"
                  >
                    推荐
                  </span>
                </div>

                <!-- 图标 -->
                <div class="flex justify-center mb-4">
                  <UIcon
                    :name="page.icon"
                    class="w-12 h-12 text-blue-600 dark:text-blue-400 group-hover:text-blue-700 dark:group-hover:text-blue-300 transition-colors duration-300"
                  />
                </div>

                <!-- 标题 -->
                <h3
                  class="text-xl font-semibold text-gray-900 dark:text-white text-center mb-3 group-hover:text-blue-600 dark:group-hover:text-blue-400 transition-colors duration-300"
                >
                  {{ page.title }}
                </h3>

                <!-- 描述 -->
                <p class="text-gray-600 dark:text-gray-300 text-center text-sm leading-relaxed">
                  {{ page.description }}
                </p>

                <!-- 跳转指示 -->
                <div class="flex justify-center mt-4">
                  <UIcon
                    name="i-heroicons-arrow-right"
                    class="w-5 h-5 text-gray-400 group-hover:text-blue-600 dark:group-hover:text-blue-400 transform group-hover:translate-x-1 transition-all duration-300"
                  />
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>

      <!-- 无搜索结果提示 -->
      <div v-if="filteredPages.length === 0" class="text-center py-12">
        <UIcon name="i-heroicons-exclamation-triangle" class="w-16 h-16 text-gray-400 mx-auto mb-4" />
        <h3 class="text-xl font-semibold text-gray-300 mb-2">未找到匹配的功能</h3>
        <p class="text-gray-400">尝试使用不同的搜索关键词或选择其他分类</p>
        <UButton
          variant="outline"
          color="white"
          class="mt-4"
          @click="
            () => {
              searchQuery = ''
              selectedCategory = 'all'
            }
          "
        >
          重置筛选
        </UButton>
      </div>

      <!-- 页面底部 -->
      <div class="text-center mt-16 pt-8 border-t border-gray-700">
        <p class="text-gray-400 text-sm">
          © 2024 Julebu SRT Editor - 专业的字幕编辑解决方案
        </p>
      </div>
    </div>
  </div>
</template>

<style scoped>
/* 卡片悬停动画效果 */
.group:hover .group-hover\:scale-105 {
  transform: scale(1.05);
}

/* 深色模式适配 */
@media (prefers-color-scheme: dark) {
  .bg-white {
    background-color: rgb(31 41 55);
  }
}

/* 键盘导航焦点样式 */
[tabindex]:focus {
  outline: 2px solid #3b82f6;
  outline-offset: 2px;
  border-radius: 0.75rem;
}

/* 响应式网格调整 */
@media (max-width: 768px) {
  .grid-cols-1.md\:grid-cols-2.lg\:grid-cols-3 {
    grid-template-columns: repeat(1, minmax(0, 1fr));
  }
}

@media (min-width: 768px) and (max-width: 1024px) {
  .grid-cols-1.md\:grid-cols-2.lg\:grid-cols-3 {
    grid-template-columns: repeat(2, minmax(0, 1fr));
  }
}

@media (min-width: 1024px) {
  .grid-cols-1.md\:grid-cols-2.lg\:grid-cols-3 {
    grid-template-columns: repeat(3, minmax(0, 1fr));
  }
}
</style>

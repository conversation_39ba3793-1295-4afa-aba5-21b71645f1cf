<template>
  <div class="p-6 max-w-4xl mx-auto">
    <h1 class="text-2xl font-bold mb-6">课程包Store测试页面</h1>
    
    <!-- 错误提示 -->
    <UAlert 
      v-if="coursePackStore.hasError" 
      color="red" 
      variant="soft" 
      :title="coursePackStore.error?.message"
      class="mb-4"
    />
    
    <!-- 加载状态 -->
    <div v-if="coursePackStore.isLoading" class="flex items-center gap-2 mb-4">
      <UIcon name="i-heroicons-arrow-path" class="animate-spin" />
      <span>加载中...</span>
    </div>
    
    <!-- 操作按钮 -->
    <div class="flex gap-4 mb-6">
      <UButton 
        @click="coursePackStore.fetchCoursePackList()"
        :loading="coursePackStore.coursePackListLoading"
        color="primary"
      >
        获取课程包列表
      </UButton>
      
      <UButton 
        @click="coursePackStore.reset()"
        color="gray"
        variant="outline"
      >
        重置状态
      </UButton>
    </div>
    
    <!-- 状态信息 -->
    <div class="grid grid-cols-1 md:grid-cols-3 gap-4 mb-6">
      <UCard>
        <template #header>
          <h3 class="text-lg font-semibold">状态信息</h3>
        </template>
        <div class="space-y-2">
          <div>状态: <UBadge :color="getStatusColor(coursePackStore.status)">{{ coursePackStore.status }}</UBadge></div>
          <div>课程包数量: {{ coursePackStore.coursePackList.length }}</div>
          <div>选中课程包ID: {{ coursePackStore.selectedCoursePackId || '无' }}</div>
          <div>选中课程ID: {{ coursePackStore.selectedCourseId || '无' }}</div>
        </div>
      </UCard>
      
      <UCard v-if="coursePackStore.currentCoursePack">
        <template #header>
          <h3 class="text-lg font-semibold">当前课程包</h3>
        </template>
        <div class="space-y-2">
          <div class="font-medium">{{ coursePackStore.currentCoursePack.title }}</div>
          <div class="text-sm text-gray-600">{{ coursePackStore.currentCoursePack.description }}</div>
          <div>课程数量: {{ coursePackStore.currentCourseList.length }}</div>
        </div>
      </UCard>
      
      <UCard v-if="coursePackStore.currentCourse">
        <template #header>
          <h3 class="text-lg font-semibold">当前课程</h3>
        </template>
        <div class="space-y-2">
          <div class="font-medium">{{ coursePackStore.currentCourse.title }}</div>
          <div class="text-sm text-gray-600">{{ coursePackStore.currentCourse.description }}</div>
          <div>语句数量: {{ coursePackStore.currentCourse.statements?.length || 0 }}</div>
        </div>
      </UCard>
    </div>
    
    <!-- 课程包列表 -->
    <div v-if="coursePackStore.coursePackList.length > 0" class="mb-6">
      <h2 class="text-xl font-semibold mb-4">课程包列表</h2>
      <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
        <UCard 
          v-for="pack in coursePackStore.coursePackList" 
          :key="pack.id"
          :class="{ 'ring-2 ring-primary-500': pack.id === coursePackStore.selectedCoursePackId }"
        >
          <template #header>
            <div class="flex items-center justify-between">
              <h3 class="font-semibold truncate">{{ pack.title }}</h3>
              <UBadge v-if="pack.isFree" color="green" variant="soft">免费</UBadge>
            </div>
          </template>
          
          <div class="space-y-2">
            <p class="text-sm text-gray-600 line-clamp-2">{{ pack.description }}</p>
            <div class="text-xs text-gray-500">
              创建时间: {{ new Date(pack.createdAt).toLocaleDateString() }}
            </div>
          </div>
          
          <template #footer>
            <UButton 
              @click="coursePackStore.selectCoursePack(pack.id)"
              :loading="coursePackStore.currentCoursePackLoading && coursePackStore.selectedCoursePackId === pack.id"
              size="sm"
              block
            >
              选择课程包
            </UButton>
          </template>
        </UCard>
      </div>
    </div>
    
    <!-- 课程列表 -->
    <div v-if="coursePackStore.currentCourseList.length > 0" class="mb-6">
      <div class="flex items-center justify-between mb-4">
        <h2 class="text-xl font-semibold">课程列表</h2>
        <div v-if="coursePackStore.currentCourse" class="flex items-center gap-2">
          <UButton 
            @click="coursePackStore.goToPrevCourse()"
            :disabled="!coursePackStore.hasPrevCourse"
            size="sm"
            icon="i-heroicons-chevron-left"
          >
            上一课程
          </UButton>
          <span class="text-sm">
            {{ coursePackStore.currentCourseIndex + 1 }} / {{ coursePackStore.currentCourseList.length }}
          </span>
          <UButton 
            @click="coursePackStore.goToNextCourse()"
            :disabled="!coursePackStore.hasNextCourse"
            size="sm"
            icon="i-heroicons-chevron-right"
          >
            下一课程
          </UButton>
        </div>
      </div>
      
      <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
        <UCard 
          v-for="course in coursePackStore.currentCourseList" 
          :key="course.id"
          :class="{ 'ring-2 ring-primary-500': course.id === coursePackStore.selectedCourseId }"
        >
          <template #header>
            <h3 class="font-semibold truncate">{{ course.title }}</h3>
          </template>
          
          <div class="space-y-2">
            <p class="text-sm text-gray-600 line-clamp-2">{{ course.description }}</p>
            <div class="text-xs text-gray-500">
              类型: {{ course.type }} | 顺序: {{ course.order }}
            </div>
          </div>
          
          <template #footer>
            <UButton 
              @click="coursePackStore.selectCourse(course.id)"
              :loading="coursePackStore.currentCourseLoading && coursePackStore.selectedCourseId === course.id"
              size="sm"
              block
            >
              选择课程
            </UButton>
          </template>
        </UCard>
      </div>
    </div>
    
    <!-- 课程详情 -->
    <div v-if="coursePackStore.currentCourse">
      <h2 class="text-xl font-semibold mb-4">课程详情</h2>
      <UCard>
        <div class="space-y-4">
          <div>
            <h3 class="font-semibold">{{ coursePackStore.currentCourse.title }}</h3>
            <p class="text-gray-600">{{ coursePackStore.currentCourse.description }}</p>
          </div>
          
          <div class="grid grid-cols-2 md:grid-cols-4 gap-4 text-sm">
            <div>
              <span class="text-gray-500">类型:</span>
              <span class="ml-1">{{ coursePackStore.currentCourse.type }}</span>
            </div>
            <div>
              <span class="text-gray-500">顺序:</span>
              <span class="ml-1">{{ coursePackStore.currentCourse.order }}</span>
            </div>
            <div>
              <span class="text-gray-500">语句数:</span>
              <span class="ml-1">{{ coursePackStore.currentCourse.statements?.length || 0 }}</span>
            </div>
            <div>
              <span class="text-gray-500">媒体:</span>
              <span class="ml-1">{{ coursePackStore.currentCourse.mediaUrl ? '有' : '无' }}</span>
            </div>
          </div>
          
          <div v-if="coursePackStore.currentCourse.mediaUrl">
            <span class="text-gray-500">媒体URL:</span>
            <ULink 
              :to="coursePackStore.currentCourse.mediaUrl" 
              target="_blank"
              class="ml-1 text-sm break-all"
            >
              {{ coursePackStore.currentCourse.mediaUrl }}
            </ULink>
          </div>
        </div>
      </UCard>
    </div>
  </div>
</template>

<script setup>
import { useCoursePackStore } from '~/stores/coursePackStore'

const coursePackStore = useCoursePackStore()

function getStatusColor(status) {
  switch (status) {
    case 'idle': return 'gray'
    case 'loading': return 'yellow'
    case 'loaded': return 'green'
    case 'error': return 'red'
    default: return 'gray'
  }
}

// 页面加载时自动获取课程包列表
onMounted(() => {
  if (coursePackStore.coursePackList.length === 0) {
    coursePackStore.fetchCoursePackList()
  }
})
</script>

<script setup lang="ts">
import type { AdminCoursePack } from '~/types/course'
import { isArray, isEmpty } from 'lodash-es'
import { onMounted, ref } from 'vue'

const { $trpc } = useNuxtApp()
const loading = ref(false)
const errorMessage = ref('')

const adminCoursePacks = ref<AdminCoursePack[]>([])

onMounted(async () => {
  loading.value = true
  errorMessage.value = ''
  try {
    const packs = await $trpc.adminCoursePack.listMyCoursePacks.query()
    if (!isEmpty(packs) && isArray(packs)) {
      adminCoursePacks.value = packs
    }
  }
  catch (err: any) {
    errorMessage.value = err?.message || '获取课程包列表失败'
  }
  finally {
    loading.value = false
  }
})

function handleSelect(packId: string) {
  navigateTo({ path: '/course-list', query: { coursePackId: packId } })
}
</script>

<template>
  <NuxtLayout>
    <div class="mx-auto max-w-4xl p-6">
      <h1 class="mb-8 text-4xl font-bold text-gray-900">
        我的课程包
      </h1>
      <div v-if="loading" class="text-lg text-gray-700">
        加载中...
      </div>
      <div v-else-if="errorMessage" class="text-lg font-medium text-red-600">
        {{ errorMessage }}
      </div>
      <div v-else>
        <div v-if="adminCoursePacks.length === 0" class="text-lg text-gray-600">
          暂无课程包
        </div>
        <div v-else class="space-y-4">
          <div
            v-for="pack in adminCoursePacks"
            :key="pack.id"
            class="cursor-pointer rounded-lg border border-gray-200 p-6 transition-all duration-200 hover:border-blue-400 hover:bg-gray-50 hover:shadow-md"
            @click="handleSelect(pack.id)"
          >
            <div class="flex items-center justify-between">
              <div>
                <h2 class="text-2xl font-bold text-gray-300">
                  {{ pack.title }}
                </h2>
                <p class="mt-2 text-base text-gray-400">
                  {{ pack.description || '无描述' }}
                </p>
                <div class="mt-3 flex items-center space-x-6 text-sm text-gray-600">
                  <span>创建时间: {{ pack.createdAt }}</span>
                  <span>分享级别: {{ pack.shareLevel }}</span>
                  <span>是否免费: {{ pack.isFree ? '免费' : '付费' }}</span>
                </div>
              </div>
              <div class="text-base font-medium text-blue-600 hover:text-blue-700">
                进入详情 →
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  </NuxtLayout>
</template>

<style scoped>
</style>

<script setup lang="ts">
import { Pane, Splitpanes } from 'splitpanes'
import AudioPlayer from '~/components/AudioPlayer.vue'
import RightPane from '~/components/RightPane.vue'
import SubtitleEditor from '~/components/SubtitleEditor.vue'
import TopBar from '~/components/TopBar.vue'
import { useCourseDetail } from '~/composables/useCourseDetail'
import 'splitpanes/dist/splitpanes.css'

useCourseDetail()
</script>

<template>
  <NuxtLayout>
    <div class="mx-auto flex h-screen flex-col">
      <TopBar />
      <div class="flex flex-1 flex-col gap-4 overflow-hidden">
        <Splitpanes class="splitpanes-main-horizontal flex-1 overflow-hidden" style="height: 100%;">
          <Pane size="50" class="h-full overflow-auto border border-r-0 border-t-0 border-solid border-gray-300">
            <SubtitleEditor />
          </Pane>
          <!-- 右侧区域：LRC 显示组件 -->
          <Pane size="50" class="relative h-full overflow-auto border border-l-0 border-t-0 border-solid border-gray-300">
            <RightPane />
          </Pane>
        </Splitpanes>
        <!-- 底部区域：音频波形 -->
        <div class="w-full shrink-0 border border-solid border-gray-300">
          <AudioPlayer />
        </div>
      </div>
    </div>
  </NuxtLayout>
</template>

<style scoped>
.splitpanes-main-horizontal :deep(.splitpanes__splitter) {
  background: rgba(180, 180, 200, 0.18);
  width: 7px;
  min-width: 7px;
  max-width: 7px;
  height: 100%;
  border-radius: 4px;
  position: relative;
  cursor: col-resize;
  transition:
    background 0.3s,
    box-shadow 0.3s;
  box-shadow: none;
  border: none;
}
.splitpanes-main-horizontal :deep(.splitpanes__splitter:hover) {
  background: rgba(120, 180, 255, 0.28);
  box-shadow: 0 0 8px 2px rgba(120, 180, 255, 0.18);
}
.splitpanes-main-horizontal :deep(.splitpanes__splitter::before) {
  content: '';
  position: absolute;
  top: 0;
  bottom: 0;
  left: -8px;
  right: -8px;
  background: transparent;
  z-index: 1;
  border-radius: 6px;
  transition: background 0.3s;
}
.splitpanes-main-horizontal :deep(.splitpanes__splitter:hover::before) {
  background: rgba(120, 180, 255, 0.1);
}
.splitpanes-main-horizontal :deep(.splitpanes--vertical > .splitpanes__splitter) {
  width: 7px;
  min-width: 7px;
  max-width: 7px;
  height: 100%;
}
</style>

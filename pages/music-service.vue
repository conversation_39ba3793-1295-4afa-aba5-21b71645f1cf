<script setup lang="ts">
import type { NeteaseSearchListSchemaType } from '~/server/trpc/schemas/api/song'

// 页面元数据
definePageMeta({
  title: '完整tRPC音乐服务',
})

const neteaseQuery = ref('曲婉婷 Jar Of Love')
const selectedSongId = ref<string>('')

// tRPC客户端
const { $trpc } = useNuxtApp()

// 响应式状态变量
const searchData = ref<NeteaseSearchListSchemaType | null>(null)
const searchStatus = ref<'idle' | 'pending' | 'success' | 'error'>('idle')
const searchError = ref<Error | null>(null)

async function searchNeteaseSongs() {
  if (!neteaseQuery.value.trim()) return

  // 确保只在客户端调用
  if (!import.meta.client) return

  searchStatus.value = 'pending'
  searchError.value = null
  searchData.value = null

  try {
    const result = await $trpc.netease.search.query({
      q: neteaseQuery.value,
      page: 1,
      limit: 10,
    })

    searchData.value = result
    searchStatus.value = 'success'
  } catch (error) {
    console.error('搜索歌曲失败:', error)
    searchError.value = error instanceof Error ? error : new Error(String(error))
    searchStatus.value = 'error'
  }
}

function getLyrics(songId: string) {
  if (import.meta.server) return
  // 触发响应式歌词获取
  selectedSongId.value = songId
}
</script>
<template>
  <div class="p-8 max-w-6xl mx-auto">
    <!-- 网易云音乐双语歌词演示 -->
    <div class="mb-8">
      <h2 class="text-2xl font-semibold mb-4">🎵 网易云音乐双语歌词</h2>
      <div class="grid md:grid-cols-2 gap-6">
        <!-- 歌曲搜索 -->
        <div class="border rounded-lg p-4">
          <h3 class="text-lg font-medium mb-3">搜索歌曲</h3>
          <div class="space-y-3">
            <input
              v-model="neteaseQuery"
              type="text"
              placeholder="搜索歌曲名或歌手..."
              class="w-full px-3 py-2 border rounded text-blue-500 focus:outline-none focus:ring-2 focus:ring-blue-500"
            />
            <button
              :disabled="!neteaseQuery.trim() || searchStatus === 'pending'"
              class="w-full px-4 py-2 bg-blue-500 text-white rounded hover:bg-blue-600 disabled:opacity-50"
              @click="searchNeteaseSongs"
            >
              {{ searchStatus === 'pending' ? '搜索中...' : '搜索歌曲' }}
            </button>
          </div>

          <!-- 搜索结果 -->
          <div v-if="searchData" class="mt-4">
            <div class="text-sm text-gray-600 mb-2">找到 {{ searchData?.total || 0 }} 首歌曲</div>
            <div class="space-y-2 max-h-60 overflow-y-auto">
              <div
                v-for="song in searchData?.list?.slice(0, 5)"
                :key="song.songId"
                class="p-2 bg-blue-50 rounded text-sm cursor-pointer hover:bg-blue-100"
                @click="getLyrics(song.songId)"
              >
                <div class="font-medium text-gray-800">{{ song.name }}</div>
                <div class="text-gray-600">{{ song.singer }}</div>
              </div>
            </div>
          </div>

          <!-- 错误提示 -->
          <div v-if="searchError" class="mt-4 p-3 bg-red-50 border border-red-200 rounded text-red-700 text-sm">
            搜索失败：{{ searchError.message }}
          </div>
        </div>

        <!-- 双语歌词展示 -->
        <!-- <div class="border rounded-lg p-4">
          <h3 class="text-lg font-medium mb-3">双语歌词</h3>

          <div v-if="lyricsStatus === 'pending'" class="text-center py-4 text-gray-500">
            获取歌词中...
          </div>

          <div v-else-if="lyricsData" class="space-y-2">
            <div class="max-h-80 overflow-y-auto space-y-1">
              <div class="p-2 bg-gray-50 rounded text-sm">
                <div class="font-medium text-gray-800 whitespace-pre-line">{{ lyricsData.lyric }}</div>
              </div>
            </div>
          </div>

          <div v-else-if="lyricsError" class="text-center py-4 text-red-500">
            {{ lyricsError?.message || '获取歌词失败' }}
          </div>

          <div v-else class="text-center py-4 text-gray-500">
            点击上方歌曲获取歌词
          </div>
        </div> -->
      </div>
    </div>
  </div>
</template>

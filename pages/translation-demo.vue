<script setup lang="ts">
import { DEEP_SEEK_MODEL_ID } from '~/common/constants'

// 页面元数据
definePageMeta({
  title: '翻译主题分析系统演示',
})

// 响应式数据
const currentUser = ref('user001')
const inputContent = ref('')
const isGenerating = ref(false)
const isGeneratingQuick = ref(false)
const showEditModal = ref(false)
const themeData = ref<ThemeData | null>(null)
const editingThemeData = ref<ThemeData>({ theme: '', terms: [] })
const translationPrompt = ref('')
const logs = ref<Array<{ timestamp: string; message: string; type: string }>>([])

// 会话和状态管理
const currentSession = ref<UserSession | null>(null)
const activeSessions = ref(new Map<string, UserSession>())
const activeLocks = ref(new Map<string, number>())
const config = useRuntimeConfig()

// 配置数据
const apiConfig = ref<ApiConfig>({
  endpoint: 'https://api.openai.com/v1/chat/completions',
  apiKey: config.arkApiKey,
  model: DEEP_SEEK_MODEL_ID,
  timeout: 30000,
  userQuota: 1000,
  maxConcurrentRequests: 3,
})

const translationConfig = ref<TranslationConfig>({
  sourceLanguage: '英文',
  targetLanguage: '中文',
  maxLength: 1000,
})

const workflowOptions = ref<WorkflowOptions>({
  pauseForEdit: false,
  outputPath: 'terminology.json',
  autoSave: true,
  userId: 'user001',
  sessionId: undefined,
})

// 选项数据
const userOptions = [
  { label: '用户001', value: 'user001' },
  { label: '用户002', value: 'user002' },
  { label: '用户003', value: 'user003' },
]

const modelOptions = [{ label: 'deepseek-v3', value: DEEP_SEEK_MODEL_ID }]

// 环境检测
const isNode = typeof window === 'undefined' && typeof process !== 'undefined'

// 示例内容
const sampleContent = `
Artificial Intelligence (AI) has become one of the most transformative technologies of our time. Machine learning algorithms enable computers to learn patterns from data without explicit programming. Deep learning, a subset of machine learning, uses neural networks with multiple layers to process complex information.

Key concepts in AI include:
- Natural Language Processing (NLP): Enabling computers to understand human language
- Computer Vision: Teaching machines to interpret visual information
- Reinforcement Learning: Training agents through rewards and penalties
- Transfer Learning: Applying knowledge from one domain to another

The applications of AI span across various industries, from healthcare diagnostics to autonomous vehicles, financial trading systems, and personalized recommendation engines. As AI continues to evolve, it promises to revolutionize how we work, live, and interact with technology.
`

// 工具函数
const addLog = (message: string, type: 'info' | 'success' | 'error' | 'warning' = 'info') => {
  logs.value.push({
    timestamp: new Date().toLocaleTimeString(),
    message,
    type,
  })
  // 保持日志数量在合理范围内
  if (logs.value.length > 100) {
    logs.value = logs.value.slice(-50)
  }
}

const clearLogs = () => {
  logs.value = []
  addLog('日志已清空', 'info')
}

// 会话管理
const generateSessionId = (): string => {
  return `session_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`
}

const createUserWorkspace = (userId: string): string => {
  const timestamp = Date.now()
  return `workspace/${userId}/${timestamp}`
}

const createNewSession = () => {
  const sessionId = generateSessionId()
  const workspacePath = createUserWorkspace(currentUser.value)

  const session: UserSession = {
    userId: currentUser.value,
    sessionId,
    workspacePath,
    createdAt: Date.now(),
    lastActivity: Date.now(),
    isActive: true,
  }

  activeSessions.value.set(sessionId, session)
  currentSession.value = session
  workflowOptions.value.userId = currentUser.value
  workflowOptions.value.sessionId = sessionId

  addLog(`为用户 ${currentUser.value} 创建新会话: ${sessionId}`, 'success')
}

const switchUser = () => {
  workflowOptions.value.userId = currentUser.value
  // 为新用户创建会话
  createNewSession()
  addLog(`切换到用户: ${currentUser.value}`, 'info')
}

// 模拟 API 调用
const mockApiCall = async (_content: string): Promise<ThemeData> => {
  // 模拟网络延迟
  await new Promise((resolve) => setTimeout(resolve, 2000))

  // 模拟主题分析结果
  return {
    theme: `这是关于${translationConfig.value.sourceLanguage}到${translationConfig.value.targetLanguage}翻译的人工智能技术内容。文本介绍了机器学习、深度学习、自然语言处理等核心概念及其在各行业的应用。`,
    terms: [
      {
        src: 'Artificial Intelligence',
        tgt: '人工智能',
        note: '使机器能够执行通常需要人类智能的任务的技术',
      },
      {
        src: 'Machine Learning',
        tgt: '机器学习',
        note: '使计算机能够在没有明确编程的情况下从数据中学习的方法',
      },
      {
        src: 'Deep Learning',
        tgt: '深度学习',
        note: '使用多层神经网络处理复杂信息的机器学习子集',
      },
      {
        src: 'Natural Language Processing',
        tgt: '自然语言处理',
        note: '使计算机能够理解和处理人类语言的技术',
      },
      {
        src: 'Computer Vision',
        tgt: '计算机视觉',
        note: '教机器解释和理解视觉信息的技术',
      },
    ],
  }
}

// 文件操作模拟
const simulateFileOperations = {
  saveThemeData: (data: ThemeData, filePath: string) => {
    // 在浏览器环境中模拟文件保存
    const jsonString = JSON.stringify(data, null, 2)
    localStorage.setItem(`theme_${currentUser.value}_${filePath}`, jsonString)
    addLog(`主题数据已保存到: ${filePath}`, 'success')
  },

  loadThemeData: (filePath: string): ThemeData | null => {
    const stored = localStorage.getItem(`theme_${currentUser.value}_${filePath}`)
    if (stored) {
      addLog(`从 ${filePath} 加载主题数据`, 'success')
      return JSON.parse(stored)
    }
    addLog(`文件不存在: ${filePath}`, 'warning')
    return null
  },
}

// 文件锁模拟
const fileLockDemo = {
  acquireLock: async (filePath: string): Promise<boolean> => {
    const lockKey = `${filePath}.lock`
    const now = Date.now()

    if (!activeLocks.value.has(lockKey)) {
      activeLocks.value.set(lockKey, now)
      addLog(`获取文件锁: ${filePath}`, 'success')
      return true
    }

    addLog(`文件已被锁定: ${filePath}`, 'warning')
    return false
  },

  releaseLock: (filePath: string) => {
    const lockKey = `${filePath}.lock`
    activeLocks.value.delete(lockKey)
    addLog(`释放文件锁: ${filePath}`, 'info')
  },
}

// 主要功能函数
const generateThemeWithEdit = async () => {
  if (!inputContent.value.trim()) {
    addLog('请输入内容', 'error')
    return
  }

  if (!apiConfig.value.apiKey) {
    addLog('请配置API密钥', 'error')
    return
  }

  isGenerating.value = true

  try {
    addLog(`用户 ${currentUser.value} 开始完整工作流分析...`, 'info')

    // 生成唯一文件路径
    const baseOutputPath = workflowOptions.value.outputPath || 'terminology.json'
    const timestamp = Date.now()
    const outputPath = `${currentUser.value}_${currentSession.value?.sessionId}_${timestamp}_${baseOutputPath}`

    // 获取文件锁
    const lockAcquired = await fileLockDemo.acquireLock(outputPath)
    if (!lockAcquired) {
      addLog('无法获取文件锁，请稍后重试', 'error')
      return
    }

    try {
      // 调用API生成主题数据
      const generatedData = await mockApiCall(inputContent.value)
      themeData.value = generatedData

      // 保存数据
      simulateFileOperations.saveThemeData(generatedData, outputPath)

      // 如果启用了编辑暂停
      if (workflowOptions.value.pauseForEdit) {
        editingThemeData.value = JSON.parse(JSON.stringify(generatedData))
        showEditModal.value = true
        addLog('暂停以供用户编辑术语表', 'info')
      } else {
        // 直接生成翻译prompt
        generateTranslationPrompt(generatedData)
      }
    } finally {
      fileLockDemo.releaseLock(outputPath)
    }
  } catch (error) {
    const errorMessage = error instanceof Error ? error.message : '未知错误'
    addLog(`生成失败: ${errorMessage}`, 'error')
  } finally {
    isGenerating.value = false
  }
}

const generateQuickTheme = async () => {
  if (!inputContent.value.trim()) {
    addLog('请输入内容', 'error')
    return
  }

  isGeneratingQuick.value = true

  try {
    addLog('快速生成主题中...', 'info')
    const generatedData = await mockApiCall(inputContent.value)
    themeData.value = generatedData
    generateTranslationPrompt(generatedData)
    addLog('快速主题生成完成', 'success')
  } catch (error) {
    const errorMessage = error instanceof Error ? error.message : '未知错误'
    addLog(`快速生成失败: ${errorMessage}`, 'error')
  } finally {
    isGeneratingQuick.value = false
  }
}

const generateTranslationPrompt = (data: ThemeData) => {
  const { sourceLanguage, targetLanguage } = translationConfig.value

  translationPrompt.value = `你是专业的${sourceLanguage}到${targetLanguage}翻译专家。

## 内容主题
${data.theme}

## 翻译原则
1. 忠实原文：准确传达原文内容和含义
2. 术语准确：正确使用专业术语并保持一致性
3. 风格适配：根据主题调整语言风格
   - 教程内容：使用轻松口语化语言
   - 技术内容：使用专业术语
   - 纪录片：使用正式语言

## 专业术语表
${data.terms?.map((term) => `- ${term.src} → ${term.tgt} (${term.note})`).join('\n')}

## 待翻译内容
${inputContent.value}

请提供自然流畅的${targetLanguage}翻译：`

  addLog('翻译Prompt生成完成', 'success')
}

// 编辑相关函数
const addNewTerm = () => {
  editingThemeData.value.terms.push({
    src: '',
    tgt: '',
    note: '',
  })
}

const removeTerm = (index: number) => {
  editingThemeData.value.terms.splice(index, 1)
}

const saveEdit = () => {
  if (themeData.value) {
    themeData.value = JSON.parse(JSON.stringify(editingThemeData.value))
    showEditModal.value = false
    if (themeData.value) {
      generateTranslationPrompt(themeData.value)
    }
    addLog('用户编辑完成，继续工作流', 'success')
  }
}

const cancelEdit = () => {
  showEditModal.value = false
  addLog('用户取消编辑', 'warning')
}

// 实用功能
const loadSampleContent = () => {
  inputContent.value = sampleContent
  addLog('加载示例内容', 'info')
}

const clearContent = () => {
  inputContent.value = ''
  addLog('清空内容', 'info')
}

const copyPrompt = async () => {
  if (translationPrompt.value) {
    try {
      await navigator.clipboard.writeText(translationPrompt.value)
      addLog('Prompt已复制到剪贴板', 'success')
    } catch {
      addLog('复制失败', 'error')
    }
  }
}

const showFileLockDemo = () => {
  addLog('=== 文件锁演示 ===', 'info')
  const testFile = 'demo-file.json'

  // 演示锁的获取和释放
  setTimeout(async () => {
    const acquired = await fileLockDemo.acquireLock(testFile)
    if (acquired) {
      setTimeout(() => {
        fileLockDemo.releaseLock(testFile)
      }, 3000)
    }
  }, 1000)
}

const showSessionManagement = () => {
  addLog('=== 会话管理演示 ===', 'info')
  addLog(`当前活跃会话数: ${activeSessions.value.size}`, 'info')

  for (const [sessionId, session] of activeSessions.value) {
    addLog(`会话 ${sessionId}: 用户${session.userId}, 创建于${new Date(session.createdAt).toLocaleTimeString()}`, 'info')
  }
}

// 生命周期
onMounted(() => {
  // 初始化用户会话
  createNewSession()
  addLog('翻译主题分析系统演示已启动', 'success')
  addLog(`运行环境: ${isNode ? 'Node.js' : '浏览器'}`, 'info')
})

// 监听用户变化
watch(currentUser, () => {
  workflowOptions.value.userId = currentUser.value
})
</script>

<template>
  <div class="min-h-screen bg-gradient-to-br from-blue-900 via-purple-900 to-indigo-900 p-6">
    <div class="max-w-7xl mx-auto">
      <!-- 页面标题 -->
      <div class="text-center mb-8">
        <h1 class="text-4xl font-bold text-white mb-2">
          翻译主题分析系统演示
        </h1>
        <p class="text-gray-300">
          完整展示主题生成、术语提取、多用户会话管理等功能
        </p>
      </div>

      <!-- 用户会话选择 -->
      <div class="mb-6 bg-white/10 backdrop-blur-sm rounded-lg p-4">
        <h2 class="text-xl font-semibold text-white mb-4">用户会话管理</h2>
        <div class="grid grid-cols-1 md:grid-cols-3 gap-4">
          <UFormGroup label="当前用户" class="text-white">
            <USelect v-model="currentUser" :options="userOptions" @change="switchUser" />
          </UFormGroup>
          <UFormGroup label="会话ID" class="text-white">
            <UInput :model-value="currentSession?.sessionId || ''" disabled placeholder="自动生成" />
          </UFormGroup>
          <div class="flex items-end">
            <UButton color="emerald" variant="solid" @click="createNewSession">
              创建新会话
            </UButton>
          </div>
        </div>
      </div>

      <!-- 主要功能区域 -->
      <div class="grid grid-cols-1 lg:grid-cols-2 gap-6">
        <!-- 左侧：内容输入和配置 -->
        <div class="space-y-6">
          <!-- API配置 -->
          <div class="bg-white/10 backdrop-blur-sm rounded-lg p-6">
            <h3 class="text-lg font-semibold text-white mb-4">API配置</h3>
            <div class="space-y-4">
              <UFormGroup label="API端点" class="text-white">
                <UInput v-model="apiConfig.endpoint" placeholder="https://api.openai.com/v1/chat/completions" />
              </UFormGroup>
              <UFormGroup label="API密钥" class="text-white">
                <UInput v-model="apiConfig.apiKey" type="password" placeholder="your-api-key" />
              </UFormGroup>
              <UFormGroup label="模型" class="text-white">
                <USelect v-model="apiConfig.model" :options="modelOptions" />
              </UFormGroup>
            </div>
          </div>

          <!-- 内容输入 -->
          <div class="bg-white/10 backdrop-blur-sm rounded-lg p-6">
            <h3 class="text-lg font-semibold text-white mb-4">待分析内容</h3>
            <UTextarea v-model="inputContent" :rows="8" placeholder="请输入需要分析和翻译的内容..." class="w-full" />
            <div class="mt-4 flex gap-2">
              <UButton color="gray" variant="outline" @click="loadSampleContent">
                加载示例内容
              </UButton>
              <UButton color="red" variant="outline" @click="clearContent">
                清空内容
              </UButton>
            </div>
          </div>

          <!-- 工作流选项 -->
          <div class="bg-white/10 backdrop-blur-sm rounded-lg p-6">
            <h3 class="text-lg font-semibold text-white mb-4">工作流选项</h3>
            <div class="space-y-4">
              <UCheckbox v-model="workflowOptions.pauseForEdit" label="暂停以编辑术语表" class="text-white" />
              <UCheckbox v-model="workflowOptions.autoSave" label="自动保存主题数据" class="text-white" />
              <UFormGroup label="输出文件名" class="text-white">
                <UInput v-model="workflowOptions.outputPath" placeholder="terminology.json" />
              </UFormGroup>
            </div>
          </div>
        </div>

        <!-- 右侧：结果显示和操作 -->
        <div class="space-y-6">
          <!-- 操作按钮 -->
          <div class="bg-white/10 backdrop-blur-sm rounded-lg p-6">
            <h3 class="text-lg font-semibold text-white mb-4">操作面板</h3>
            <div class="grid grid-cols-2 gap-3">
              <UButton :loading="isGenerating" color="emerald" block @click="generateThemeWithEdit">
                完整工作流
              </UButton>
              <UButton :loading="isGeneratingQuick" color="blue" block @click="generateQuickTheme">
                快速生成主题
              </UButton>
              <UButton color="orange" block @click="showFileLockDemo">
                文件锁演示
              </UButton>
              <UButton color="purple" block @click="showSessionManagement">
                会话管理
              </UButton>
            </div>
          </div>

          <!-- 生成的主题数据 -->
          <div v-if="themeData" class="bg-white/10 backdrop-blur-sm rounded-lg p-6">
            <h3 class="text-lg font-semibold text-white mb-4">生成的主题数据</h3>
            <div class="space-y-4">
              <div>
                <h4 class="text-sm font-medium text-gray-300 mb-2">主题摘要</h4>
                <div class="bg-black/20 rounded-lg p-3 text-white">
                  {{ themeData.theme }}
                </div>
              </div>
              <div v-if="themeData.terms?.length">
                <h4 class="text-sm font-medium text-gray-300 mb-2">专业术语</h4>
                <div class="space-y-2">
                  <div v-for="(term, index) in themeData.terms" :key="index" class="bg-black/20 rounded-lg p-3 text-sm">
                    <div class="flex justify-between items-start">
                      <div class="text-blue-300">{{ term.src }}</div>
                      <div class="text-green-300">{{ term.tgt }}</div>
                    </div>
                    <div class="text-gray-400 text-xs mt-1">{{ term.note }}</div>
                  </div>
                </div>
              </div>
            </div>
          </div>

          <!-- 翻译Prompt -->
          <div v-if="translationPrompt" class="bg-white/10 backdrop-blur-sm rounded-lg p-6">
            <h3 class="text-lg font-semibold text-white mb-4">生成的翻译Prompt</h3>
            <div class="bg-black/20 rounded-lg p-4 text-sm text-gray-300 max-h-60 overflow-y-auto">
              <pre class="whitespace-pre-wrap">{{ translationPrompt }}</pre>
            </div>
            <UButton color="gray" variant="outline" class="mt-3" @click="copyPrompt">
              复制Prompt
            </UButton>
          </div>

          <!-- 系统状态 -->
          <div class="bg-white/10 backdrop-blur-sm rounded-lg p-6">
            <h3 class="text-lg font-semibold text-white mb-4">系统状态</h3>
            <div class="space-y-2 text-sm">
              <div class="flex justify-between">
                <span class="text-gray-300">活跃会话数:</span>
                <span class="text-white">{{ activeSessions.size }}</span>
              </div>
              <div class="flex justify-between">
                <span class="text-gray-300">文件锁数:</span>
                <span class="text-white">{{ activeLocks.size }}</span>
              </div>
              <div class="flex justify-between">
                <span class="text-gray-300">当前环境:</span>
                <span class="text-white">{{ isNode ? 'Node.js' : '浏览器' }}</span>
              </div>
              <div class="flex justify-between">
                <span class="text-gray-300">工作空间:</span>
                <span class="text-white text-xs">{{ currentSession?.workspacePath }}</span>
              </div>
            </div>
          </div>
        </div>
      </div>

      <!-- 日志显示 -->
      <div class="mt-8 bg-white/10 backdrop-blur-sm rounded-lg p-6">
        <div class="flex justify-between items-center mb-4">
          <h3 class="text-lg font-semibold text-white">系统日志</h3>
          <UButton color="red" variant="outline" size="sm" @click="clearLogs">
            清空日志
          </UButton>
        </div>
        <div class="bg-black/30 rounded-lg p-4 h-40 overflow-y-auto">
          <div
            v-for="(log, index) in logs"
            :key="index"
            :class="[
              'text-xs mb-1',
              {
                'text-green-400': log.type === 'success',
                'text-red-400': log.type === 'error',
                'text-yellow-400': log.type === 'warning',
                'text-gray-300': log.type === 'info',
              },
            ]"
          >
            [{{ log.timestamp }}] {{ log.message }}
          </div>
        </div>
      </div>
    </div>

    <!-- 模态框：文件编辑 -->
    <UModal v-model="showEditModal">
      <UCard>
        <template #header>
          <h3 class="text-lg font-semibold">编辑主题数据</h3>
        </template>

        <div class="space-y-4">
          <UFormGroup label="主题摘要">
            <UTextarea v-model="editingThemeData.theme" :rows="3" />
          </UFormGroup>

          <div>
            <div class="flex justify-between items-center mb-2">
              <label class="text-sm font-medium">专业术语</label>
              <UButton color="emerald" size="xs" @click="addNewTerm">
                添加术语
              </UButton>
            </div>

            <div v-for="(term, index) in editingThemeData.terms" :key="index" class="grid grid-cols-12 gap-2 mb-2">
              <UInput v-model="term.src" placeholder="源术语" class="col-span-4" />
              <UInput v-model="term.tgt" placeholder="目标翻译" class="col-span-4" />
              <UInput v-model="term.note" placeholder="说明" class="col-span-3" />
              <UButton color="red" variant="outline" size="xs" class="col-span-1" @click="removeTerm(index)">
                删除
              </UButton>
            </div>
          </div>
        </div>

        <template #footer>
          <div class="flex justify-end gap-2">
            <UButton color="gray" variant="outline" @click="cancelEdit">
              取消
            </UButton>
            <UButton color="emerald" @click="saveEdit">
              保存并继续
            </UButton>
          </div>
        </template>
      </UCard>
    </UModal>
  </div>
</template>

<style scoped>
/* 自定义样式 */
.bg-gradient-to-br {
  background-image: linear-gradient(to bottom right, var(--tw-gradient-stops));
}

pre {
  font-family: 'Consolas', 'Monaco', 'Courier New', monospace;
}

/* 滚动条样式 */
::-webkit-scrollbar {
  width: 6px;
}

::-webkit-scrollbar-track {
  background: rgba(0, 0, 0, 0.1);
}

::-webkit-scrollbar-thumb {
  background: rgba(255, 255, 255, 0.3);
  border-radius: 3px;
}

::-webkit-scrollbar-thumb:hover {
  background: rgba(255, 255, 255, 0.5);
}
</style>

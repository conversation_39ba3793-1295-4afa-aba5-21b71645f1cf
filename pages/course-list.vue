<script setup lang="ts">
import type { CourseInPackResponse, CoursePackWithCoursesResponse } from '~/types/course'
import { isEmpty } from 'lodash-es'
import { onMounted, ref } from 'vue'
import { useRoute } from 'vue-router'

const { $trpc } = useNuxtApp()
const route = useRoute()

const loading = ref(false)
const errorMessage = ref('')
const coursePack = ref<CoursePackWithCoursesResponse>()
const courses = ref<CourseInPackResponse[]>([])

const coursePackId = route.query.coursePackId as string

onMounted(async () => {
  if (!coursePackId) {
    errorMessage.value = '未提供课程包ID'
    return
  }
  loading.value = true
  errorMessage.value = ''
  try {
    const res = await $trpc.adminCoursePack.getCoursePackWithCourses.query({ coursePackId })
    if (!isEmpty(res)) {
      coursePack.value = res
      courses.value = res.courses
    }
  }
  catch (err: any) {
    errorMessage.value = err?.message || '获取课程包详情失败'
  }
  finally {
    loading.value = false
  }
})

// 点击课程
async function handleCourseClick(course: CourseInPackResponse) {
  // // 获取课程详情
  // const courseDetail = await $trpc.adminCourse.getCourseDetail.query({
  //   coursePackId: course.coursePackId,
  //   courseId: course.id,
  // })
  // 跳转到编辑页面
  navigateTo(`/?coursePackId=${course.coursePackId}&courseId=${course.id}`)
}
</script>

<template>
  <NuxtLayout>
    <div class="mx-auto max-w-3xl p-6">
      <div v-if="loading" class="text-gray-500">
        加载中...
      </div>
      <div v-else-if="errorMessage" class="text-red-500">
        {{ errorMessage }}
      </div>
      <div v-else-if="coursePack">
        <h1 class="mb-4 text-2xl font-bold text-gray-300">
          {{ coursePack.title }}
        </h1>
        <p class="mb-2 text-gray-400">
          {{ coursePack.description || '无描述' }}
        </p>
        <div class="mb-4 flex space-x-4 text-xs text-gray-500">
          <span>是否免费: {{ coursePack.type === 'free' ? '免费' : '付费' }}</span>
        </div>
        <h2 class="mb-2 mt-6 text-lg font-semibold text-gray-600">
          课程列表
        </h2>
        <div v-if="!courses.length" class="text-gray-400">
          暂无课程
        </div>
        <ul v-else class="space-y-2">
          <li v-for="course in courses" :key="course.id" class="rounded border bg-white p-3 shadow-sm" @click="handleCourseClick(course)">
            <div class="font-medium text-gray-900">
              {{ course.title }}
            </div>
            <div class="text-sm text-gray-500">
              {{ course.description || '无描述' }}
            </div>
            <div class="mt-1 text-xs text-gray-400">
              课程类型: {{ course.type === 'music' ? '音乐课程' : '标准课程' }}
            </div>
          </li>
        </ul>
      </div>
    </div>
  </NuxtLayout>
</template>

<style scoped>
</style>

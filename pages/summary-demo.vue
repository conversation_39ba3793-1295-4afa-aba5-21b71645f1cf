<script setup lang="ts">
import { useSummaryExtraction } from '~/composables/translation/useSummaryExtraction'
import type { CustomTerm, CustomTermsJson } from '~/common/prompts'

// 页面元数据
useHead({
  title: '文本摘要提取',
  meta: [{ name: 'description', content: '基于AI的智能文本内容分析和术语提取工具' }],
})

// 使用composable
const {
  isExtracting,
  extractionResult,
  errorMessage,
  extractSummaryFn,
  getPromptPreview,
  configureLanguages,
  reset,
} = useSummaryExtraction()

// 响应式数据
const content = ref(`

**The Enchanted Library**  

In a quiet town nestled between hills, there stood an old mansion with a dusty library. <PERSON>, a curious 12-year-old, stumbled upon it one rainy afternoon. The moment she pushed open the creaky oak door, the room shimmered—books floated in mid-air, their spines glowing in hues of amethyst and gold.  

"Ahem," coughed a small, spectacled owl perched on a ladder. "Welcome to the Library of Whispers. Books here hold *living stories*—but beware: only those with open hearts may borrow them."  

Lila's eyes widened as a leather-bound book titled *The Starless Sea* drifted toward her. As she touched it, silver ink spilled across the cover, rewriting its title to *<PERSON>'s Journey*. Inside, pages depicted her hometown… but shrouded in darkness. A voice whispered, *"Find the lost star to light your world."*  

Guided by the owl (who revealed himself as Mr. Whiskers, the guardian), Lila flipped through enchanted maps and met talking creatures: a fox with a compass made of moonlight, a river nymph who sang riddles, and a wise turtle who carried ancient constellations on his shell.  

At last, she reached the story's final page—a void where the star should be. "Look within," Mr. Whiskers hooted. Lila closed her eyes and thought of her courage, her kindness, her love for stories. When she opened them, a tiny star sparked in her palm, casting warmth over the ink. The book glowed, rewriting itself into a vibrant tale of hope.  

From then on, Lila visited the library often, discovering that every story was a mirror—reflecting not just adventures, but the magic within anyone brave enough to dream. And as the stars twinkled outside, the books whispered, *"The greatest stories are never finished… they're just waiting for someone to believe."*  
`)

const sourceLanguage = ref('English')
const targetLanguage = ref('Chinese')
const showPromptPreview = ref(false)

// 语言选项
const sourceLanguageOptions = [
  { label: '英语', value: 'English' },
  { label: '中文', value: 'Chinese' },
]

const targetLanguageOptions = [
  { label: '中文', value: 'Chinese' },
  { label: '英语', value: 'English' },
]

// 已存在术语
const existingTerms = ref<CustomTerm[]>([])

// 计算属性
const promptPreview = computed(() => {
  if (!content.value.trim()) return ''
  const customTerms: CustomTermsJson | undefined =
    existingTerms.value.length > 0 ? { terms: existingTerms.value.filter((t) => t.src && t.tgt) } : undefined
  return getPromptPreview(content.value, customTerms)
})

// 方法
const updateLanguageConfig = () => {
  configureLanguages(sourceLanguage.value, targetLanguage.value)
  // 显示成功消息
  const toast = useToast()
  toast.add({
    title: '语言配置已更新',
    description: `源语言: ${sourceLanguage.value}, 目标语言: ${targetLanguage.value}`,
    color: 'green',
  })
}

const addTerm = () => {
  existingTerms.value.push({ src: '', tgt: '', note: '' })
}

const removeTerm = (index: number) => {
  existingTerms.value.splice(index, 1)
}

const handleExtractSummary = async () => {
  const customTerms: CustomTermsJson | undefined =
    existingTerms.value.length > 0 ? { terms: existingTerms.value.filter((t) => t.src && t.tgt) } : undefined

  await extractSummaryFn(content.value, customTerms)
}

const exportAsJson = () => {
  if (!extractionResult.value) return

  const dataStr = JSON.stringify(extractionResult.value, null, 2)
  const dataBlob = new Blob([dataStr], { type: 'application/json' })
  const url = URL.createObjectURL(dataBlob)
  const link = document.createElement('a')
  link.href = url
  link.download = 'text-summary-terms.json'
  link.click()
  URL.revokeObjectURL(url)
}

const copyToClipboard = async () => {
  if (!extractionResult.value) return

  const text = `主题摘要：\n${extractionResult.value.theme}\n\n专业术语：\n${extractionResult.value.terms
    .map((t) => `- ${t.src}: ${t.tgt} (${t.note})`)
    .join('\n')}`

  try {
    await navigator.clipboard.writeText(text)
    const toast = useToast()
    toast.add({
      title: '复制成功',
      description: '结果已复制到剪贴板',
      color: 'green',
    })
  } catch (error) {
    console.error('复制失败:', error)
  }
}

// 组件挂载时重置状态
onMounted(() => {
  reset()
})
</script>

<template>
  <div class="container mx-auto p-6 max-w-4xl">
    <div class="space-y-6">
      <!-- 页面标题 -->
      <div class="text-center">
        <h1 class="text-3xl font-bold text-gray-900 dark:text-white">文本摘要和术语提取</h1>
        <p class="mt-2 text-gray-600 dark:text-gray-400">基于DeepSeek AI的智能文本内容分析工具</p>
      </div>

      <!-- 语言配置 -->
      <div class="bg-white dark:bg-gray-800 rounded-lg shadow-lg p-6 hidden">
        <h2 class="text-xl font-semibold text-gray-900 dark:text-white mb-4">语言配置</h2>
        <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
          <div>
            <label class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2"> 源语言 </label>
            <USelect v-model="sourceLanguage" :options="sourceLanguageOptions" />
          </div>
          <div>
            <label class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2"> 目标语言 </label>
            <USelect v-model="targetLanguage" :options="targetLanguageOptions" />
          </div>
        </div>
        <div class="mt-4">
          <UButton color="blue" variant="outline" @click="updateLanguageConfig"> 应用语言设置 </UButton>
        </div>
      </div>

      <!-- 文本内容输入 -->
      <div class="bg-white dark:bg-gray-800 rounded-lg shadow-lg p-6">
        <h2 class="text-xl font-semibold text-gray-900 dark:text-white mb-4">文本内容</h2>
        <UTextarea v-model="content" placeholder="请输入文本内容，支持字幕、转录文本或手动输入的文本..." :rows="8" />

        <!-- 已存在术语 -->
        <div class="mt-4">
          <h3 class="text-lg font-medium text-gray-800 dark:text-gray-200 mb-2">已存在术语（可选）</h3>
          <div class="space-y-2">
            <div v-for="(term, index) in existingTerms" :key="index" class="grid grid-cols-4 gap-2">
              <UInput v-model="term.src" placeholder="源术语" />
              <UInput v-model="term.tgt" placeholder="目标翻译" />
              <UInput v-model="term.note" placeholder="说明" />
              <UButton color="red" variant="outline" size="sm" @click="removeTerm(index)"> 删除 </UButton>
            </div>
          </div>
          <UButton color="green" variant="outline" size="sm" class="mt-2" @click="addTerm"> 添加术语 </UButton>
        </div>

        <!-- 提取按钮 -->
        <div class="mt-6 flex justify-center">
          <UButton :loading="isExtracting" :disabled="!content.trim()" size="lg" @click="handleExtractSummary">
            {{ isExtracting ? '分析中...' : '开始提取摘要' }}
          </UButton>
        </div>
      </div>

      <!-- 错误提示 -->
      <div v-if="errorMessage" class="bg-red-50 dark:bg-red-900/20 border border-red-200 dark:border-red-800 rounded-lg p-4">
        <p class="text-red-700 dark:text-red-400">{{ errorMessage }}</p>
      </div>

      <!-- 提取结果 -->
      <div v-if="extractionResult" class="bg-white dark:bg-gray-800 rounded-lg shadow-lg p-6">
        <h2 class="text-xl font-semibold text-gray-900 dark:text-white mb-4">提取结果</h2>

        <!-- 主题摘要 -->
        <div class="mb-6">
          <h3 class="text-lg font-medium text-gray-800 dark:text-gray-200 mb-2">📋 主题摘要</h3>
          <div class="bg-blue-50 dark:bg-blue-900/20 border border-blue-200 dark:border-blue-800 rounded-lg p-4">
            <p class="text-gray-900 dark:text-white">{{ extractionResult.theme }}</p>
          </div>
        </div>

        <!-- 专业术语 -->
        <div>
          <h3 class="text-lg font-medium text-gray-800 dark:text-gray-200 mb-2">🔤 专业术语 ({{ extractionResult.terms.length }} 个)</h3>
          <div class="space-y-3">
            <div
              v-for="(term, index) in extractionResult.terms"
              :key="index"
              class="bg-gray-50 dark:bg-gray-700 border border-gray-200 dark:border-gray-600 rounded-lg p-4"
            >
              <div class="grid grid-cols-1 md:grid-cols-3 gap-2">
                <div>
                  <span class="text-sm font-medium text-gray-500 dark:text-gray-400">源术语:</span>
                  <p class="text-gray-900 dark:text-white font-mono">{{ term.src }}</p>
                </div>
                <div>
                  <span class="text-sm font-medium text-gray-500 dark:text-gray-400">翻译:</span>
                  <p class="text-gray-900 dark:text-white font-mono">{{ term.tgt }}</p>
                </div>
                <div>
                  <span class="text-sm font-medium text-gray-500 dark:text-gray-400">说明:</span>
                  <p class="text-gray-700 dark:text-gray-300 text-sm">{{ term.note }}</p>
                </div>
              </div>
            </div>
          </div>
        </div>

        <!-- 导出功能 -->
        <div class="mt-6 flex gap-2">
          <UButton color="green" variant="outline" @click="exportAsJson"> 导出为JSON </UButton>
          <UButton color="blue" variant="outline" @click="copyToClipboard"> 复制结果 </UButton>
        </div>
      </div>

      <!-- 提示词预览（开发调试用） -->
      <div v-if="showPromptPreview" class="bg-gray-50 dark:bg-gray-900 rounded-lg shadow p-6">
        <h3 class="text-lg font-medium text-gray-800 dark:text-gray-200 mb-2">提示词预览（调试用）</h3>
        <pre class="text-xs text-gray-600 dark:text-gray-400 whitespace-pre-wrap">{{ promptPreview }}</pre>
        <UButton size="sm" variant="outline" class="mt-2" @click="showPromptPreview = false"> 隐藏 </UButton>
      </div>

      <!-- 调试按钮 -->
      <div class="text-center">
        <UButton variant="ghost" size="sm" @click="showPromptPreview = true"> 显示提示词预览 </UButton>
      </div>
    </div>
  </div>
</template>

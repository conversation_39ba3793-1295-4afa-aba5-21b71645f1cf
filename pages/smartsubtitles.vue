<script setup lang="ts">
import { useSpeechRecognition } from '~/composables/api/useSpeechRecognition'
import { useFileUpload } from '~/composables/ui/useFileUpload'
import { useSubtitleGeneration } from '~/composables/subtitle/useSubtitleGeneration'
import { useSpeechRecognitionConfig } from '~/composables/audio/useSpeechRecognitionConfig'
import { useSmartChunking } from '~/composables/translation/useSmartChunking'
import { useLineByLineTranslation } from '~/composables/translation/useLineByLineTranslation'
import { useVideoLingoTranslation } from '~/composables/translation/useVideoLingoTranslation'
import { useSummaryExtraction } from '~/composables/translation/useSummaryExtraction'
import { useTimestampAlignment } from '~/composables/subtitle/useTimestampAlignment'

import type { SummaryResult } from '~/common/prompts'
import { formatTimeToString, millisecondsToSeconds } from '~/utils/processing/time/timeFormatter'

const {
  isProcessing,
  recognitionResult,
  jobId,
  errorMessage: speechErrorMessage,
  canStartRecognition,
  startRecognition,
  mergeRecognitionResult,
} = useSpeechRecognition()
const {
  uploadedFile,
  fileInputRef,
  errorMessage: fileErrorMessage,
  triggerFileInput,
  handleFileUpload,
  removeFile,
  formatFileSize,
} = useFileUpload()
const { downloadSubtitle } = useSubtitleGeneration()
const {
  apiConfig,
  selectedLanguage,
  selectedContentType,
  features,
  maxCharsPerLine,
  maxLines,
  contentTypeOptions,
  getRecommendedCharsPerLine,
  getLanguageLabel,
  getContentTypeLabel,
} = useSpeechRecognitionConfig()
const {
  chunks,
  isChunking,
  chunkingError,
  contexts,
  statistics,
  config,
  isTranslating,
  translationError,
  translationResults,
  performSmartChunking,
  performConcurrentTranslation,
  resetChunking,
  exportChunksAsJson,
} = useSmartChunking()

// 逐行翻译功能
const {
  isTranslating: isLineTranslating,
  translationError: lineTranslationError,
  faithfulResults,
  expressiveResults,
  finalResults,
  config: _lineTranslationConfig,
  getTranslationStatistics,
  translateRecognitionResult,
  resetTranslation: resetLineTranslation,
  updateConfig: updateLineTranslationConfig,
} = useLineByLineTranslation()

// VideoLingo翻译功能
const {
  isTranslating: isVideoLingoTranslating,
  translationError: videoLingoTranslationError,
  chunks: _videoLingoChunks,
  translationResults: videoLingoResults,
  faithfulResults: _videoLingoFaithfulResults,
  expressiveResults: _videoLingoExpressiveResults,
  config: _videoLingoConfig,
  getTranslationStatistics: getVideoLingoStatistics,
  translateAll: translateAllVideoLingo,
  resetTranslation: resetVideoLingoTranslation,
  updateConfig: updateVideoLingoConfig,
} = useVideoLingoTranslation()

// 摘要提取功能
const {
  isExtracting,
  extractionResult,
  errorMessage: summaryErrorMessage,
  extractSummaryFn,
  reset: resetSummary,
  setCachedResult,
} = useSummaryExtraction()

// 时间戳对齐功能
const {
  isProcessing: _isAligning,
  hasResults: hasAlignmentResults,
  showPreview: _showAlignmentPreview,
  error: _alignmentError,
  warnings: _alignmentWarnings,
  sourceData: _sourceData,
  translationData: _translationData,
  alignmentResult: _alignmentResult,
  alignedTranslations: _alignedTranslations,
  previewData: _previewData,
  failedItems: _failedItems,
  successStatistics: _successStatistics,
  importSourceData,
  importTranslationData,
  performAlignment,
  showAlignmentPreview: openAlignmentPreview,
  hideAlignmentPreview: _hideAlignmentPreview,
  confirmAlignment,
  reset: _resetAlignment,
} = useTimestampAlignment()

// 句子分割配置
const sentenceSegmentationConfig = ref({
  maxSentenceLength: 150,
  maxPauseTime: 1.0,
  usePunctuation: true,
  useTimeGaps: true,
  useLengthLimit: true,
})

// 缓存相关功能
const generateCacheKey = () => {
  if (!uploadedFile.value || !recognitionResult.value) return null

  // 基于文件信息和识别结果生成缓存键
  const fileInfo = `${uploadedFile.value.name}_${uploadedFile.value.size}_${uploadedFile.value.lastModified}`
  const contentHash = recognitionResult.value.utterances
    .map((u) => u.text)
    .join('')
    .slice(0, 100) // 取前100个字符作为内容标识

  return `summary_${btoa(fileInfo + contentHash)
    .replace(/[^a-zA-Z0-9]/g, '')
    .slice(0, 50)}`
}

const loadSummaryFromCache = (cacheKey: string) => {
  try {
    const cached = localStorage.getItem(cacheKey)
    if (cached) {
      const parsedCache = JSON.parse(cached)
      // 检查缓存是否在7天内
      const cacheAge = Date.now() - parsedCache.timestamp
      const maxAge = 7 * 24 * 60 * 60 * 1000 // 7天

      if (cacheAge < maxAge) {
        return parsedCache.data
      } else {
        // 缓存过期，删除
        localStorage.removeItem(cacheKey)
      }
    }
  } catch (error) {
    console.error('读取摘要缓存失败:', error)
  }
  return null
}

const saveSummaryToCache = (cacheKey: string, data: SummaryResult) => {
  try {
    const cacheData = {
      data: JSON.parse(JSON.stringify(data)), // 深拷贝移除readonly
      timestamp: Date.now(),
    }
    localStorage.setItem(cacheKey, JSON.stringify(cacheData))
  } catch (error) {
    console.error('保存摘要缓存失败:', error)
  }
}

const clearSummaryCache = () => {
  try {
    // 清除所有摘要相关的缓存
    const keys = Object.keys(localStorage)
    keys.forEach((key) => {
      if (key.startsWith('summary_')) {
        localStorage.removeItem(key)
      }
    })
    console.log('摘要缓存已清除')
  } catch (error) {
    console.error('清除摘要缓存失败:', error)
  }
}

// 合并错误信息
const errorMessage = computed(() => {
  return (
    speechErrorMessage.value ||
    fileErrorMessage.value ||
    chunkingError.value ||
    translationError.value ||
    lineTranslationError.value ||
    videoLingoTranslationError.value ||
    summaryErrorMessage.value ||
    _alignmentError.value
  )
})

// 计算属性
const canStartRecognitionComputed = computed(() => {
  return uploadedFile.value && apiConfig.appid && apiConfig.token && canStartRecognition.value
})

// 翻译配置
const translationConfig = ref({
  targetLanguage: '简体中文',
  maxWorkers: 4,
})

// 逐行翻译配置
const lineByLineConfig = ref({
  targetLanguage: '简体中文',
  sourceLanguage: 'English',
  reflectTranslate: true,
})

// VideoLingo翻译配置
const videoLingoConfig_local = ref({
  targetLanguage: '简体中文',
  sourceLanguage: 'English',
  reflectTranslate: true,
  maxWorkers: 4,
  chunkSize: 600, // 按文档推荐：600字符提供丰富上下文
  maxSentences: 10, // 按文档推荐：最多10句，保持语义完整性
})

// 方法
const handleStartRecognition = async () => {
  if (!canStartRecognitionComputed.value || !uploadedFile.value) return

  const recognitionParams = {
    language: selectedLanguage.value,
    words_per_line: maxCharsPerLine.value,
    max_lines: maxLines.value,
    caption_type: selectedContentType.value,
    use_itn: features.useItn,
    use_punc: features.usePunc,
    use_ddc: features.useDdc,
    with_speaker_info: features.withSpeakerInfo,
  }

  await startRecognition(uploadedFile.value, apiConfig, recognitionParams)
}

const handleDownloadSubtitle = () => {
  if (recognitionResult.value) {
    downloadSubtitle(recognitionResult.value, uploadedFile.value?.name)
  }
}

const handleMergeRecognitionResult = () => {
  const content = mergeRecognitionResult()
  console.log('%c AT 🥝 content 🥝-68', 'font-size:13px; background:#113083; color:#5574c7;', content)
}

const handleSmartChunking = async () => {
  if (!recognitionResult.value) return

  try {
    await performSmartChunking(recognitionResult.value)
  } catch (error) {
    console.error('智能分块失败:', error)
  }
}

const handleConcurrentTranslation = async () => {
  if (chunks.value.length === 0) return

  try {
    await performConcurrentTranslation(translationConfig.value.targetLanguage, translationConfig.value.maxWorkers)
  } catch (error) {
    console.error('并发翻译失败:', error)
  }
}

// 逐行翻译方法
const handleLineByLineTranslation = async () => {
  if (!recognitionResult.value) return

  try {
    // 更新逐行翻译配置
    updateLineTranslationConfig({
      targetLanguage: lineByLineConfig.value.targetLanguage,
      sourceLanguage: lineByLineConfig.value.sourceLanguage,
      reflectTranslate: lineByLineConfig.value.reflectTranslate,
    })

    await translateRecognitionResult(recognitionResult.value, {
      summary: '这是一段语音识别的字幕内容，需要进行准确翻译。',
      thingsToNote: '请注意保持专业术语的准确性和语言表达的自然流畅。',
    })
  } catch (error) {
    console.error('逐行翻译失败:', error)
  }
}

// 下载逐行翻译结果
const handleDownloadLineTranslation = () => {
  if (finalResults.value.size === 0) return

  const translatedText = Array.from(finalResults.value.values()).join('\n')
  const blob = new Blob([translatedText], { type: 'text/plain;charset=utf-8' })
  const url = URL.createObjectURL(blob)
  const a = document.createElement('a')
  a.href = url
  a.download = `逐行翻译结果_${new Date().toISOString().slice(0, 19).replace(/:/g, '-')}.txt`
  document.body.appendChild(a)
  a.click()
  document.body.removeChild(a)
  URL.revokeObjectURL(url)
}

// VideoLingo翻译方法
const handleVideoLingoTranslation = async () => {
  if (!recognitionResult.value) return

  try {
    // 更新VideoLingo翻译配置
    updateVideoLingoConfig({
      targetLanguage: videoLingoConfig_local.value.targetLanguage,
      sourceLanguage: videoLingoConfig_local.value.sourceLanguage,
      reflectTranslate: videoLingoConfig_local.value.reflectTranslate,
      maxWorkers: videoLingoConfig_local.value.maxWorkers,
      chunkSize: videoLingoConfig_local.value.chunkSize,
      maxSentences: videoLingoConfig_local.value.maxSentences,
    })

    // 构建动态的上下文信息
    let summaryText = '这是一段语音识别的字幕内容，使用VideoLingo架构进行高质量翻译。'
    let termsNote = '请注意保持专业术语的准确性、语言表达的自然流畅性，并维持上下文的连贯性。'

    // 如果有摘要提取结果，使用动态内容
    if (extractionResult.value) {
      summaryText = extractionResult.value.theme

      // 构建术语注意事项
      if (extractionResult.value.terms.length > 0) {
        const termsList = extractionResult.value.terms
          .map((term: { src: string; tgt: string; note: string }) => `- ${term.src}: ${term.tgt} (${term.note})`)
          .join('\n')

        termsNote = `请特别注意以下专业术语的准确翻译：\n${termsList}\n\n同时保持语言表达的自然流畅性，并维持上下文的连贯性。`
      }
    }

    await translateAllVideoLingo(recognitionResult.value, {
      summary: summaryText,
      thingsToNote: termsNote,
    })
  } catch (error) {
    console.error('VideoLingo翻译失败:', error)
  }
}

// 下载VideoLingo翻译结果
const handleDownloadVideoLingoTranslation = () => {
  if (videoLingoResults.value.size === 0) return

  const translatedText = Array.from(videoLingoResults.value.values()).join('\n')
  const blob = new Blob([translatedText], { type: 'text/plain;charset=utf-8' })
  const url = URL.createObjectURL(blob)
  const a = document.createElement('a')
  a.href = url
  a.download = `VideoLingo翻译结果_${new Date().toISOString().slice(0, 19).replace(/:/g, '-')}.txt`
  document.body.appendChild(a)
  a.click()
  document.body.removeChild(a)
  URL.revokeObjectURL(url)
}

// 摘要提取方法
const handleSummaryExtraction = async () => {
  if (!recognitionResult.value) return

  try {
    // 生成缓存键
    const cacheKey = generateCacheKey()

    if (cacheKey) {
      // 检查是否有缓存
      const cachedResult = loadSummaryFromCache(cacheKey)

      if (cachedResult) {
        // 使用缓存的结果
        setCachedResult(cachedResult)
        console.log('🎯 使用缓存的摘要结果')
        return
      }
    }

    // 没有缓存，进行摘要提取
    console.log('📝 开始提取摘要...')
    const mergedText = mergeRecognitionResult()
    await extractSummaryFn(mergedText)

    // 保存到缓存
    if (cacheKey && extractionResult.value) {
      saveSummaryToCache(cacheKey, extractionResult.value as SummaryResult)
      console.log('💾 摘要结果已保存到缓存')
    }
  } catch (error) {
    console.error('摘要提取失败:', error)
  }
}

// 下载摘要结果
const handleDownloadSummary = () => {
  if (!extractionResult.value) return

  const summaryContent = `主题摘要：
${extractionResult.value.theme}

专业术语：
${extractionResult.value.terms
  .map((term: { src: string; tgt: string; note: string }) => `${term.src} → ${term.tgt} (${term.note})`)
  .join('\n')}`

  const blob = new Blob([summaryContent], { type: 'text/plain;charset=utf-8' })
  const url = URL.createObjectURL(blob)
  const a = document.createElement('a')
  a.href = url
  a.download = `内容摘要_${new Date().toISOString().slice(0, 19).replace(/:/g, '-')}.txt`
  document.body.appendChild(a)
  a.click()
  document.body.removeChild(a)
  URL.revokeObjectURL(url)
}

// 智能翻译：摘要提取 + VideoLingo翻译
const handleSmartTranslation = async () => {
  if (!recognitionResult.value) return

  try {
    // 如果没有摘要结果，先进行摘要提取
    if (!extractionResult.value) {
      const mergedText = mergeRecognitionResult()
      await extractSummaryFn(mergedText)
    }

    // 然后进行VideoLingo翻译
    await handleVideoLingoTranslation()

    // 添加成功提示
    console.log('🎉 智能翻译完成！摘要提取和VideoLingo翻译都已完成，结果已显示在页面上。')
  } catch (error) {
    console.error('智能翻译失败:', error)
  }
}

// 时间戳对齐相关方法
const _handleTimestampAlignment = async () => {
  if (!recognitionResult.value || videoLingoResults.value.size === 0) {
    return
  }

  try {
    console.log('🔄 开始句子级时间戳对齐...')

    // 准备utterances数据（句子级）
    const utterances = recognitionResult.value.utterances.map((utterance) => ({
      start_time: utterance.start_time,
      end_time: utterance.end_time,
      text: utterance.text,
      attribute: utterance.attribute,
    }))

    // 准备翻译数据
    const translations = Array.from(videoLingoResults.value.entries()).map(([index, translation]) => ({
      source: recognitionResult.value!.utterances[index]?.text || '',
      translation: translation,
      index: index,
    }))

    console.log('📊 对齐数据统计:', {
      句子数量: utterances.length,
      翻译数量: translations.length,
      数据类型: '句子级',
    })

    // 导入数据
    const sourceImported = importSourceData(utterances)
    const translationImported = importTranslationData(translations)

    if (sourceImported && translationImported) {
      // 执行对齐
      console.log('🎯 开始执行句子级时间戳对齐...')
      await performAlignment()
    }
  } catch (error) {
    console.error('时间戳对齐失败:', error)
  }
}

const _handleShowAlignmentPreview = () => {
  if (hasAlignmentResults.value) {
    openAlignmentPreview()
  }
}

const _handleConfirmTimestampAlignment = () => {
  if (confirmAlignment()) {
    // 对齐成功，可以进行后续操作
    console.log('时间戳对齐已应用')
  }
}

// 页面标题
useHead({
  title: '字幕生成 - 火山引擎',
})
</script>

<template>
  <div class="min-h-screen bg-gray-50">
    <!-- 页面标题 -->
    <div class="bg-white border-b border-gray-200">
      <div class="max-w-6xl mx-auto px-6 py-4">
        <h1 class="text-2xl font-bold text-gray-900">字幕生成</h1>
        <p class="text-gray-600 mt-1">使用火山引擎AI技术自动生成字幕</p>
      </div>
    </div>

    <div class="max-w-6xl mx-auto p-8">
      <div class="bg-white rounded-lg shadow-sm border border-gray-200 p-6 space-y-8">
        <!-- 字幕内容 -->
        <div class="space-y-4">
          <h2 class="text-lg font-semibold text-gray-900 flex items-center">
            <UIcon name="i-heroicons-document-text" class="mr-2 text-green-600" />
            字幕内容
          </h2>

          <div class="bg-green-50 border border-green-200 rounded-lg p-4">
            <URadioGroup v-model="selectedContentType" :options="contentTypeOptions" />
          </div>
        </div>

        <!-- 特色功能 -->
        <div class="space-y-4 hidden">
          <h2 class="text-lg font-semibold text-gray-900 flex items-center">
            <UIcon name="i-heroicons-sparkles" class="mr-2 text-purple-600" />
            特色功能
          </h2>

          <div class="bg-purple-50 border border-purple-200 rounded-lg p-4 space-y-4">
            <!-- 功能开关 -->
            <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
              <div class="flex items-center justify-between">
                <label class="text-sm font-medium text-gray-700">标点符号</label>
                <UToggle v-model="features.usePunc" />
              </div>

              <div class="flex items-center justify-between">
                <label class="text-sm font-medium text-gray-700">水词标注</label>
                <UToggle v-model="features.useDdc" />
              </div>

              <div class="flex items-center justify-between">
                <label class="text-sm font-medium text-gray-700">说话人识别</label>
                <UToggle v-model="features.withSpeakerInfo" />
              </div>

              <div class="flex items-center justify-between">
                <label class="text-sm font-medium text-gray-700">数字转换</label>
                <UToggle v-model="features.useItn" />
              </div>
            </div>

            <!-- 每行最多显示字数 -->
            <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
              <div class="space-y-2">
                <label class="text-sm font-medium text-gray-700">每行最多显示字数</label>
                <div class="flex items-center space-x-4">
                  <UInput v-model="maxCharsPerLine" type="number" min="10" max="100" class="w-20" />
                  <span class="text-sm text-gray-500">建议：{{ getRecommendedCharsPerLine() }}字</span>
                </div>
              </div>

              <div class="space-y-2">
                <label class="text-sm font-medium text-gray-700">每屏最多显示行数</label>
                <div class="flex items-center space-x-4">
                  <UInput v-model="maxLines" type="number" min="1" max="10" class="w-20" />
                  <span class="text-sm text-gray-500">默认：1行</span>
                </div>
              </div>
            </div>
          </div>
        </div>

        <!-- 音视频文件上传 -->
        <div class="space-y-4">
          <h2 class="text-lg font-semibold text-gray-900 flex items-center">
            <UIcon name="i-heroicons-film" class="mr-2 text-orange-600" />
            音视频文件
          </h2>

          <div class="bg-orange-50 border border-orange-200 rounded-lg p-6">
            <!-- 文件上传区域 -->
            <div class="border-2 border-dashed border-orange-300 rounded-lg p-8 text-center hover:border-orange-400 transition-colors">
              <input
                ref="fileInputRef"
                type="file"
                accept="audio/*,video/*,.wav,.m4a,.mp3,.mp4,.mov,.ogg"
                class="hidden"
                @change="handleFileUpload"
              />

              <div v-if="!uploadedFile" class="space-y-4">
                <UIcon name="i-heroicons-cloud-arrow-up" class="mx-auto h-12 w-12 text-orange-400" />
                <div>
                  <UButton color="orange" size="lg" @click="triggerFileInput">
                    <UIcon name="i-heroicons-plus" class="mr-2" />
                    选择文件
                  </UButton>
                </div>
                <p class="text-sm text-gray-600">
                  支持 WAV、M4A、MP3、MP4、MOV、OGG，小于 150MB
                </p>
                <p class="text-xs text-gray-500">
                  音频时长需小于一小时，推荐使用 WAV 格式以获得最佳兼容性
                </p>
                <p class="text-xs text-gray-400">
                  背景音干净、人声清晰的音视频内容，识别效果更佳哦～
                </p>
              </div>

              <!-- 文件信息显示 -->
              <div v-else class="space-y-4">
                <UIcon name="i-heroicons-document" class="mx-auto h-12 w-12 text-green-500" />
                <div>
                  <p class="font-medium text-gray-900">{{ uploadedFile.name }}</p>
                  <p class="text-sm text-gray-500">{{ formatFileSize(uploadedFile.size) }}</p>
                  <p class="text-xs text-gray-400">{{ uploadedFile.type || '未知格式' }}</p>
                </div>
                <UButton color="orange" variant="outline" @click="removeFile">
                  <UIcon name="i-heroicons-trash" class="mr-2" />
                  移除文件
                </UButton>
              </div>
            </div>
          </div>
        </div>

        <!-- 开始识别按钮 -->
        <div class="flex justify-center pt-6">
          <UButton color="blue" size="xl" :disabled="!canStartRecognitionComputed" :loading="isProcessing" @click="handleStartRecognition">
            <UIcon name="i-heroicons-play" class="mr-2" />
            {{ isProcessing ? '识别中...' : '开始识别' }}
          </UButton>
        </div>

        <!-- 识别进度 -->
        <div v-if="jobId && isProcessing" class="space-y-4">
          <h2 class="text-lg font-semibold text-gray-900 flex items-center">
            <UIcon name="i-heroicons-clock" class="mr-2 text-blue-600" />
            识别进度
          </h2>
          <div class="bg-blue-50 border border-blue-200 rounded-lg p-4">
            <div class="flex items-center justify-between">
              <span class="text-sm text-gray-700">任务ID：{{ jobId }}</span>
              <UBadge color="blue" variant="soft">处理中</UBadge>
            </div>
            <div class="mt-2">
              <div class="w-full bg-gray-200 rounded-full h-2">
                <div class="bg-blue-600 h-2 rounded-full animate-pulse" style="width: 60%;"></div>
              </div>
            </div>
          </div>
        </div>

        <!-- 识别结果展示区域 -->
        <div v-if="recognitionResult" class="space-y-4">
          <h2 class="text-lg font-semibold text-gray-900 flex items-center">
            <UIcon name="i-heroicons-check-circle" class="mr-2 text-green-600" />
            识别结果
          </h2>

          <div class="bg-gray-50 border border-gray-200 rounded-lg p-4">
            <div class="space-y-4">
              <!-- 基本信息 -->
              <div class="grid grid-cols-2 md:grid-cols-4 gap-4 text-sm">
                <div>
                  <span class="text-gray-500">语言：</span>
                  <span class="font-medium text-blue-300">{{ getLanguageLabel(selectedLanguage) }}</span>
                </div>
                <div>
                  <span class="text-gray-500">类型：</span>
                  <span class="font-medium text-blue-300">{{ getContentTypeLabel(selectedContentType) }}</span>
                </div>
                <div>
                  <span class="text-gray-500">时长：</span>
                  <span class="font-medium text-blue-300">{{ recognitionResult.duration }}秒</span>
                </div>
                <div>
                  <span class="text-gray-500">状态：</span>
                  <UBadge color="green">已完成</UBadge>
                </div>
              </div>

              <!-- 字幕内容 -->
              <div class="border-t pt-4">
                <h3 class="font-medium text-gray-900 mb-2">字幕内容：</h3>
                <div class="max-h-96 overflow-y-auto space-y-2">
                  <div v-for="(utterance, index) in recognitionResult.utterances" :key="index" class="bg-white p-3 rounded border">
                    <div class="flex justify-between items-start mb-1">
                      <span class="text-xs text-gray-500">
                        {{ formatTimeToString(millisecondsToSeconds(utterance.start_time)) }} - {{ formatTimeToString(millisecondsToSeconds(utterance.end_time)) }}
                      </span>
                      <span v-if="utterance.attribute?.speaker" class="text-xs bg-blue-100 text-blue-800 px-1 rounded">
                        说话人{{ utterance.attribute.speaker }}
                      </span>
                    </div>
                    <p class="text-gray-900">{{ utterance.text }}</p>
                  </div>
                </div>
              </div>
            </div>
          </div>

          <div class="flex gap-4">
            <UButton color="green" @click="handleDownloadSubtitle">
              <UIcon name="i-heroicons-arrow-down-tray" class="mr-2" />
              下载 SRT 字幕
            </UButton>
            <!-- 合并识别结果 -->
            <UButton color="green" @click="handleMergeRecognitionResult">
              <UIcon name="i-heroicons-arrow-down-tray" class="mr-2" />
              合并识别结果
            </UButton>
            <!-- 智能分块按钮 -->
            <UButton color="blue" :loading="isChunking" @click="handleSmartChunking">
              <UIcon name="i-heroicons-squares-2x2" class="mr-2" />
              {{ isChunking ? '分块中...' : '智能分块' }}
            </UButton>
            <!-- 逐行翻译按钮 -->
            <UButton color="purple" :loading="isLineTranslating" @click="handleLineByLineTranslation">
              <UIcon name="i-heroicons-language" class="mr-2" />
              {{ isLineTranslating ? '翻译中...' : '逐行翻译' }}
            </UButton>
            <!-- VideoLingo翻译按钮 -->
            <UButton color="purple" :loading="isVideoLingoTranslating" @click="handleVideoLingoTranslation">
              <UIcon name="i-heroicons-language" class="mr-2" />
              {{ isVideoLingoTranslating ? '翻译中...' : 'VideoLingo翻译' }}
            </UButton>
            <!-- 摘要提取按钮 -->
            <UButton color="orange" :loading="isExtracting" @click="handleSummaryExtraction">
              <UIcon name="i-heroicons-document-text" class="mr-2" />
              {{ isExtracting ? '提取中...' : '内容摘要' }}
            </UButton>
            <!-- 智能翻译组合按钮 -->
            <UButton color="emerald" size="lg" :loading="isExtracting || isVideoLingoTranslating" @click="handleSmartTranslation">
              <UIcon name="i-heroicons-sparkles" class="mr-2" />
              {{ isExtracting || isVideoLingoTranslating ? '智能翻译中...' : '智能翻译' }}
            </UButton>
          </div>

          <!-- 智能翻译提示 -->
          <div v-if="recognitionResult" class="mt-4 p-4 bg-blue-50 border border-blue-200 rounded-lg">
            <div class="flex items-start space-x-3">
              <UIcon name="i-heroicons-light-bulb" class="text-blue-500 mt-1" />
              <div>
                <h4 class="font-medium text-blue-900">智能翻译建议</h4>
                <div v-if="!extractionResult" class="mt-1 text-sm text-blue-700">
                  🚀
                  <strong>推荐使用「智能翻译」</strong
                  >：自动提取视频主题和专业术语，然后使用VideoLingo架构进行高质量翻译，一键完成整个流程。
                  <br />
                  <span class="text-blue-600">💡 提示</span>：如需保持字幕一对一对应，请在VideoLingo配置中将"每块最大句子数"设为1
                  <br />
                  <span class="text-blue-600">或者</span> 分步操作：先点击「内容摘要」→ 再点击「VideoLingo翻译」
                </div>
                <div v-else class="mt-1 text-sm text-blue-700">
                  ✅ 已提取摘要信息！现在可以：
                  <div class="mt-2 space-y-1">
                    <div>• 直接使用「VideoLingo翻译」进行智能翻译</div>
                    <div>• 或使用「智能翻译」重新分析+翻译</div>
                    <div class="mt-2 text-xs text-blue-600">
                      <strong>当前摘要：</strong>{{ extractionResult.theme }}
                      <span v-if="extractionResult.terms.length > 0">
                        <br /><strong>专业术语：</strong>{{ extractionResult.terms.length }}个
                      </span>
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </div>

          <!-- 智能翻译结果展示 -->
          <div v-if="extractionResult && videoLingoResults.size > 0" class="mt-6 space-y-4">
            <h2 class="text-lg font-semibold text-gray-900 flex items-center">
              <UIcon name="i-heroicons-sparkles" class="mr-2 text-emerald-600" />
              智能翻译结果
              <UBadge color="green" class="ml-2">完成</UBadge>
            </h2>

            <div class="bg-emerald-50 border border-emerald-200 rounded-lg p-6">
              <!-- 成功提示 -->
              <div class="mb-6 p-4 bg-green-100 border border-green-300 rounded-lg">
                <div class="flex items-center">
                  <UIcon name="i-heroicons-check-circle" class="text-green-600 mr-3 h-6 w-6" />
                  <div>
                    <h3 class="font-medium text-green-900">🎉 智能翻译完成！</h3>
                    <p class="text-sm text-green-700 mt-1">
                      已完成内容摘要提取和VideoLingo高质量翻译，结果如下：
                    </p>
                  </div>
                </div>
              </div>

              <div class="grid grid-cols-1 lg:grid-cols-2 gap-6">
                <!-- 摘要信息 -->
                <div class="space-y-4">
                  <h3 class="font-medium text-gray-900 flex items-center">
                    <UIcon name="i-heroicons-document-text" class="mr-2 text-blue-600" />
                    内容摘要
                  </h3>

                  <div class="bg-white border border-gray-200 rounded-lg p-4">
                    <div class="space-y-3">
                      <div>
                        <span class="text-sm font-medium text-gray-500">主题：</span>
                        <p class="text-gray-900 mt-1">{{ extractionResult.theme }}</p>
                      </div>

                      <div v-if="extractionResult.terms.length > 0">
                        <span class="text-sm font-medium text-gray-500">专业术语 ({{ extractionResult.terms.length }}个)：</span>
                        <div class="mt-2 max-h-32 overflow-y-auto space-y-1">
                          <div
                            v-for="(term, index) in extractionResult.terms.slice(0, 3)"
                            :key="index"
                            class="text-sm bg-gray-50 p-2 rounded"
                          >
                            <span class="font-mono text-blue-600">{{ term.src }}</span> →
                            <span class="font-mono text-purple-600">{{ term.tgt }}</span>
                          </div>
                          <div v-if="extractionResult.terms.length > 3" class="text-xs text-gray-500 text-center py-1">
                            ... 还有{{ extractionResult.terms.length - 3 }}个术语
                          </div>
                        </div>
                      </div>
                    </div>
                  </div>
                </div>

                <!-- 翻译统计 -->
                <div class="space-y-4">
                  <h3 class="font-medium text-gray-900 flex items-center">
                    <UIcon name="i-heroicons-chart-bar" class="mr-2 text-purple-600" />
                    翻译统计
                  </h3>

                  <div class="bg-white border border-gray-200 rounded-lg p-4">
                    <div class="grid grid-cols-2 gap-4 text-center">
                      <div class="space-y-1">
                        <div class="text-2xl font-bold text-purple-600">{{ getVideoLingoStatistics.totalChunks }}</div>
                        <div class="text-xs text-gray-500">分块总数</div>
                      </div>
                      <div class="space-y-1">
                        <div class="text-2xl font-bold text-green-600">{{ getVideoLingoStatistics.completedChunks }}</div>
                        <div class="text-xs text-gray-500">已完成</div>
                      </div>
                      <div class="space-y-1">
                        <div class="text-sm font-medium text-blue-600">
                          {{ getVideoLingoStatistics.reflectTranslateEnabled ? '两步翻译' : '忠实翻译' }}
                        </div>
                        <div class="text-xs text-gray-500">翻译模式</div>
                      </div>
                      <div class="space-y-1">
                        <div class="text-sm font-medium text-emerald-600">VideoLingo</div>
                        <div class="text-xs text-gray-500">翻译架构</div>
                      </div>
                    </div>
                  </div>
                </div>
              </div>

              <!-- 翻译内容预览 -->
              <div class="mt-6 space-y-4">
                <h3 class="font-medium text-gray-900 flex items-center">
                  <UIcon name="i-heroicons-language" class="mr-2 text-green-600" />
                  翻译内容
                  <span class="ml-2 text-sm text-gray-500">({{ videoLingoResults.size }}条)</span>
                </h3>

                <div class="bg-white border border-gray-200 rounded-lg p-4">
                  <div class="max-h-64 overflow-y-auto space-y-3">
                    <div
                      v-for="(translation, index) in Array.from(videoLingoResults.values()).slice(0, 5)"
                      :key="index"
                      class="p-3 bg-gray-50 rounded border-l-4 border-emerald-400"
                    >
                      <p class="text-gray-900">{{ translation }}</p>
                    </div>
                    <div v-if="videoLingoResults.size > 5" class="text-center py-2 text-sm text-gray-500 border-t border-gray-200">
                      ... 还有{{ videoLingoResults.size - 5 }}条翻译结果
                    </div>
                  </div>
                </div>
              </div>

              <!-- 操作按钮 -->
              <div class="mt-6 flex justify-center gap-4">
                <UButton color="emerald" @click="handleDownloadVideoLingoTranslation">
                  <UIcon name="i-heroicons-arrow-down-tray" class="mr-2" />
                  下载翻译结果
                </UButton>
                <UButton color="emerald" @click="handleDownloadSummary">
                  <UIcon name="i-heroicons-document-arrow-down" class="mr-2" />
                  下载摘要信息
                </UButton>
                <!-- 时间戳对齐按钮 -->
                <UButton
                  color="cyan"
                  size="lg"
                  :loading="_isAligning"
                  :disabled="!recognitionResult || videoLingoResults.size === 0"
                  @click="_handleTimestampAlignment"
                >
                  <UIcon name="i-heroicons-clock" class="mr-2" />
                  {{ _isAligning ? '对齐中...' : '智能时间戳对齐' }}
                </UButton>
              </div>
            </div>
          </div>

          <!-- 时间戳对齐结果展示 -->
          <div v-if="hasAlignmentResults" class="mt-6 space-y-4">
            <h2 class="text-lg font-semibold text-gray-900 flex items-center">
              <UIcon name="i-heroicons-clock" class="mr-2 text-cyan-600" />
              时间戳对齐结果
              <UBadge :color="hasAlignmentResults ? 'green' : 'gray'" class="ml-2">
                {{ hasAlignmentResults ? '对齐完成' : '未对齐' }}
              </UBadge>
            </h2>

            <div class="bg-cyan-50 border border-cyan-200 rounded-lg p-6">
              <!-- 对齐统计信息 -->
              <div v-if="_successStatistics" class="mb-6 grid grid-cols-1 md:grid-cols-4 gap-4">
                <div class="bg-white border border-gray-200 rounded-lg p-4 text-center">
                  <div class="text-2xl font-bold text-cyan-600">{{ _successStatistics.total }}</div>
                  <div class="text-xs text-gray-500">总条目</div>
                </div>
                <div class="bg-white border border-gray-200 rounded-lg p-4 text-center">
                  <div class="text-2xl font-bold text-green-600">{{ _successStatistics.success }}</div>
                  <div class="text-xs text-gray-500">成功对齐</div>
                </div>
                <div class="bg-white border border-gray-200 rounded-lg p-4 text-center">
                  <div class="text-2xl font-bold text-yellow-600">{{ _successStatistics.partial }}</div>
                  <div class="text-xs text-gray-500">部分对齐</div>
                </div>
                <div class="bg-white border border-gray-200 rounded-lg p-4 text-center">
                  <div class="text-lg font-bold text-cyan-600">{{ _successStatistics.successRate }}%</div>
                  <div class="text-xs text-gray-500">成功率</div>
                </div>
              </div>

              <!-- 对齐结果预览 -->
              <div v-if="_previewData" class="space-y-4">
                <h3 class="font-medium text-gray-900 flex items-center">
                  <UIcon name="i-heroicons-eye" class="mr-2 text-cyan-600" />
                  对齐结果预览
                  <span class="ml-2 text-sm text-gray-500">(前5条)</span>
                </h3>

                <div class="bg-white border border-gray-200 rounded-lg p-4">
                  <div class="space-y-3">
                    <div
                      v-for="(item, index) in _previewData.after.slice(0, 5)"
                      :key="index"
                      class="flex items-start space-x-3 p-3 border border-gray-100 rounded"
                    >
                      <div
                        class="flex-shrink-0 w-4 h-4 rounded-full mt-1"
                        :class="{
                          'bg-green-500': item.confidence >= 0.8,
                          'bg-yellow-500': item.confidence >= 0.5 && item.confidence < 0.8,
                          'bg-red-500': item.confidence < 0.5,
                        }"
                      ></div>
                      <div class="flex-1 min-w-0">
                        <div class="text-xs text-gray-500 mb-1">
                          {{ item.time }} | 策略: {{ item.strategy }} | 置信度: {{ (item.confidence * 100).toFixed(1) }}%
                        </div>
                        <p class="text-gray-900">{{ item.text }}</p>
                      </div>
                    </div>
                  </div>
                </div>
              </div>

              <!-- 失败项目显示 -->
              <div v-if="_failedItems && _failedItems.length > 0" class="mt-6 space-y-4">
                <h3 class="font-medium text-gray-900 flex items-center">
                  <UIcon name="i-heroicons-exclamation-triangle" class="mr-2 text-red-600" />
                  对齐失败项目
                  <span class="ml-2 text-sm text-gray-500">({{ _failedItems.length }}条)</span>
                </h3>

                <div class="bg-red-50 border border-red-200 rounded-lg p-4">
                  <div class="space-y-2 max-h-32 overflow-y-auto">
                    <div
                      v-for="(failedItem, index) in _failedItems.slice(0, 3)"
                      :key="index"
                      class="text-sm text-red-700 bg-white p-2 rounded border-l-4 border-red-400"
                    >
                      <strong>第{{ failedItem.index + 1 }}条:</strong> {{ failedItem.sourceText.substring(0, 50) }}...
                      <div class="text-xs text-red-600 mt-1">错误: {{ failedItem.error?.message }}</div>
                    </div>
                    <div v-if="_failedItems.length > 3" class="text-center text-sm text-red-600 py-2 border-t border-red-200">
                      ... 还有{{ _failedItems.length - 3 }}个失败项目
                    </div>
                  </div>
                </div>
              </div>

              <!-- 操作按钮 -->
              <div class="mt-6 flex justify-center gap-4">
                <UButton v-if="_previewData" color="cyan" @click="_handleShowAlignmentPreview">
                  <UIcon name="i-heroicons-eye" class="mr-2" />
                  查看完整预览
                </UButton>
                <UButton color="green" size="lg" :disabled="!hasAlignmentResults" @click="_handleConfirmTimestampAlignment">
                  <UIcon name="i-heroicons-check" class="mr-2" />
                  确认应用对齐结果
                </UButton>
              </div>
            </div>
          </div>
        </div>

        <!-- 逐行翻译结果展示 -->
        <div v-if="finalResults.size > 0" class="space-y-4">
          <h2 class="text-lg font-semibold text-gray-900 flex items-center">
            <UIcon name="i-heroicons-language" class="mr-2 text-purple-600" />
            逐行翻译结果
          </h2>

          <div class="bg-purple-50 border border-purple-200 rounded-lg p-4">
            <div class="space-y-4">
              <!-- 翻译统计信息 -->
              <div class="grid grid-cols-2 md:grid-cols-4 gap-4 text-sm">
                <div>
                  <span class="text-gray-500">翻译模式：</span>
                  <span class="font-medium text-purple-600">
                    {{ getTranslationStatistics.reflectTranslateEnabled ? '两步翻译' : '忠实翻译' }}
                  </span>
                </div>
                <div>
                  <span class="text-gray-500">总行数：</span>
                  <span class="font-medium text-purple-600">{{ getTranslationStatistics.totalTranslated }}</span>
                </div>
                <div>
                  <span class="text-gray-500">忠实翻译：</span>
                  <span class="font-medium text-purple-600">{{ faithfulResults.size }}</span>
                </div>
                <div v-if="getTranslationStatistics.reflectTranslateEnabled">
                  <span class="text-gray-500">表达优化：</span>
                  <span class="font-medium text-purple-600">{{ expressiveResults.size }}</span>
                </div>
              </div>

              <!-- 翻译内容 -->
              <div class="border-t pt-4">
                <h3 class="font-medium text-gray-900 mb-2">翻译结果：</h3>
                <div class="max-h-96 overflow-y-auto space-y-2">
                  <div v-for="(translation, index) in Array.from(finalResults.values())" :key="index" class="bg-white p-3 rounded border">
                    <p class="text-gray-900">{{ translation }}</p>
                  </div>
                </div>
              </div>
            </div>

            <div class="mt-4 flex gap-2">
              <UButton color="green" @click="handleDownloadLineTranslation">
                <UIcon name="i-heroicons-arrow-down-tray" class="mr-2" />
                下载翻译结果
              </UButton>
              <UButton variant="outline" @click="resetLineTranslation">
                <UIcon name="i-heroicons-trash" class="mr-2" />
                清除翻译结果
              </UButton>
            </div>
          </div>
        </div>

        <!-- 智能分块结果展示区域 -->
        <div v-if="chunks.length > 0" class="space-y-4">
          <h2 class="text-lg font-semibold text-gray-900 flex items-center">
            <UIcon name="i-heroicons-squares-2x2" class="mr-2 text-purple-600" />
            智能分块结果
          </h2>

          <div class="bg-purple-50 border border-purple-200 rounded-lg p-4">
            <ChunkDisplay
              :chunks="chunks"
              :contexts="contexts"
              :statistics="statistics"
              :translation-results="translationResults"
              :is-translating="isTranslating"
            />

            <div class="mt-4 flex gap-2">
              <UButton size="sm" variant="outline" @click="resetChunking">
                <UIcon name="i-heroicons-trash" class="mr-1" />
                清除分块
              </UButton>
              <UButton size="sm" color="blue" :loading="isTranslating" :disabled="chunks.length === 0" @click="handleConcurrentTranslation">
                <UIcon name="i-heroicons-language" class="mr-1" />
                {{ isTranslating ? '翻译中...' : '并发翻译' }}
              </UButton>
              <UButton size="sm" variant="outline" color="gray" @click="exportChunksAsJson">
                <UIcon name="i-heroicons-arrow-down-tray" class="mr-1" />
                导出JSON
              </UButton>
            </div>
          </div>
        </div>

        <!-- 翻译配置面板 -->
        <div v-if="chunks.length > 0" class="space-y-4">
          <h2 class="text-lg font-semibold text-gray-900 flex items-center">
            <UIcon name="i-heroicons-language" class="mr-2 text-emerald-600" />
            翻译配置
          </h2>

          <div class="bg-emerald-50 border border-emerald-200 rounded-lg p-4 space-y-4">
            <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
              <div class="space-y-2">
                <label class="text-sm font-medium text-gray-700">目标语言</label>
                <USelect v-model="translationConfig.targetLanguage" :options="[{ label: '简体中文', value: '简体中文' }]" class="w-full" />
              </div>

              <div class="space-y-2">
                <label class="text-sm font-medium text-gray-700">并发数量</label>
                <UInput v-model.number="translationConfig.maxWorkers" type="number" min="1" max="8" class="w-full" />
              </div>
            </div>
          </div>
        </div>

        <!-- 智能分块配置面板 -->
        <div v-if="recognitionResult" class="space-y-4">
          <h2 class="text-lg font-semibold text-gray-900 flex items-center">
            <UIcon name="i-heroicons-cog-6-tooth" class="mr-2 text-indigo-600" />
            分块配置
          </h2>

          <div class="bg-indigo-50 border border-indigo-200 rounded-lg p-4 space-y-4">
            <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
              <div class="space-y-2">
                <label class="text-sm font-medium text-gray-700">分块大小（字符数）</label>
                <UInput v-model.number="config.chunkSize" type="number" min="100" max="2000" class="w-full" />
              </div>

              <div class="space-y-2">
                <label class="text-sm font-medium text-gray-700">最大句子数</label>
                <UInput v-model.number="config.maxSentences" type="number" min="1" max="50" class="w-full" />
              </div>

              <div class="space-y-2">
                <label class="text-sm font-medium text-gray-700">前文行数</label>
                <UInput v-model.number="config.previousLines" type="number" min="0" max="10" class="w-full" />
              </div>

              <div class="space-y-2">
                <label class="text-sm font-medium text-gray-700">后文行数</label>
                <UInput v-model.number="config.afterLines" type="number" min="0" max="10" class="w-full" />
              </div>
            </div>
          </div>
        </div>

        <!-- 逐行翻译配置面板 -->
        <div v-if="recognitionResult" class="space-y-4">
          <h2 class="text-lg font-semibold text-gray-900 flex items-center">
            <UIcon name="i-heroicons-cog-6-tooth" class="mr-2 text-indigo-600" />
            逐行翻译配置
          </h2>

          <div class="bg-indigo-50 border border-indigo-200 rounded-lg p-4 space-y-4">
            <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
              <div class="space-y-2">
                <label class="text-sm font-medium text-gray-700">目标语言</label>
                <USelect v-model="lineByLineConfig.targetLanguage" :options="[{ label: '简体中文', value: '简体中文' }]" class="w-full" />
              </div>

              <div class="space-y-2">
                <label class="text-sm font-medium text-gray-700">源语言</label>
                <USelect v-model="lineByLineConfig.sourceLanguage" :options="[{ label: 'English', value: 'English' }]" class="w-full" />
              </div>

              <div class="space-y-2">
                <label class="text-sm font-medium text-gray-700">反射翻译</label>
                <UToggle v-model="lineByLineConfig.reflectTranslate" />
              </div>
            </div>
          </div>
        </div>

        <!-- VideoLingo翻译结果展示 -->
        <div v-if="videoLingoResults.size > 0" class="space-y-4">
          <h2 class="text-lg font-semibold text-gray-900 flex items-center">
            <UIcon name="i-heroicons-language" class="mr-2 text-purple-600" />
            VideoLingo翻译结果
          </h2>

          <div class="bg-purple-50 border border-purple-200 rounded-lg p-4">
            <div class="space-y-4">
              <!-- VideoLingo翻译统计信息 -->
              <div class="grid grid-cols-2 md:grid-cols-4 gap-4 text-sm">
                <div>
                  <span class="text-gray-500">翻译架构：</span>
                  <span class="font-medium text-purple-600">VideoLingo</span>
                </div>
                <div>
                  <span class="text-gray-500">总分块数：</span>
                  <span class="font-medium text-purple-600">{{ getVideoLingoStatistics.totalChunks }}</span>
                </div>
                <div>
                  <span class="text-gray-500">已完成：</span>
                  <span class="font-medium text-purple-600">{{ getVideoLingoStatistics.completedChunks }}</span>
                </div>
                <div>
                  <span class="text-gray-500">翻译模式：</span>
                  <span class="font-medium text-purple-600">
                    {{ getVideoLingoStatistics.reflectTranslateEnabled ? '两步翻译' : '忠实翻译' }}
                  </span>
                </div>
              </div>

              <!-- 翻译进度 -->
              <div v-if="isVideoLingoTranslating" class="space-y-2">
                <div class="flex justify-between text-sm">
                  <span class="text-gray-600">翻译进度</span>
                  <span class="text-purple-600">
                    {{ getVideoLingoStatistics.progress.completed }}/{{ getVideoLingoStatistics.progress.total }}
                  </span>
                </div>
                <div class="w-full bg-gray-200 rounded-full h-2">
                  <div
                    class="bg-purple-600 h-2 rounded-full transition-all duration-300"
                    :style="{
                      width:
                        getVideoLingoStatistics.progress.total > 0
                          ? `${(getVideoLingoStatistics.progress.completed / getVideoLingoStatistics.progress.total) * 100}%`
                          : '0%',
                    }"
                  ></div>
                </div>
              </div>

              <!-- 翻译内容 -->
              <div class="border-t pt-4">
                <h3 class="font-medium text-gray-900 mb-2">翻译结果：</h3>
                <div class="max-h-96 overflow-y-auto space-y-2">
                  <div
                    v-for="(translation, index) in Array.from(videoLingoResults.values())"
                    :key="index"
                    class="bg-white p-3 rounded border"
                  >
                    <p class="text-gray-900">{{ translation }}</p>
                  </div>
                </div>
              </div>
            </div>

            <div class="mt-4 flex gap-2">
              <UButton color="green" @click="handleDownloadVideoLingoTranslation">
                <UIcon name="i-heroicons-arrow-down-tray" class="mr-2" />
                下载VideoLingo翻译
              </UButton>
              <UButton variant="outline" @click="resetVideoLingoTranslation">
                <UIcon name="i-heroicons-trash" class="mr-2" />
                清除VideoLingo结果
              </UButton>
            </div>
          </div>
        </div>

        <!-- VideoLingo翻译配置面板 -->
        <div v-if="recognitionResult" class="space-y-4">
          <h2 class="text-lg font-semibold text-gray-900 flex items-center">
            <UIcon name="i-heroicons-cog-6-tooth" class="mr-2 text-indigo-600" />
            VideoLingo翻译配置
          </h2>

          <div class="bg-indigo-50 border border-indigo-200 rounded-lg p-4 space-y-4">
            <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
              <div class="space-y-2">
                <label class="text-sm font-medium text-gray-700">目标语言</label>
                <USelect
                  v-model="videoLingoConfig_local.targetLanguage"
                  :options="[{ label: '简体中文', value: '简体中文' }]"
                  class="w-full"
                />
              </div>

              <div class="space-y-2">
                <label class="text-sm font-medium text-gray-700">源语言</label>
                <USelect
                  v-model="videoLingoConfig_local.sourceLanguage"
                  :options="[{ label: 'English', value: 'English' }]"
                  class="w-full"
                />
              </div>

              <div class="space-y-2">
                <label class="text-sm font-medium text-gray-700">分块大小（字符）</label>
                <UInput v-model.number="videoLingoConfig_local.chunkSize" type="number" min="50" max="1000" class="w-full" />
                <p class="text-xs text-gray-500">较小值保持更精确的分段对应</p>
              </div>

              <div class="space-y-2">
                <label class="text-sm font-medium text-gray-700">每块最大句子数</label>
                <UInput v-model.number="videoLingoConfig_local.maxSentences" type="number" min="1" max="20" class="w-full" />
                <p class="text-xs text-gray-500">设为1可保持一对一翻译关系</p>
              </div>

              <div class="space-y-2">
                <label class="text-sm font-medium text-gray-700">两步翻译</label>
                <UToggle v-model="videoLingoConfig_local.reflectTranslate" />
                <p class="text-xs text-gray-500">启用表达优化，提升翻译自然度</p>
              </div>

              <div class="space-y-2">
                <label class="text-sm font-medium text-gray-700">并发数量</label>
                <UInput v-model.number="videoLingoConfig_local.maxWorkers" type="number" min="1" max="8" class="w-full" />
                <p class="text-xs text-gray-500">控制并发翻译的线程数</p>
              </div>
            </div>

            <div class="bg-white border border-indigo-200 rounded p-3">
              <h4 class="text-sm font-medium text-gray-800 mb-2">VideoLingo分块翻译机制：</h4>
              <ul class="text-xs text-gray-600 space-y-1">
                <li>• <strong>分块目的：</strong>提供丰富上下文信息，提升翻译质量和连贯性</li>
                <li>• <strong>分块策略：</strong>600字符或10句为一块，在句子边界分割</li>
                <li>• <strong>翻译输出：</strong>严格保持句子级别，一句原文对应一句译文</li>
                <li>• <strong>时间戳对齐：</strong>基于句子边界，实现精确的时间同步</li>
                <li>• <strong>并发处理：</strong>多线程翻译提升效率，结果自动重组</li>
                <li>• <strong>质量保证：</strong>行数匹配验证和相似度匹配确保准确性</li>
              </ul>
            </div>
          </div>
        </div>

        <!-- 摘要提取结果展示 -->
        <div v-if="extractionResult" class="space-y-4">
          <h2 class="text-lg font-semibold text-gray-900 flex items-center">
            <UIcon name="i-heroicons-document-text" class="mr-2 text-purple-600" />
            摘要提取结果
          </h2>

          <div class="bg-purple-50 border border-purple-200 rounded-lg p-4">
            <div class="space-y-4">
              <!-- 主题摘要 -->
              <div>
                <h3 class="font-medium text-gray-900 mb-2">主题摘要：</h3>
                <div class="bg-white p-3 rounded border">
                  <p class="text-gray-900">{{ extractionResult.theme }}</p>
                </div>
              </div>

              <!-- 专业术语 -->
              <div v-if="extractionResult.terms.length > 0">
                <h3 class="font-medium text-gray-900 mb-2">专业术语 ({{ extractionResult.terms.length }} 个)：</h3>
                <div class="max-h-96 overflow-y-auto space-y-2">
                  <div v-for="(term, index) in extractionResult.terms" :key="index" class="bg-white p-3 rounded border">
                    <div class="grid grid-cols-1 md:grid-cols-3 gap-2">
                      <div>
                        <span class="text-sm font-medium text-gray-500">源术语:</span>
                        <p class="text-gray-900 font-mono">{{ term.src }}</p>
                      </div>
                      <div>
                        <span class="text-sm font-medium text-gray-500">翻译:</span>
                        <p class="text-gray-900 font-mono">{{ term.tgt }}</p>
                      </div>
                      <div>
                        <span class="text-sm font-medium text-gray-500">说明:</span>
                        <p class="text-gray-700 text-sm">{{ term.note }}</p>
                      </div>
                    </div>
                  </div>
                </div>
              </div>
            </div>

            <div class="mt-4 flex gap-2">
              <UButton color="green" @click="handleDownloadSummary">
                <UIcon name="i-heroicons-arrow-down-tray" class="mr-2" />
                下载摘要结果
              </UButton>
              <UButton variant="outline" @click="resetSummary">
                <UIcon name="i-heroicons-trash" class="mr-2" />
                清除摘要结果
              </UButton>
              <UButton variant="outline" color="orange" @click="clearSummaryCache">
                <UIcon name="i-heroicons-trash" class="mr-2" />
                清除摘要缓存
              </UButton>
            </div>
          </div>
        </div>

        <!-- 句子分割配置面板 -->
        <div v-if="recognitionResult" class="space-y-4">
          <h2 class="text-lg font-semibold text-gray-900 flex items-center">
            <UIcon name="i-heroicons-scissors" class="mr-2 text-cyan-600" />
            句子分割配置
            <UBadge color="cyan" class="ml-2">单词→句子级时间戳</UBadge>
          </h2>

          <div class="bg-cyan-50 border border-cyan-200 rounded-lg p-4 space-y-4">
            <div class="mb-4 p-3 bg-white border border-cyan-200 rounded">
              <h4 class="text-sm font-medium text-cyan-900 mb-2">功能说明</h4>
              <p class="text-xs text-cyan-700">
                此功能将单词级时间戳智能转换为句子级时间戳，通过分析标点符号、停顿时间和句子长度来提高时间戳对齐的准确性。
              </p>
            </div>

            <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
              <div class="space-y-2">
                <label class="text-sm font-medium text-gray-700">最大句子长度（字符）</label>
                <UInput v-model.number="sentenceSegmentationConfig.maxSentenceLength" type="number" min="50" max="300" class="w-full" />
                <p class="text-xs text-gray-500">超过此长度将强制分割句子</p>
              </div>

              <div class="space-y-2">
                <label class="text-sm font-medium text-gray-700">最大停顿时间（秒）</label>
                <UInput
                  v-model.number="sentenceSegmentationConfig.maxPauseTime"
                  type="number"
                  min="0.1"
                  max="5.0"
                  step="0.1"
                  class="w-full"
                />
                <p class="text-xs text-gray-500">单词间停顿超过此时间将分割</p>
              </div>

              <div class="space-y-3">
                <div class="flex items-center justify-between">
                  <label class="text-sm font-medium text-gray-700">使用标点符号分割</label>
                  <UToggle v-model="sentenceSegmentationConfig.usePunctuation" />
                </div>
                <div class="flex items-center justify-between">
                  <label class="text-sm font-medium text-gray-700">使用时间间隔分割</label>
                  <UToggle v-model="sentenceSegmentationConfig.useTimeGaps" />
                </div>
                <div class="flex items-center justify-between">
                  <label class="text-sm font-medium text-gray-700">使用长度限制分割</label>
                  <UToggle v-model="sentenceSegmentationConfig.useLengthLimit" />
                </div>
              </div>
            </div>

            <div class="bg-white border border-cyan-200 rounded p-3">
              <h4 class="text-sm font-medium text-gray-800 mb-2">分割策略说明：</h4>
              <ul class="text-xs text-gray-600 space-y-1">
                <li>• <strong>标点符号分割：</strong>遇到句号、问号、感叹号时自动分割</li>
                <li>• <strong>时间间隔分割：</strong>根据语音停顿时间判断句子边界</li>
                <li>• <strong>长度限制分割：</strong>防止句子过长影响阅读体验</li>
                <li>• <strong>智能组合：</strong>系统会根据语音特征自动调整分割策略</li>
              </ul>
            </div>
          </div>
        </div>

        <!-- 错误信息 -->
        <div v-if="errorMessage" class="bg-red-50 border border-red-200 rounded-lg p-4">
          <div class="flex items-center">
            <UIcon name="i-heroicons-exclamation-triangle" class="text-red-500 mr-2" />
            <span class="text-red-800">{{ errorMessage }}</span>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

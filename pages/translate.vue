<template>
  <div class="container mx-auto p-6 max-w-4xl">
    <div class="space-y-6">
      <!-- 页面标题 -->
      <div class="text-center">
        <h1 class="text-3xl font-bold text-gray-900 dark:text-white">
          文本翻译
        </h1>
        <p class="mt-2 text-gray-600 dark:text-gray-400">
          基于火山引擎的智能翻译服务
        </p>
      </div>

      <!-- 翻译表单 -->
      <div class="bg-white dark:bg-gray-800 rounded-lg shadow-lg p-6">
        <form class="space-y-6" @submit.prevent="handleTranslate">
          <!-- 语言选择 -->
          <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
            <div>
              <label class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                源语言
              </label>
              <USelect v-model="sourceLanguage" :options="sourceLanguageOptions" placeholder="自动检测" />
            </div>
            <div>
              <label class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                目标语言 *
              </label>
              <USelect v-model="targetLanguage" :options="targetLanguageOptions" placeholder="请选择目标语言" required />
            </div>
          </div>

          <!-- 文本输入 -->
          <div>
            <label class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
              待翻译文本
            </label>
            <UTextarea v-model="inputText" placeholder="请输入要翻译的文本..." :rows="6" required />
          </div>

          <!-- 翻译按钮 -->
          <div class="flex justify-center">
            <UButton type="submit" :loading="isLoading" :disabled="!inputText.trim() || !targetLanguage" size="lg">
              {{ isLoading ? '翻译中...' : '开始翻译' }}
            </UButton>
          </div>
        </form>
      </div>

      <!-- 翻译结果 -->
      <div v-if="translationResult" class="bg-white dark:bg-gray-800 rounded-lg shadow-lg p-6">
        <h2 class="text-xl font-semibold text-gray-900 dark:text-white mb-4">
          翻译结果
        </h2>
        <div class="space-y-4">
          <div v-for="(result, index) in translationResult.translationList" :key="index">
            <div class="bg-gray-50 dark:bg-gray-700 rounded-lg p-4">
              <p class="text-gray-900 dark:text-white whitespace-pre-line">{{ result.translation }}</p>
              <p v-if="result.detectedSourceLanguage" class="text-sm text-gray-500 dark:text-gray-400 mt-2">
                检测到源语言：{{ getLanguageName(result.detectedSourceLanguage) }}
              </p>
            </div>
          </div>
        </div>
      </div>

      <!-- 错误提示 -->
      <div v-if="errorMessage" class="bg-red-50 dark:bg-red-900/20 border border-red-200 dark:border-red-800 rounded-lg p-4">
        <p class="text-red-700 dark:text-red-400">{{ errorMessage }}</p>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import type { TranslateOutputType } from '~/server/trpc/schemas/translate'

// 语言选项类型
interface LanguageOption {
  label: string
  value: string
}

// 语言选项配置
const sourceLanguageOptions: LanguageOption[] = [{ label: '英语', value: 'en' }]

const targetLanguageOptions: LanguageOption[] = [{ label: '中文', value: 'zh' }]

// 响应式数据
const sourceLanguage = ref('en')
const targetLanguage = ref('zh')
const inputText = ref('')
const isLoading = ref(false)
const translationResult = ref<TranslateOutputType | null>(null)
const errorMessage = ref('')

// tRPC客户端
const { $trpc } = useNuxtApp()

// 翻译处理函数
async function handleTranslate() {
  if (!inputText.value.trim() || !targetLanguage.value) {
    return
  }

  isLoading.value = true
  errorMessage.value = ''
  translationResult.value = null

  try {
    const result = await $trpc.translate.text.mutate({
      sourceLanguage: sourceLanguage.value || undefined,
      targetLanguage: targetLanguage.value,
      textList: [inputText.value.trim()],
    })

    translationResult.value = result
  } catch (error) {
    console.error('翻译失败:', error)
    const errorMsg = error instanceof Error ? error.message : '翻译失败，请稍后重试'
    errorMessage.value = errorMsg
  } finally {
    isLoading.value = false
  }
}

// 获取语言名称
function getLanguageName(languageCode: string): string {
  const language = [...sourceLanguageOptions, ...targetLanguageOptions].find((option) => option.value === languageCode)
  return language?.label || languageCode
}

// 页面元数据
useHead({
  title: '文本翻译',
  meta: [{ name: 'description', content: '基于火山引擎的智能文本翻译服务' }],
})
</script>

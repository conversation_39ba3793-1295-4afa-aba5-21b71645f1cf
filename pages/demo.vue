<script setup lang="ts">
import videojs from 'video.js'
import type Player from 'video.js/dist/types/player'
import 'video.js/dist/video-js.css'

// 定义字幕轨道信息接口
interface SubtitleTrackInfo {
  src: string
  lang: string
  label: string
  isBlob?: boolean // 新增：标记 src 是否为 Blob URL
}

// 存储字幕轨道信息，每个对象包含字幕文件的路径、语言和标签
const subtitleTracks = ref<SubtitleTrackInfo[]>([
  { src: '/subtitles/chinese.vtt', lang: 'zh', label: '中文' },
  { src: '/subtitles/subtitles.vtt', lang: 'en', label: 'English' },
])

// 用于跟踪当前显示的字幕轨道的索引
const currentSubtitleIndex = ref(0)
const activeBlobUrl = ref<string | null>(null)
const currentVideoBlobUrl = ref<string | null>(null)

// 用于引用 video 标签的 DOM 元素
const videoPlayer = ref<HTMLVideoElement | null>(null)

// 用于存储 Video.js 播放器实例
let player: Player | null = null

// 异步帮助函数，用于从 VTT 文件路径创建 Blob URL
async function createBlobUrlFromVttPath(vttPath: string): Promise<string> {
  try {
    const response = await fetch(vttPath)
    if (!response.ok) {
      throw new Error(`Failed to fetch VTT file: ${response.status} ${response.statusText}`)
    }
    const vttString = await response.text()
    const blob = new Blob([vttString], { type: 'text/vtt' })
    return URL.createObjectURL(blob)
  } catch (error) {
    console.error('Error creating Blob URL from VTT path:', error)
    throw error // Re-throw the error to be caught by the caller
  }
}

// 组件挂载后执行初始化操作
onMounted(async () => {
  if (videoPlayer.value) {
    const options = {
      controls: false,
      preload: 'auto',
      fluid: true,
      bigPlayButton: false,
    }
    // 初始化 Video.js 播放器
    player = videojs(videoPlayer.value, options, async () => {})
  }
})

// 组件卸载前执行清理操作
onBeforeUnmount(() => {
  if (player && !player.isDisposed()) {
    player.dispose()
    player = null
  }
  // Revoke all isBlob tracks
  subtitleTracks.value.forEach((track) => {
    if (track.isBlob && track.src) {
      URL.revokeObjectURL(track.src)
    }
  })
  // Revoke activeBlobUrl if it's still set
  if (activeBlobUrl.value) {
    URL.revokeObjectURL(activeBlobUrl.value)
    activeBlobUrl.value = null
  }
  // 释放视频 Blob URL
  if (currentVideoBlobUrl.value) {
    URL.revokeObjectURL(currentVideoBlobUrl.value)
    currentVideoBlobUrl.value = null
  }
})

// 处理切换字幕的逻辑
const handleSwitchSubtitle = async () => {
  // 检查播放器实例是否存在且未被销毁
  if (!player || player.isDisposed()) {
    console.error('Player not available for subtitle switch.')
    return
  }

  // 1. 移除当前所有 'subtitles' 类型的轨道
  // 获取当前播放器已加载的远程文本轨道列表
  // eslint-disable-next-line @typescript-eslint/no-explicit-any
  const existingTracks = (player as any).remoteTextTracks()
  if (existingTracks && existingTracks.length > 0) {
    // 从后向前遍历并移除，因为移除元素会改变列表长度和索引
    for (let i = existingTracks.length - 1; i >= 0; i--) {
      const track = existingTracks[i]
      if (track.kind === 'subtitles') {
        // 移除指定的远程文本轨道
        // eslint-disable-next-line @typescript-eslint/no-explicit-any
        ;(player as any).removeRemoteTextTrack(track)
        // console.log('Removed subtitle track:', track.label);
      }
    }
  }

  // 2. 更新到下一个字幕的索引 (循环切换)
  // 计算下一个字幕轨道的索引，实现循环切换
  currentSubtitleIndex.value = (currentSubtitleIndex.value + 1) % subtitleTracks.value.length

  // 3. 添加新的字幕轨道
  try {
    const targetSubtitle = subtitleTracks.value[currentSubtitleIndex.value]
    let newActiveBlobUrl: string

    if (targetSubtitle.isBlob) {
      newActiveBlobUrl = targetSubtitle.src
    } else {
      newActiveBlobUrl = await createBlobUrlFromVttPath(targetSubtitle.src)
    }

    if (activeBlobUrl.value && activeBlobUrl.value !== newActiveBlobUrl) {
      URL.revokeObjectURL(activeBlobUrl.value)
    }
    activeBlobUrl.value = newActiveBlobUrl

    // 向播放器添加新的远程文本字幕轨道
    const newTrackElement = player.addRemoteTextTrack(
      {
        kind: 'subtitles',
        src: activeBlobUrl.value, // 使用 Blob URL
        srclang: targetSubtitle.lang,
        label: targetSubtitle.label,
        default: true, // 设置为默认显示
      },
      false,
    ) // 第二个参数 (insertBeforeCurrent) 通常为 false

    // 监听新字幕轨道加载完成事件，并在加载完成后将其设置为显示状态
    if (newTrackElement) {
      newTrackElement.addEventListener('load', function () {
        // eslint-disable-next-line @typescript-eslint/no-explicit-any
        const track = (newTrackElement as any).track
        if (track) {
          track.mode = 'showing' // 设置字幕为显示状态
          console.log(`Switched subtitle track '${targetSubtitle.label}' mode set to showing.`)
        }
      })
    } else {
      console.error('Failed to get new subtitle track element for:', targetSubtitle.src)
    }
  } catch (error) {
    console.error('Failed to switch subtitle:', error)
  }
}

const handleLoadSubtitle = async () => {
  // 检查播放器实例是否存在且未被销毁
  if (!player || player.isDisposed()) {
    console.error('Player not available for loading subtitle.')
    return
  }

  // 1. 移除当前所有 'subtitles' 类型的轨道
  // eslint-disable-next-line @typescript-eslint/no-explicit-any
  const existingTracks = (player as any).remoteTextTracks()
  if (existingTracks && existingTracks.length > 0) {
    // 从后向前遍历并移除，因为移除元素会改变列表长度和索引
    for (let i = existingTracks.length - 1; i >= 0; i--) {
      const track = existingTracks[i]
      if (track.kind === 'subtitles') {
        // 移除指定的远程文本轨道
        // eslint-disable-next-line @typescript-eslint/no-explicit-any
        ;(player as any).removeRemoteTextTrack(track)
      }
    }
  }

  // 2. 设置加载第一个字幕
  currentSubtitleIndex.value = 0 // 确保加载第一个字幕
  const subtitleToLoad = subtitleTracks.value[currentSubtitleIndex.value]

  if (!subtitleToLoad) {
    console.error('No subtitle found at index 0.')
    return
  }

  // 3. 添加新的字幕轨道
  try {
    let newActiveBlobUrl: string
    if (subtitleToLoad.isBlob) {
      newActiveBlobUrl = subtitleToLoad.src
    } else {
      newActiveBlobUrl = await createBlobUrlFromVttPath(subtitleToLoad.src)
    }

    if (activeBlobUrl.value && activeBlobUrl.value !== newActiveBlobUrl) {
      URL.revokeObjectURL(activeBlobUrl.value)
    }
    activeBlobUrl.value = newActiveBlobUrl

    // 向播放器添加新的远程文本字幕轨道
    const newTrackElement = player.addRemoteTextTrack(
      {
        kind: 'subtitles',
        src: activeBlobUrl.value, // 使用 Blob URL
        srclang: subtitleToLoad.lang,
        label: subtitleToLoad.label,
        default: true, // 设置为默认显示
      },
      false,
    ) // 第二个参数 (insertBeforeCurrent) 通常为 false

    // 监听新字幕轨道加载完成事件，并在加载完成后将其设置为显示状态
    if (newTrackElement) {
      newTrackElement.addEventListener('load', function () {
        // eslint-disable-next-line @typescript-eslint/no-explicit-any
        const track = (newTrackElement as any).track
        if (track) {
          track.mode = 'showing' // 设置字幕为显示状态
          console.log(`Subtitle track '${subtitleToLoad.label}' loaded and mode set to showing.`)
        }
      })
    } else {
      console.error('Failed to get new subtitle track element for:', subtitleToLoad.src)
    }
  } catch (error) {
    console.error('Failed to load subtitle:', error)
  }
}

const handleUploadVideo = async () => {
  const fileInput = document.createElement('input')
  fileInput.type = 'file'
  fileInput.accept = 'video/*,audio/*'
  fileInput.style.display = 'none'

  fileInput.onchange = () => {
    const file = fileInput.files?.[0]
    if (file) {
      if (currentVideoBlobUrl.value) {
        URL.revokeObjectURL(currentVideoBlobUrl.value)
      }
      currentVideoBlobUrl.value = URL.createObjectURL(file)
      if (player && !player.isDisposed()) {
        player.src({ src: currentVideoBlobUrl.value, type: file.type })
        player.load()
        // player.play() // 可选：如果希望上传后自动播放，取消此行注释
      }
    }
    document.body.removeChild(fileInput) // 清理
  }

  document.body.appendChild(fileInput)
  fileInput.click()
}

const handleAddSubtitle = async () => {
  const fileInput = document.createElement('input')
  fileInput.type = 'file'
  fileInput.accept = '.vtt' // 只接受 VTT
  fileInput.style.display = 'none'

  fileInput.onchange = async () => {
    const selectedFile = fileInput.files?.[0]
    if (selectedFile && player && !player.isDisposed()) {
      const userUploadedBlobUrl = URL.createObjectURL(selectedFile)

      if (activeBlobUrl.value) {
        URL.revokeObjectURL(activeBlobUrl.value)
      }
      activeBlobUrl.value = userUploadedBlobUrl

      const newTrack: SubtitleTrackInfo = {
        src: activeBlobUrl.value, // 存储 Blob URL
        lang: 'new', // 可以尝试从文件名推断或让用户选择
        label: selectedFile.name,
        isBlob: true,
      }

      subtitleTracks.value.push(newTrack)
      currentSubtitleIndex.value = subtitleTracks.value.length - 1

      // 移除所有现有的字幕轨道，以确保新添加的成为唯一显示的
      const existingTracks = (player as any).remoteTextTracks()
      if (existingTracks && existingTracks.length > 0) {
        for (let i = existingTracks.length - 1; i >= 0; i--) {
          const track = existingTracks[i]
          if (track.kind === 'subtitles') {
            ;(player as any).removeRemoteTextTrack(track)
          }
        }
      }

      const trackOptions = {
        kind: 'subtitles',
        src: activeBlobUrl.value,
        srclang: newTrack.lang,
        label: newTrack.label,
        default: true,
      }

      console.log('%c AT 🥝 trackOptions 🥝-309', 'font-size:13px; background:#1880ad; color:#5cc4f1;', trackOptions)
      const newTrackElement = player.addRemoteTextTrack(trackOptions, false)

      if (newTrackElement) {
        newTrackElement.addEventListener('load', function () {
          const track = (newTrackElement as any).track
          if (track) {
            track.mode = 'showing'
            console.log('%c AT 🥝 track.mode 🥝-316', 'font-size:13px; background:#fa5368; color:#ff97ac;', track.mode)
            console.log(`Added and activated subtitle track '${newTrack.label}'`)
          }
        })
      } else {
        console.error('Failed to get new subtitle track element for user-added subtitle:', selectedFile.name)
      }
    }
    // 清理
    if (fileInput.parentNode) {
      fileInput.parentNode.removeChild(fileInput)
    }
  }

  document.body.appendChild(fileInput)
  fileInput.click()
}
</script>

<template>
  <div>
    <!-- 这是一组 UI 按钮，第一个按钮用于触发切换字幕的功能，其余按钮用于演示不同颜色 -->
    <div class="flex flex-wrap gap-2">
      <UButton color="red" @click="handleSwitchSubtitle">切换字幕</UButton>
      <UButton color="orange" @click="handleLoadSubtitle">加载字幕</UButton>
      <UButton color="amber" @click="handleUploadVideo">上传视频</UButton>
      <UButton color="yellow" @click="handleAddSubtitle">添加字幕</UButton>
      <UButton color="red">red</UButton>
      <UButton color="orange">orange</UButton>
      <UButton color="amber">amber</UButton>
      <UButton color="yellow">yellow</UButton>
      <UButton color="lime">lime</UButton>
      <UButton color="green">green</UButton>
      <UButton color="emerald">emerald</UButton>
      <UButton color="teal">teal</UButton>
      <UButton color="cyan">cyan</UButton>
      <UButton color="sky">sky</UButton>
      <UButton color="blue">blue</UButton>
      <UButton color="indigo">indigo</UButton>
      <UButton color="violet">violet</UButton>
      <UButton color="purple">purple</UButton>
      <UButton color="fuchsia">fuchsia</UButton>
      <UButton color="pink">pink</UButton>
      <UButton color="rose">rose</UButton>
      <UButton color="primary">primary</UButton>
    </div>

    <!-- Video.js 播放器的主体容器 -->
    <div class="video-container">
      <!-- Video.js 播放器元素 -->
      <video ref="videoPlayer" class="video-js" controls preload="auto">
        <source v-if="currentVideoBlobUrl" :src="currentVideoBlobUrl" type="video/mp4" />
        <!-- 初始字幕轨道将通过 JS 添加 -->
        <p class="vjs-no-js">
          To view this video please enable JavaScript, and consider upgrading to a web browser that
          <a href="https://videojs.com/html5-video-support/" target="_blank">supports HTML5 video</a>
        </p>
      </video>
    </div>
  </div>
</template>

<style scoped>
/* 视频播放器容器的样式 */
.video-container {
  max-width: 800px;
  margin: 20px auto;
}

/* Video.js 播放器本身的样式调整 */
.video-js {
  display: block;
  margin: 0 auto;
}
</style>

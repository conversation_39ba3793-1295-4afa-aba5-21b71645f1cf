import process from 'node:process'
import { codeInspectorPlugin } from 'code-inspector-plugin'

export default defineNuxtConfig({
  compatibilityDate: '2024-11-01',
  devtools: { enabled: false },
  css: ['~/assets/css/main.css'],
  modules: ['@pinia/nuxt', '@nuxt/eslint', '@nuxt/ui', '@pinia-plugin-persistedstate/nuxt'],
  runtimeConfig: {
    volcengineAccessKey: process.env.VOLCENGINE_ACCESS_KEY,
    volcengineSecretKey: process.env.VOLCENGINE_SECRET_KEY,
    arkApiKey: process.env.ARK_API_KEY,
    public: {
      apiBase: process.env.NUXT_PUBLIC_API_BASE ?? '/api',
    },
    admin: {
      userId: process.env.ADMIN_USER_ID ?? 'admin',
      secretKey: process.env.ADMIN_SECRET_KEY ?? '',
      baseUrl: process.env.ADMIN_BASE_URL ?? 'http://localhost:3400',
    },
  },
  devServer: {
    port: 6001,
  },
  build: {
    transpile: ['trpc-nuxt'],
  },
  app: {
    head: {
      link: [{ rel: 'icon', type: 'image/x-icon', href: '/favicon.ico' }],
    },
  },
  vite: {
    plugins: [codeInspectorPlugin({ editor: 'cursor', bundler: 'vite' })],
  },
})

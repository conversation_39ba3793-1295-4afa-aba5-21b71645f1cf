<script setup lang="ts">
const props = defineProps<{
  isOpen: boolean
  title?: string
  subTitle?: string
  width?: string
  height?: string
  showClose?: boolean
}>()

const emit = defineEmits(['close', 'update:isOpen'])

const isVisible = computed({
  get: () => props.isOpen,
  set: (value) => {
    emit('update:isOpen', value)
    if (!value) {
      emit('close')
    }
  },
})

function handleClose() {
  isVisible.value = false
}
</script>

<template>
  <UModal
    v-model="isVisible"
    :ui="{
      width: 'sm:max-w-none w-[70vw] h-[70vh]',
    }"
    @close="handleClose"
  >
    <UCard
      :ui="{
        background: 'bg-gray-900',
        base: 'h-full w-full overflow-y-auto flex flex-col',
        body: {
          base: 'flex-1 flex flex-col overflow-y-auto',
          padding: 'px-4 py-5 sm:p-6',
        },
      }"
    >
      <template v-if="$slots.header || props.title" #header>
        <div class="flex justify-between items-center">
          <!-- 标题 -->
          <h3 class="text-lg font-bold">{{ props.title }}</h3>
          <!-- 副标题 -->
          <div v-if="props.subTitle" class="text-xs text-gray-400 mr-auto ml-2">{{ props.subTitle }}</div>
          <UButton
            v-if="props.showClose !== false"
            variant="link"
            color="gray"
            :padded="false"
            icon="i-heroicons-x-mark"
            @click="handleClose"
          />
        </div>
      </template>

      <!-- 内容区域 -->

      <template #default>
        <slot />
      </template>

      <!-- 底部操作区 -->
      <template v-if="$slots.footer" #footer>
        <div class="flex justify-end gap-2">
          <slot name="footer" />
        </div>
      </template>
    </UCard>
  </UModal>
</template>

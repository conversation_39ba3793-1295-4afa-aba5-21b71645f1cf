<script setup lang="ts">
import { useSubtitleStore } from '~/stores/subtitleStore'
import SubtitleItem from '~/components/SubtitleItem.vue'
import { useMergeHighlight } from '~/composables/ui/useMergeHighlight'
import { usePlayerStore } from '~/stores/playerStore'
import { parseSrtTime } from '~/utils/processing/time/timeParser'
import { findIndex } from 'lodash-es'

const scrollContainerRef = ref<HTMLDivElement | null>(null)

const store = useSubtitleStore()
const playerStore = usePlayerStore()

const { highlightBoxStyle, showHighlightBox, handleMergeButtonHover, handleMergeButtonLeave } = useMergeHighlight(store, scrollContainerRef)

// 滚动相关
const activeSubtitleIndex = ref(-1)
const userLastScrolledTime = ref(0)
const isProgrammaticScroll = ref(false)

// 计算当前活动字幕索引（基于筛选后的列表）
const currentActiveSubtitleIndex = computed(() => {
  const currentTime = playerStore.currentTime
  const filteredSubtitles = store.filteredSubtitles
  if (!filteredSubtitles || filteredSubtitles.length === 0) return -1
  return findIndex(filteredSubtitles, (s) => {
    const start = parseSrtTime(s.startTime)
    const end = parseSrtTime(s.endTime)
    return start !== null && end !== null && currentTime >= start && currentTime < end
  })
})

// 监听活动字幕变化，自动滚动
watch(currentActiveSubtitleIndex, async (newIndex) => {
  activeSubtitleIndex.value = newIndex
  if (newIndex === -1) return
  await nextTick()
  const uuid = store.filteredSubtitles[newIndex]?.uuid
  if (!uuid) return
  const targetElement = document.getElementById(`subtitle-item-${uuid}`)
  if (targetElement && scrollContainerRef.value) {
    isProgrammaticScroll.value = true
    targetElement.scrollIntoView({ behavior: 'smooth', block: 'center' })
    setTimeout(() => {
      isProgrammaticScroll.value = false
    }, 100)
  }
})

// 用户手动滚动处理
function handleUserScroll() {
  if (isProgrammaticScroll.value) return
  userLastScrolledTime.value = Date.now()
}

function handleRowBlankClick(startTime: string) {
  // SRT 时间字符串转秒
  const seconds = parseSrtTime(startTime)
  if (typeof seconds === 'number' && !isNaN(seconds)) {
    playerStore.setSeekRequest(seconds)
  }
}
</script>

<template>
  <div class="relative h-full">
    <!-- 高亮框 -->
    <div
      v-if="showHighlightBox"
      :style="highlightBoxStyle"
      class="highlight-overlay-box absolute box-border pointer-events-none z-10 rounded-xl"
    ></div>

    <!-- 字幕 item 列表，滚动容器 -->
    <div ref="scrollContainerRef" class="relative h-full overflow-y-auto" @scroll.passive="handleUserScroll">
      <SubtitleItem
        v-for="(sub, index) in store.filteredSubtitles"
        :id="`subtitle-item-${sub.uuid}`"
        :key="sub.uuid"
        :subtitle="sub"
        :nextSubtitle="store.filteredSubtitles[index + 1] || null"
        :isLast="index === store.filteredSubtitles.length - 1"
        :isActive="index === activeSubtitleIndex && activeSubtitleIndex !== -1"
        @merge-button-hover="handleMergeButtonHover"
        @merge-button-leave="handleMergeButtonLeave"
        @row-blank-click="handleRowBlankClick"
      />
    </div>
    
    <!-- 筛选状态提示 -->
    <div 
      v-if="store.currentFilter !== 'all' && store.filteredSubtitles.length === 0" 
      class="absolute inset-0 flex items-center justify-center"
    >
      <div class="text-center text-gray-400">
        <div class="text-lg mb-2">没有符合筛选条件的字幕</div>
        <div class="text-sm">当前筛选：{{ store.currentFilter === 'textEmpty' ? '文本为空' : 
          store.currentFilter === 'textOnly' ? '仅有文本' :
          store.currentFilter === 'translationOnly' ? '仅有翻译' :
          store.currentFilter === 'bothEmpty' ? '都为空' :
          store.currentFilter === 'hasContent' ? '有内容' : '全部' }}</div>
      </div>
    </div>
  </div>
</template>

<style scoped>
.highlight-overlay-box {
  border: 2px solid transparent;
  transition: box-shadow 0.3s cubic-bezier(0.4, 0, 0.2, 1), background 0.3s cubic-bezier(0.4, 0, 0.2, 1),
    border 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  background: linear-gradient(90deg, rgba(59, 130, 246, 0.1) 0%, rgba(236, 72, 153, 0.1) 100%);
  box-shadow: 0 4px 24px 0 rgba(59, 130, 246, 0.1);
  border-image: linear-gradient(90deg, #3b82f6, #ec4899) 1;
  border-image-slice: 1;
}
</style>

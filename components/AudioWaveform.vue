<script setup lang="ts">
import { useWaveSurfer } from '~/composables/audio/useWaveSurfer'
import { usePlayerStore } from '~/stores/playerStore'
import { useSubtitleStore } from '~/stores/subtitleStore'
import { useWaveformRegions } from '~/composables/audio/useWaveformRegions'
import { isEmpty } from 'lodash-es'

// Refs
const waveformRef = ref<HTMLDivElement | null>(null)
const timelineRef = ref<HTMLDivElement | null>(null)

// 使用 WaveSurfer composable
const { wavesurfer, status, error, initialize, loadAudio, play, destroy, setPlaybackRate, regionsPluginInstance } = useWaveSurfer()

// 使用自定义的 region 管理 composable
const { registerRegionEvents, unregisterRegionEvents, syncRegions } = useWaveformRegions(regionsPluginInstance)

const playerStore = usePlayerStore()
const subtitleStore = useSubtitleStore()

/**
 * 封装 WaveSurfer 的初始化和音频加载逻辑。
 * @param {File} file - 需要加载的媒体文件。
 * @returns {Promise<void>} - 异步操作，无返回值。
 */
const setupWaveSurfer = async (file: File) => {
  try {
    console.log('正在设置 WaveSurfer...')
    await initialize({
      container: waveformRef.value!,
      timelineContainer: timelineRef.value || undefined,
      onSeek: (time) => playerStore.seek(time),
      onError: (err) => playerStore.setError(err),
      onDurationAvailable: (duration) => playerStore.setDuration(duration),
      onReady: () => {
        console.log('WaveSurfer 初始化完成')
        playerStore.updatePauseStatus()
        if (playerStore.isPlaying) {
          play()
        }
        // 设置初始播放速度
        setPlaybackRate(playerStore.playbackRate)
        // 注册区域事件
        console.log('在 onReady 中注册区域事件')
        registerRegionEvents()
      },
      playbackRate: playerStore.playbackRate,
    })

    console.log('正在加载音频文件...')
    await loadAudio(file)
  } catch (err) {
    console.error('设置 WaveSurfer 时出错:', err)
    playerStore.setError(err as Error)
  }
}

// 在组件挂载后执行初始化
onMounted(async () => {
  await nextTick()
  if (playerStore.mediaFile && waveformRef.value) {
    console.log('组件已挂载，尝试进行初始设置...')
    await setupWaveSurfer(playerStore.mediaFile)
  }
})

// 监听媒体文件变化
watch(
  () => playerStore.mediaFile,
  async (newFile, oldFile) => {
    if (newFile === oldFile) {
      return
    }
    try {
      if (newFile) {
        if (wavesurfer.value) {
          console.log('正在加载新的音频文件...')
          await loadAudio(newFile)
        } else if (waveformRef.value) {
          console.log('正在为新文件执行完整设置...')
          await setupWaveSurfer(newFile)
        }
      } else if (wavesurfer.value) {
        console.log('正在销毁 WaveSurfer 实例...')
        destroy()
      }
    } catch (err) {
      console.error('处理文件更改时出错:', err)
      playerStore.setError(err as Error)
    }
  },
)

// 监听字幕变化并同步 region
watch(
  () => subtitleStore.subtitles,
  () => {
    syncRegions(wavesurfer.value, status.value)
  },
  { deep: true },
)

watch(
  () => status.value,
  (newStatus, oldStatus) => {
    if (newStatus === 'ready' && oldStatus !== 'ready') {
      if (!isEmpty(subtitleStore.subtitles)) {
        console.log('WaveSurfer status 变为 ready，主动调用 syncRegions 将现有字幕同步到波形')
        syncRegions(wavesurfer.value, newStatus)
      }
    }
  },
)

onBeforeUnmount(() => {
  unregisterRegionEvents()
})
</script>

<template>
  <div class="relative h-full w-full">
    <!-- 波形容器 -->
    <div v-show="status === 'ready'" ref="waveformRef" class="w-full bg-gray-800 rounded-lg h-full"></div>

    <!-- 时间轴容器 -->
    <div v-show="status === 'ready'" ref="timelineRef" class="w-full h-5"></div>

    <!-- 状态指示器 -->
    <div v-if="status === 'initializing' || status === 'loading'" class="absolute inset-0 flex items-center justify-center bg-gray-800/50">
      <div class="flex flex-col items-center gap-2">
        <div class="animate-spin rounded-full h-8 w-8 border-4 border-primary-500 border-t-transparent"></div>
        <div class="text-gray-600">
          {{ status === 'initializing' ? '初始化中...' : '加载音频...' }}
        </div>
      </div>
    </div>

    <!-- 错误提示 -->
    <div v-if="status === 'error' && error" class="absolute inset-0 flex items-center justify-center bg-gray-900/80">
      <div class="text-red-400 p-4 text-center">
        <div class="font-bold">加载失败</div>
        <div class="mt-2">{{ error.message }}</div>
      </div>
    </div>
  </div>
</template>

<style scoped></style>

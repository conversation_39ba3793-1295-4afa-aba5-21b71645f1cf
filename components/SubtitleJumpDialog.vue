<script setup lang="ts">
import type { Subtitle } from '~/types/subtitle'
import BaseDialog from './ui/BaseDialog.vue'
import { parseTimeStringToSeconds, parseNormalizedTimeToSeconds } from '~/utils/processing/time/timeParser'
import { normalizeVttTime } from '~/utils/processing/time/timeFormatter'
import { filter, isEmpty, isInteger, debounce, trim } from 'lodash-es'

interface Props {
  isOpen: boolean
  subtitles: Subtitle[]
}

interface Emits {
  (e: 'update:isOpen', value: boolean): void
  (e: 'jump-to', subtitle: Subtitle): void
}

const props = defineProps<Props>()
const emit = defineEmits<Emits>()

const indexInput = ref('')
const textInput = ref('')
const timeInput = ref('')
const searchType = ref<'index' | 'text' | 'time'>('index')
const textMode = ref<'all' | 'text' | 'translation'>('all')
const jumpResults = ref<Subtitle[]>([])
const searchStatus = ref<'idle' | 'pending' | 'success' | 'error'>('idle')
const indexError = ref('')
const indexIsValid = ref(true)
const timeError = ref('')
const timeIsValid = ref(true)

watch(searchType, () => {
  indexInput.value = ''
  textInput.value = ''
  timeInput.value = ''
  jumpResults.value = []
  indexError.value = ''
  indexIsValid.value = true
  timeError.value = ''
  timeIsValid.value = true
})

function validateIndex(value: string): { isValid: boolean; error: string } {
  const trimmedValue = trim(value)

  const n = Number(trimmedValue)
  if (isNaN(n)) {
    return { isValid: false, error: '请输入有效数字' }
  }

  if (!isInteger(n)) {
    return { isValid: false, error: '序号必须是整数' }
  }

  if (n < 1 || n > props.subtitles.length) {
    return { isValid: false, error: `序号范围：1 ~ ${props.subtitles.length}` }
  }

  return { isValid: true, error: '' }
}

// 验证时间输入格式
function validateTimeInput(value: string): { isValid: boolean; seconds: number | null; error: string } {
  const trimmedValue = trim(value)

  // 空值处理
  if (!trimmedValue) {
    return { isValid: false, seconds: null, error: '' }
  }

  // 尝试解析为纯数字（秒数）
  const numberValue = Number(trimmedValue)
  if (!isNaN(numberValue) && isFinite(numberValue) && numberValue >= 0) {
    return { isValid: true, seconds: numberValue, error: '' }
  }

  const normalized = normalizeVttTime(trimmedValue, ',') // 先标准化

  // 尝试解析为时间格式字符串
  const seconds = parseNormalizedTimeToSeconds(normalized)
  if (seconds !== null && seconds >= 0) {
    return { isValid: true, seconds, error: '' }
  }

  return {
    isValid: false,
    seconds: null,
    error: `请输入有效的时间格式，支持格式：秒数（如 12.5）、时分秒毫秒 HH:MM:SS.mmm（可简化如 M:S 1:23 或 M:SS.s 1:23.5）`,
  }
}

const throttledValidateIndex = debounce((e: Event) => {
  if (searchStatus.value !== 'idle') {
    searchStatus.value = 'idle'
  }

  const num = (e.target as HTMLInputElement).value
  const result = validateIndex(num)
  indexIsValid.value = result.isValid
  indexError.value = result.error

  // 初始化
  if (num === '') {
    jumpResults.value = []
    searchStatus.value = 'idle'
  }
}, 300)

const canJump = () => {
  if (searchType.value === 'index') {
    const result = validateIndex(indexInput.value)
    return result.isValid
  }
  if (searchType.value === 'time') {
    const result = validateTimeInput(timeInput.value)
    return result.isValid
  }
  if (searchType.value === 'text') {
    return !!textInput.value.trim()
  }

  return false
}

function handleJump() {
  let input = ''
  if (searchType.value === 'index') {
    input = trim(indexInput.value)
  } else if (searchType.value === 'text') {
    input = trim(textInput.value)
  } else if (searchType.value === 'time') {
    input = trim(timeInput.value)
  }

  jumpResults.value = []
  if (!input) return
  if (!canJump()) return

  searchStatus.value = 'pending'
  if (searchType.value === 'index') {
    const idx = Number(input) - 1
    console.log('[跳转调试] 输入值:', input, '计算下标:', idx, '字幕总数:', props.subtitles.length)
    if (isInteger(idx + 1) && idx >= 0 && idx < props.subtitles.length) {
      console.log('[跳转调试] 命中字幕:', props.subtitles[idx])
      jumpResults.value = [props.subtitles[idx]]
    } else {
      console.log('[跳转调试] 未命中，jumpResults 为空')
      jumpResults.value = []
    }
    searchStatus.value = 'success'
    return
  }
  if (searchType.value === 'time') {
    const result = validateTimeInput(timeInput.value)
    if (result.isValid && result.seconds !== null) {
      jumpResults.value = filter(props.subtitles, (sub) => {
        const start = parseTimeStringToSeconds(sub.startTime)
        const end = parseTimeStringToSeconds(sub.endTime)
        return start <= result.seconds! && result.seconds! <= end
      })
    }
    searchStatus.value = 'success'
    return
  }
  if (searchType.value === 'text') {
    const keyword = textInput.value.toLowerCase()
    if (textMode.value === 'all') {
      jumpResults.value = props.subtitles.filter(
        (sub) =>
          (sub.text && sub.text.toLowerCase().includes(keyword)) ||
          (sub.translationText && sub.translationText.toLowerCase().includes(keyword)),
      )
    } else if (textMode.value === 'text') {
      jumpResults.value = props.subtitles.filter((sub) => sub.text && sub.text.toLowerCase().includes(keyword))
    } else if (textMode.value === 'translation') {
      jumpResults.value = props.subtitles.filter((sub) => sub.translationText && sub.translationText.toLowerCase().includes(keyword))
    }
    searchStatus.value = 'success'
    return
  }
}

function jumpToSubtitle(sub: Subtitle) {
  emit('jump-to', sub)
  emit('update:isOpen', false)
  indexInput.value = ''
  textInput.value = ''
  timeInput.value = ''
  jumpResults.value = []
}

function onClearSearch() {
  indexInput.value = ''
  textInput.value = ''
  timeInput.value = ''
  jumpResults.value = []
  indexError.value = ''
  indexIsValid.value = true
  timeError.value = ''
  timeIsValid.value = true
}

function handleClose() {
  emit('update:isOpen', false)
}

const throttledValidateText = debounce(() => {
  if (searchStatus.value === 'idle') return
  searchStatus.value = 'idle'
}, 300)

const throttledValidateTime = debounce((e: Event) => {
  if (searchStatus.value !== 'idle') {
    searchStatus.value = 'idle'
  }

  const value = (e.target as HTMLInputElement).value
  const result = validateTimeInput(value)
  timeIsValid.value = result.isValid
  timeError.value = result.error

  // 初始化
  if (value === '') {
    jumpResults.value = []
    searchStatus.value = 'idle'
  }
}, 300)
</script>

<template>
  <BaseDialog
    :isOpen="props.isOpen"
    title="跳转到字幕"
    :showClose="true"
    @close="handleClose"
    @update:isOpen="emit('update:isOpen', $event)"
  >
    <div class="flex flex-col gap-4 py-2">
      <div class="flex gap-4 items-center">
        <label class="flex items-center gap-1"> <input v-model="searchType" type="radio" value="index" /> 序号 </label>
        <label class="flex items-center gap-1"> <input v-model="searchType" type="radio" value="text" /> 字幕 </label>
        <label class="flex items-center gap-1"> <input v-model="searchType" type="radio" value="time" /> 时间戳 </label>
      </div>

      <!-- 序号输入 -->
      <UInput
        v-show="searchType === 'index'"
        v-model="indexInput"
        :placeholder="`输入字幕序号（1-${props.subtitles.length}）`"
        type="text"
        :min="1"
        :max="props.subtitles.length"
        step="1"
        :loading="searchStatus === 'pending'"
        :ui="{ icon: { trailing: { pointer: '' } } }"
        @input="throttledValidateIndex"
        @keyup.enter="handleJump"
      >
        <template #trailing>
          <UButton
            v-show="indexInput !== ''"
            color="gray"
            variant="link"
            icon="i-heroicons-x-mark-20-solid"
            :padded="false"
            @click.stop="onClearSearch"
          />
        </template>
      </UInput>

      <!-- 序号错误提示 -->
      <div v-if="searchType === 'index' && !indexIsValid && indexError && !!indexInput" class="text-red-500 text-xs mt-1">
        {{ indexError }}
      </div>

      <!-- 文本输入 -->
      <UInput
        v-show="searchType === 'text'"
        v-model="textInput"
        placeholder="输入字幕片段"
        type="text"
        :loading="searchStatus === 'pending'"
        :ui="{ icon: { trailing: { pointer: '' } } }"
        @input="throttledValidateText"
        @keyup.enter="handleJump"
      >
        <template #trailing>
          <UButton
            v-show="textInput !== ''"
            color="gray"
            variant="link"
            icon="i-heroicons-x-mark-20-solid"
            :padded="false"
            @click.stop="onClearSearch"
          />
        </template>
      </UInput>

      <!-- 时间输入 -->
      <UInput
        v-show="searchType === 'time'"
        v-model="timeInput"
        placeholder="输入时间（如：12.5 或 1:23.5）"
        type="text"
        :loading="searchStatus === 'pending'"
        :ui="{ icon: { trailing: { pointer: '' } } }"
        @input="throttledValidateTime"
        @keyup.enter="handleJump"
      >
        <template #trailing>
          <UButton
            v-show="timeInput !== ''"
            color="gray"
            variant="link"
            icon="i-heroicons-x-mark-20-solid"
            :padded="false"
            @click.stop="onClearSearch"
          />
        </template>
      </UInput>

      <!-- 时间错误提示 -->
      <div v-if="searchType === 'time' && !timeIsValid && timeError && !!timeInput" class="text-red-500 text-xs mt-1">
        {{ timeError }}
      </div>

      <div v-if="searchType === 'time'" class="text-xs text-gray-400">支持格式：秒数（如 12.5）、时分秒（如 1:23 或 1:23.5）</div>

      <div v-if="searchType === 'text'" class="flex gap-4 items-center mt-1">
        <label class="flex items-center gap-1"> <input v-model="textMode" type="radio" value="all" /> 全部 </label>
        <label class="flex items-center gap-1"> <input v-model="textMode" type="radio" value="text" /> 原文 </label>
        <label class="flex items-center gap-1"> <input v-model="textMode" type="radio" value="translation" /> 翻译 </label>
      </div>
    </div>
    <div class="mt-4">
      <template v-if="jumpResults.length > 1">
        <div class="text-xs text-gray-400 mb-2">共找到 {{ jumpResults.length }} 条匹配字幕，请选择：</div>
        <div class="max-h-48 overflow-y-auto flex flex-col gap-2">
          <div
            v-for="sub in jumpResults"
            :key="sub.uuid"
            class="p-2 rounded cursor-pointer hover:bg-primary-900/30 border border-gray-700 flex flex-col gap-1"
            @click="jumpToSubtitle(sub)"
          >
            <div class="flex gap-2 text-sm">
              <span class="text-gray-400">#{{ sub.id }}</span>
              <span class="text-gray-400">{{ sub.startTime }} - {{ sub.endTime }}</span>
            </div>
            <div class="text-white">{{ sub.text }}</div>
            <div v-if="sub.translationText" class="text-gray-400 text-xs">{{ sub.translationText }}</div>
          </div>
        </div>
      </template>
      <template v-else-if="jumpResults.length === 1">
        <div class="text-xs text-gray-400 mb-2">已定位到唯一字幕：</div>
        <div
          class="p-2 rounded bg-primary-900/20 border border-primary-700 flex flex-col gap-1 cursor-pointer"
          @click="jumpToSubtitle(jumpResults[0])"
        >
          <div class="flex gap-2 text-sm">
            <span class="text-gray-400">#{{ jumpResults[0].id }}</span>
            <span class="text-gray-400">{{ jumpResults[0].startTime }} - {{ jumpResults[0].endTime }}</span>
          </div>
          <div class="text-white">{{ jumpResults[0].text }}</div>
          <div v-if="jumpResults[0].translationText" class="text-gray-400 text-xs">{{ jumpResults[0].translationText }}</div>
        </div>
      </template>

      <template
        v-else-if="
          ((searchType === 'index' && !!indexInput) || (searchType === 'text' && !!textInput) || (searchType === 'time' && !!timeInput)) &&
          isEmpty(jumpResults) &&
          searchStatus === 'success'
        "
      >
        <div class="text-sm text-red-500">未找到对应字幕</div>
      </template>
    </div>
  </BaseDialog>
</template>

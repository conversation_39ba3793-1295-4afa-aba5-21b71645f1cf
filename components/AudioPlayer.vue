<script setup lang="ts">
import { usePlayerStore, PLAYBACK_RATES, PlayerStatusEnum } from '~/stores/playerStore'
import AudioWaveform from '~/components/AudioWaveform.vue'
import { formatTimeToString } from '~/utils/processing/time/timeFormatter'

const playerStore = usePlayerStore()
const audioFileInput = ref<HTMLInputElement | null>(null)

// 文件选择处理
const handleFileSelect = (event: Event) => {
  const file = (event.target as HTMLInputElement).files?.[0]
  if (!file) return

  if (file) {
    playerStore.setMediaFile(file)
  }

  // 清空 input 以便连续上传同一文件
  ;(event.target as HTMLInputElement).value = ''
}

// 计算进度百分比
const progress = computed(() => {
  if (playerStore.duration === 0) return 0
  return (playerStore.currentTime / playerStore.duration) * 100
})

// 处理进度条点击
const handleProgressClick = (event: MouseEvent) => {
  const progressBar = event.currentTarget as HTMLDivElement
  const rect = progressBar.getBoundingClientRect()
  const percentage = (event.clientX - rect.left) / rect.width
  const newTime = percentage * playerStore.duration
  playerStore.setSeekRequest(newTime)

  // TODO: 更新波形进度 wavesurfer.value.setTime
}

const cyclePlaybackRate = () => {
  // 获取当前速率在PLAYBACK_RATES中的索引
  const idx = PLAYBACK_RATES.indexOf(playerStore.playbackRate)
  const nextIdx = (idx + 1) % PLAYBACK_RATES.length
  playerStore.playbackRate = PLAYBACK_RATES[nextIdx]
}

// 悬浮时间
const hoverTime = ref<number | null>(null)
const hoverX = ref<number | null>(null)

// 处理进度条鼠标移动
const handleProgressMouseMove = (event: MouseEvent) => {
  const progressBar = event.currentTarget as HTMLDivElement
  const rect = progressBar.getBoundingClientRect()
  const x = event.clientX - rect.left
  const percentage = Math.min(Math.max(x / rect.width, 0), 1)
  hoverTime.value = percentage * playerStore.duration
  hoverX.value = x
}

// 处理进度条鼠标离开
const handleProgressMouseLeave = () => {
  hoverTime.value = null
  hoverX.value = null
}

const triggerAudioFileInput = () => {
  audioFileInput.value?.click()
}
</script>

<template>
  <div class="flex flex-col gap-3 h-52 relative">
    <input ref="audioFileInput" type="file" accept="audio/*,video/*" class="hidden" @change="handleFileSelect" />
    <div v-if="!playerStore.mediaFile" class="absolute left-1/2 -translate-x-1/2 top-1/2 -translate-y-1/2 flex flex-col items-center">
      <p class="text-xs text-gray-500 select-none">支持 WAV、M4A、MP3、MP4、MOV、OGG，文件小于 150MB</p>
      <p class="text-xs text-gray-500 select-none">音频时长需小于一小时，推荐使用 WAV 格式以获得最佳兼容性</p>
      <p class="text-xs text-gray-500 select-none">背景音干净、人声清晰的音视频内容，效果更佳</p>
      <div class="mt-4 flex items-center gap-4 h-10">
        <UButton color="indigo" icon="i-heroicons-film" @click="triggerAudioFileInput">导入媒体</UButton>
      </div>
    </div>
    <div v-if="playerStore.mediaFile" class="flex items-center gap-4 w-full h-9 bg-[#6366f142]">
      <!-- 播放/暂停图标按钮 -->
      <UButton
        class="ml-3 rounded-full h-7 w-9"
        :disabled="playerStore.status === PlayerStatusEnum.ERROR || !playerStore.mediaFile"
        @click="playerStore.togglePlay"
      >
        <UIcon :name="playerStore.isPlaying ? 'heroicons:pause-solid' : 'heroicons:play-solid'" class="w-6 h-6 text-gray-200" />
      </UButton>

      <!-- 播放速度切换 -->
      <button
        class="text-sm font-medium w-14 text-gray-200 px-2 py-1 rounded hover:bg-gray-700 focus:outline-none"
        @click="cyclePlaybackRate"
      >
        <!-- 显示当前播放速率，点击切换 -->
        {{ playerStore.playbackRate }}x
      </button>

      <!-- 进度条 -->
      <div class="flex-grow mx-2">
        <div
          class="relative h-3 bg-gray-800 rounded-full cursor-pointer"
          @click="handleProgressClick"
          @mousemove="handleProgressMouseMove"
          @mouseleave="handleProgressMouseLeave"
        >
          <!-- 已播放进度条 -->
          <div
            class="absolute top-0 left-0 h-3 bg-primary-500 rounded-full transition-all duration-100"
            :style="{ width: `${progress}%` }"
          ></div>
          <!-- 悬浮时显示对应时间提示 -->
          <div
            v-if="hoverTime !== null && hoverX !== null"
            class="absolute -top-7 z-20 px-2 py-1 rounded bg-black/80 text-xs text-white pointer-events-none select-none whitespace-nowrap"
            :style="{ left: `${hoverX}px`, transform: 'translateX(-50%)' }"
          >
            {{ formatTimeToString(hoverTime) }}
          </div>
        </div>
      </div>

      <!-- 时间显示，垂直堆叠，显示当前时间和总时长 -->
      <div class="flex flex-col items-start text-xs text-gray-400 min-w-[110px]">
        <span>{{ formatTimeToString(playerStore.currentTime) }} /</span>
        <span>{{ formatTimeToString(playerStore.duration) }}</span>
      </div>
    </div>

    <div class="h-28 relative">
      <!-- 波形显示，只有选择音频文件后才显示波形组件 -->
      <AudioWaveform v-if="playerStore.mediaFile" />

      <!-- 错误提示 -->
      <div v-if="playerStore.error" class="text-red-600 absolute inset-0 flex items-center justify-center bg-black/60 z-10">
        {{ playerStore.error.message }}
      </div>
    </div>
  </div>
</template>

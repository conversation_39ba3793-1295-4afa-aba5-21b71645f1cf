<script setup lang="ts">
import type { ChunkItem, ChunkContext } from '~/types/chunking'

interface Props {
  chunks: ChunkItem[]
  contexts: Map<number, ChunkContext>
  statistics: {
    totalChunks: number
    averageCharCount: number
    averageSentenceCount: number
    maxCharCount: number
    minCharCount: number
  }
  translationResults?: Map<number, string>
  translationQuality?: {
    averageSimilarity: number
    minSimilarity: number
    lowQualityCount: number
    failedCount: number
  }
  isTranslating?: boolean
}

const props = defineProps<Props>()

// 控制上下文信息的展开状态
const expandedContexts = ref<Set<number>>(new Set())
// 控制翻译结果的展开状态
const expandedTranslations = ref<Set<number>>(new Set())

/**
 * 切换上下文信息展开状态
 */
const toggleContext = (chunkIndex: number) => {
  const newExpanded = new Set(expandedContexts.value)
  if (newExpanded.has(chunkIndex)) {
    newExpanded.delete(chunkIndex)
  } else {
    newExpanded.add(chunkIndex)
  }
  expandedContexts.value = newExpanded
}

/**
 * 切换翻译结果展开状态
 */
const toggleTranslation = (chunkIndex: number) => {
  const newExpanded = new Set(expandedTranslations.value)
  if (newExpanded.has(chunkIndex)) {
    newExpanded.delete(chunkIndex)
  } else {
    newExpanded.add(chunkIndex)
  }
  expandedTranslations.value = newExpanded
}

/**
 * 复制分块内容到剪贴板
 */
const copyChunkContent = async (content: string) => {
  try {
    await navigator.clipboard.writeText(content)
    // 这里可以添加成功提示
  } catch (err) {
    console.error('复制失败:', err)
  }
}

/**
 * 复制翻译结果到剪贴板
 */
const copyTranslation = async (translation: string) => {
  try {
    await navigator.clipboard.writeText(translation)
    // 这里可以添加成功提示
  } catch (err) {
    console.error('复制翻译失败:', err)
  }
}

/**
 * 获取质量徽章颜色
 */
const getQualityBadgeColor = (chunkIndex: number) => {
  if (!props.translationQuality) return 'gray'

  const score = getQualityScore(chunkIndex)
  if (score >= 95) return 'green'
  if (score >= 90) return 'yellow'
  return 'red'
}

/**
 * 获取质量分数
 */
const getQualityScore = (_chunkIndex: number) => {
  if (!props.translationQuality) return 0

  // 这里简化处理，实际应该从翻译结果中获取具体的相似度
  return Math.round(props.translationQuality.averageSimilarity * 100)
}
</script>

<template>
  <div class="space-y-6">
    <!-- 统计信息 -->
    <div class="bg-blue-50 border border-blue-200 rounded-lg p-4">
      <h3 class="font-medium text-gray-900 mb-3">分块统计</h3>
      <div class="grid grid-cols-2 md:grid-cols-5 gap-4 text-sm text-blue-400">
        <div>
          <span class="text-gray-500">总分块数：</span>
          <span class="font-medium">{{ statistics.totalChunks }}</span>
        </div>
        <div>
          <span class="text-gray-500">平均字符数：</span>
          <span class="font-medium">{{ statistics.averageCharCount }}</span>
        </div>
        <div>
          <span class="text-gray-500">平均句子数：</span>
          <span class="font-medium">{{ statistics.averageSentenceCount }}</span>
        </div>
        <div>
          <span class="text-gray-500">最大字符数：</span>
          <span class="font-medium">{{ statistics.maxCharCount }}</span>
        </div>
        <div>
          <span class="text-gray-500">最小字符数：</span>
          <span class="font-medium">{{ statistics.minCharCount }}</span>
        </div>
      </div>
    </div>

    <!-- 分块列表 -->
    <div class="space-y-4">
      <h3 class="font-medium text-gray-900">分块内容</h3>

      <div class="space-y-3">
        <div
          v-for="chunk in chunks"
          :key="chunk.index"
          class="bg-white border border-gray-200 rounded-lg p-4 hover:shadow-sm transition-shadow"
        >
          <!-- 分块头部信息 -->
          <div class="flex items-center justify-between mb-3">
            <div class="flex items-center space-x-4">
              <UBadge color="blue" variant="soft">块 {{ chunk.index + 1 }}</UBadge>
              <span class="text-sm text-gray-500"> {{ chunk.charCount }} 字符 | {{ chunk.sentenceCount }} 句 </span>
              <!-- 翻译质量指标 -->
              <UBadge
                v-if="translationQuality && translationResults?.get(chunk.index)"
                :color="getQualityBadgeColor(chunk.index)"
                variant="soft"
                size="xs"
              >
                质量 {{ getQualityScore(chunk.index) }}%
              </UBadge>
            </div>
            <div class="flex items-center space-x-2">
              <UButton size="xs" variant="outline" @click="copyChunkContent(chunk.content)">
                <UIcon name="i-heroicons-clipboard" class="mr-1" />
                复制
              </UButton>
              <UButton
                v-if="contexts.get(chunk.index)?.previousContent?.length || contexts.get(chunk.index)?.afterContent?.length"
                size="xs"
                variant="outline"
                @click="toggleContext(chunk.index)"
              >
                <UIcon :name="expandedContexts.has(chunk.index) ? 'i-heroicons-chevron-up' : 'i-heroicons-chevron-down'" class="mr-1" />
                上下文
              </UButton>
              <UButton
                v-if="translationResults?.get(chunk.index)"
                size="xs"
                variant="outline"
                color="green"
                @click="toggleTranslation(chunk.index)"
              >
                <UIcon :name="expandedTranslations.has(chunk.index) ? 'i-heroicons-chevron-up' : 'i-heroicons-chevron-down'" class="mr-1" />
                翻译
              </UButton>
              <UButton
                v-if="translationResults?.get(chunk.index)"
                size="xs"
                variant="outline"
                color="green"
                @click="copyTranslation(translationResults.get(chunk.index) || '')"
              >
                <UIcon name="i-heroicons-clipboard" class="mr-1" />
                复制译文
              </UButton>
              <UBadge v-if="isTranslating" color="orange" variant="soft">
                翻译中...
              </UBadge>
            </div>
          </div>

          <!-- 上下文信息（前文） -->
          <div
            v-if="expandedContexts.has(chunk.index) && contexts.get(chunk.index)?.previousContent?.length"
            class="mb-3 p-3 bg-gray-50 rounded border-l-4 border-gray-300"
          >
            <div class="text-xs text-gray-500 mb-1">前文上下文：</div>
            <div class="text-sm text-gray-600 space-y-1">
              <div v-for="(line, idx) in contexts.get(chunk.index)?.previousContent" :key="idx" class="opacity-75">
                {{ line }}
              </div>
            </div>
          </div>

          <!-- 分块主要内容 -->
          <div class="bg-gray-50 rounded p-3 font-mono text-sm whitespace-pre-wrap text-green-600">
            {{ chunk.content }}
          </div>

          <!-- 翻译结果展示 -->
          <div
            v-if="translationResults?.get(chunk.index) && expandedTranslations.has(chunk.index)"
            class="mt-3 p-3 bg-green-50 rounded border-l-4 border-green-400"
          >
            <div class="text-xs text-gray-500 mb-1">翻译结果：</div>
            <div class="text-sm text-gray-800 font-mono whitespace-pre-wrap">
              {{ translationResults.get(chunk.index) }}
            </div>
          </div>

          <!-- 上下文信息（后文） -->
          <div
            v-if="expandedContexts.has(chunk.index) && contexts.get(chunk.index)?.afterContent?.length"
            class="mt-3 p-3 bg-gray-50 rounded border-l-4 border-gray-300"
          >
            <div class="text-xs text-gray-500 mb-1">后文上下文：</div>
            <div class="text-sm text-gray-600 space-y-1">
              <div v-for="(line, idx) in contexts.get(chunk.index)?.afterContent" :key="idx" class="opacity-75">
                {{ line }}
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

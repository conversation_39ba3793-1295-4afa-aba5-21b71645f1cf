<script setup lang="ts">
import { debounce } from 'lodash-es'
import { validateSrtTimeFormat } from '~/utils/processing/time/timeValidator'
import { parseSrtTime } from '~/utils/processing/time/timeParser'

type OrderValidType = boolean | 'prev' | 'next'
interface Props {
  modelValue: string
  label?: string
  isOrderValid?: OrderValidType
  maxSeconds?: number
}

const props = withDefaults(defineProps<Props>(), {
  label: '00:00:00,000',
  isOrderValid: true,
  maxSeconds: undefined,
})

const emit = defineEmits(['update:modelValue'])

const inputValue = ref('')
const isFormatValid = ref(true)
const inputRef = ref<HTMLInputElement | null>(null)

// 结束时间不允许超过媒体总时长
const isMaxValid = computed(() => {
  if (props.maxSeconds === undefined) return true
  const seconds = parseSrtTime(inputValue.value)
  if (seconds === null) return true // 格式无效时不做最大值校验
  return seconds <= props.maxSeconds
})

const isComponentValid = computed(() => {
  if (typeof props.isOrderValid === 'boolean') {
    return isFormatValid.value && props.isOrderValid && isMaxValid.value
  }
  // 'prev' 或 'next' 视为无效
  return isFormatValid.value && props.isOrderValid !== 'prev' && props.isOrderValid !== 'next' && isMaxValid.value
})

watch(
  () => props.modelValue,
  (val) => {
    inputValue.value = val
    isFormatValid.value = validateSrtTimeFormat(val)
  },
  { immediate: true },
)

function onInputHandler(event: Event) {
  let newValue = (event.target as HTMLInputElement).value
  // 将中文的句号、点号、逗号替换为英文的句号、点号、逗号
  newValue = newValue.replace(/[。.,，]/g, ',')
  inputValue.value = newValue
  // isFormatValid.value = validateSrtTimeFormat(newValue)
  isFormatValid.value = validateSrtTimeFormat(newValue)
  emit('update:modelValue', newValue)
}

const onInput = debounce(onInputHandler, 500)
</script>
<template>
  <div>
    <UInput
      ref="inputRef"
      :model-value="inputValue"
      :placeholder="props.label"
      color="sky"
      :ui="{
        variant: {
          outline: isComponentValid
            ? 'shadow-sm bg-transparent text-gray-900 dark:text-white ring-1 ring-inset ring-{color}-500 dark:ring-{color}-400 focus:ring-2 focus:ring-{color}-500 dark:focus:ring-{color}-400'
            : 'shadow-sm bg-transparent text-gray-900 dark:text-white ring-2 ring-inset ring-red-500 dark:ring-red-400 focus:ring-2 focus:ring-red-500 dark:focus:ring-red-400',
        },
      }"
      style="color: #e5e7ebe3;"
      maxlength="12"
      @input="onInput"
    />
    <div v-if="!isComponentValid" class="text-xs text-red-500 mt-1">
      <span v-if="!isFormatValid" class="whitespace-nowrap">
        HH:MM:SS,mmm
      </span>
      <span v-else-if="props.isOrderValid === 'prev'">不能早于上一行结束时间</span>
      <span v-else-if="props.isOrderValid === 'next'">不能晚于下一行开始时间</span>
      <span v-else-if="props.isOrderValid === false">开始不能晚于结束</span>
      <span v-else-if="!isMaxValid">不能超过音频总时长</span>
    </div>
  </div>
</template>

<script setup lang="ts">
import { useLineByLineTranslation } from '~/composables/translation/useLineByLineTranslation'

const {
  isTranslating,
  translationError,
  faithfulResults,
  expressiveResults,
  finalResults,
  config,
  getTranslationStatistics,
  translateLines,
  resetTranslation,
  updateConfig,
} = useLineByLineTranslation()

// 测试数据
const testText = ref(`这是第一行测试文本。
这是第二行测试文本。
这是第三行测试文本。`)

const translationContext = ref({
  previousContent: [],
  afterContent: [],
  summary: '这是一个测试文本，用于验证逐行翻译功能。',
  thingsToNote: '请注意保持翻译的准确性和流畅性。',
})

// 测试翻译
const handleTestTranslation = async () => {
  try {
    await translateLines(testText.value, translationContext.value, 0)
  } catch (error) {
    console.error('测试翻译失败:', error)
  }
}

// 更新配置
const handleUpdateConfig = () => {
  updateConfig({
    targetLanguage: 'English',
    sourceLanguage: '中文',
    reflectTranslate: true,
  })
}
</script>

<template>
  <div class="max-w-4xl mx-auto p-6 space-y-6">
    <div class="bg-white rounded-lg shadow-sm border border-gray-200 p-6">
      <h2 class="text-xl font-bold text-gray-900 mb-4">逐行翻译功能测试</h2>

      <!-- 配置区域 -->
      <div class="space-y-4 mb-6">
        <h3 class="text-lg font-semibold text-gray-800">翻译配置</h3>
        <div class="grid grid-cols-1 md:grid-cols-3 gap-4">
          <div>
            <label class="block text-sm font-medium text-gray-700 mb-2">目标语言</label>
            <select v-model="config.targetLanguage" class="w-full p-2 border border-gray-300 rounded-md">
              <option value="简体中文">简体中文</option>
              <option value="English">English</option>
              <option value="日本語">日本語</option>
            </select>
          </div>
          <div>
            <label class="block text-sm font-medium text-gray-700 mb-2">源语言</label>
            <select v-model="config.sourceLanguage" class="w-full p-2 border border-gray-300 rounded-md">
              <option value="中文">中文</option>
              <option value="English">English</option>
              <option value="日本語">日本語</option>
            </select>
          </div>
          <div class="flex items-center">
            <label class="flex items-center">
              <input v-model="config.reflectTranslate" type="checkbox" class="mr-2" />
              <span class="text-sm font-medium text-gray-700">启用两步翻译</span>
            </label>
          </div>
        </div>
      </div>

      <!-- 测试文本区域 -->
      <div class="space-y-4 mb-6">
        <h3 class="text-lg font-semibold text-gray-800">测试文本</h3>
        <textarea
          v-model="testText"
          rows="4"
          class="w-full p-3 border border-gray-300 rounded-md"
          placeholder="输入要翻译的文本..."
        ></textarea>
      </div>

      <!-- 操作按钮 -->
      <div class="flex gap-4 mb-6">
        <button
          :disabled="isTranslating"
          class="px-4 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700 disabled:opacity-50"
          @click="handleTestTranslation"
        >
          {{ isTranslating ? '翻译中...' : '开始翻译' }}
        </button>
        <button class="px-4 py-2 bg-green-600 text-white rounded-md hover:bg-green-700" @click="handleUpdateConfig">
          更新配置
        </button>
        <button class="px-4 py-2 bg-red-600 text-white rounded-md hover:bg-red-700" @click="resetTranslation">
          重置翻译
        </button>
      </div>

      <!-- 错误信息 -->
      <div v-if="translationError" class="mb-6 p-4 bg-red-50 border border-red-200 rounded-md">
        <p class="text-red-800">{{ translationError }}</p>
      </div>

      <!-- 翻译统计 -->
      <div v-if="getTranslationStatistics.totalTranslated > 0" class="mb-6">
        <h3 class="text-lg font-semibold text-gray-800 mb-2">翻译统计</h3>
        <div class="grid grid-cols-2 md:grid-cols-4 gap-4 text-sm">
          <div class="bg-blue-50 p-3 rounded-md">
            <span class="text-gray-600">总翻译数：</span>
            <span class="font-medium text-blue-600">{{ getTranslationStatistics.totalTranslated }}</span>
          </div>
          <div class="bg-green-50 p-3 rounded-md">
            <span class="text-gray-600">忠实翻译：</span>
            <span class="font-medium text-green-600">{{ faithfulResults.size }}</span>
          </div>
          <div class="bg-purple-50 p-3 rounded-md">
            <span class="text-gray-600">表达优化：</span>
            <span class="font-medium text-purple-600">{{ expressiveResults.size }}</span>
          </div>
          <div class="bg-orange-50 p-3 rounded-md">
            <span class="text-gray-600">翻译模式：</span>
            <span class="font-medium text-orange-600">
              {{ getTranslationStatistics.reflectTranslateEnabled ? '两步翻译' : '忠实翻译' }}
            </span>
          </div>
        </div>
      </div>

      <!-- 翻译结果 -->
      <div v-if="finalResults.size > 0" class="space-y-4">
        <h3 class="text-lg font-semibold text-gray-800">翻译结果</h3>
        <div class="space-y-2">
          <div
            v-for="(translation, index) in Array.from(finalResults.values())"
            :key="index"
            class="p-3 bg-gray-50 border border-gray-200 rounded-md"
          >
            <p class="text-gray-900">{{ translation }}</p>
          </div>
        </div>
      </div>

      <!-- 详细结果（开发模式） -->
      <div v-if="faithfulResults.size > 0" class="mt-6 space-y-4">
        <h3 class="text-lg font-semibold text-gray-800">详细翻译结果（开发模式）</h3>

        <!-- 忠实翻译结果 -->
        <div class="bg-blue-50 p-4 rounded-md">
          <h4 class="font-medium text-blue-900 mb-2">忠实翻译结果</h4>
          <pre class="text-xs text-blue-800 whitespace-pre-wrap">{{ JSON.stringify(Array.from(faithfulResults.entries()), null, 2) }}</pre>
        </div>

        <!-- 表达优化结果 -->
        <div v-if="expressiveResults.size > 0" class="bg-purple-50 p-4 rounded-md">
          <h4 class="font-medium text-purple-900 mb-2">表达优化结果</h4>
          <pre class="text-xs text-purple-800 whitespace-pre-wrap">{{
            JSON.stringify(Array.from(expressiveResults.entries()), null, 2)
          }}</pre>
        </div>
      </div>
    </div>
  </div>
</template>

<style scoped>
/* 自定义样式 */
</style>

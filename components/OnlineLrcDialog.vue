<script setup lang="ts">
import { onMounted, onUnmounted } from 'vue'
import BaseDialog from './ui/BaseDialog.vue'
import type { LrcData } from '~/utils/formats/parsers/lrcParser'
import { find, includes } from 'lodash-es'
import { useOnlineLrcDialog } from '~/composables/online-lrc/useOnlineLrcDialog'
import type { NeteaseSongItemSchemaType } from '~/server/trpc/schemas/api/song'

// props: 弹窗开关、标题
const props = defineProps<{
  isOpen: boolean
  title?: string
  subTitle?: string
}>()

export interface Emits {
  (e: 'close'): void
  (e: 'update:isOpen', value: boolean): void
  (e: 'apply', lrc: LrcData): void
}

// emits: 关闭弹窗、更新isOpen、应用歌词
const emit = defineEmits<Emits>()

// 点击查看的歌词的列表
const viewLrcList = ref<NeteaseSongItemSchemaType[]>([])

// 使用在线歌词对话框 composable
const {
  searchText,
  allSongs,
  searchStatus,
  lyricsData,
  lyricsStatus,
  lyricsError,
  hasMore,
  isLoadingMore,
  sentinelRef,
  selectedSongId,
  onGetOnlineLrc,
  onClearSearch,
  onView,
  onSetupObserver,
  onCleanupObserver,
  onCreateLrcData,
} = useOnlineLrcDialog()

const handleClose = () => {
  emit('close')
  emit('update:isOpen', false)
}

// 应用歌词
const handleApply = () => {
  if (!lyricsData.value?.lyric || !selectedSongId.value) return

  const songInfo = find(allSongs.value, (item) => item.songId === selectedSongId.value)
  if (!songInfo) return

  const lrcData = onCreateLrcData(songInfo)
  if (!lrcData) return

  emit('apply', lrcData)
  emit('close')
}

// 生命周期管理
onMounted(() => {
  onSetupObserver()
})

onUnmounted(() => {
  onCleanupObserver()
})
</script>

<template>
  <BaseDialog
    :isOpen="props.isOpen"
    :title="props.title || '获取歌词'"
    :subTitle="props.subTitle || '仅支持英文歌曲'"
    :showClose="true"
    @close="handleClose"
    @update:isOpen="emit('update:isOpen', $event)"
  >
    <div class="h-[55vh] w-full">
      <div class="flex w-full h-full gap-2">
        <div class="flex flex-1 flex-col w-1/2 border rounded-lg p-4">
          <UInput
            v-model="searchText"
            :loading="searchStatus === 'pending'"
            placeholder="请输入歌名/歌手"
            icon="i-heroicons-magnifying-glass"
            :ui="{ icon: { trailing: { pointer: '' } } }"
            @keyup.enter="
              () => {
                if (searchStatus === 'pending') return
                onGetOnlineLrc()
              }
            "
          >
            <template #trailing>
              <UButton
                v-show="searchText !== ''"
                color="gray"
                variant="link"
                icon="i-heroicons-x-mark-20-solid"
                :padded="false"
                @click="onClearSearch"
              />
            </template>
          </UInput>
          <!-- 歌曲搜索结果列表 -->
          <div v-if="allSongs.length > 0" class="mt-4 flex-1 space-y-2 overflow-y-auto rounded w-full">
            <div v-for="item in allSongs" :key="item.songId" class="bg-[#eaf0fa] rounded px-4 py-2">
              <div class="flex items-center justify-between">
                <div class="flex flex-col flex-1">
                  <span class="font-bold text-base text-gray-800">{{ item.name }}</span>
                  <span class="text-sm text-gray-600">{{ item.singer }}</span>
                </div>
                <div class="flex items-center gap-4">
                  <span class="text-sm text-gray-600">{{ item.duration }}</span>
                  <div class="flex ml-auto">
                    <UButton size="sm" color="sky" class="ml-2 flex items-center justify-center" @click.stop="onView(item)">
                      <UIcon :name="includes(viewLrcList, item) ? 'i-heroicons-eye-slash' : 'i-heroicons-eye'" class="w-4 h-4" />
                    </UButton>
                  </div>
                </div>
              </div>
            </div>

            <!-- 加载更多状态指示器 -->
            <div v-if="isLoadingMore" class="text-center py-4 text-gray-500">
              <UIcon name="i-heroicons-arrow-path" class="w-5 h-5 animate-spin inline-block mr-2" />
              加载更多中...
            </div>

            <!-- 已加载完毕提示 -->
            <div v-else-if="!hasMore && allSongs.length > 0" class="text-center py-4 text-gray-400">
              已加载完毕
            </div>

            <!-- Intersection Observer的目标元素 -->
            <div ref="sentinelRef" class="h-1"></div>
          </div>
        </div>

        <!-- 歌词展示 -->
        <div class="flex flex-1 flex-col w-1/2 border rounded-lg p-4">
          <div class="flex">
            <h3 class="text-lg font-medium mb-3">歌词</h3>
            <UButton color="emerald" class="ml-auto h-8" :disabled="!lyricsData?.lyric" @click.stop="handleApply">应用</UButton>
          </div>

          <div v-if="lyricsStatus === 'pending'" class="text-center py-4 text-gray-500">
            获取歌词中...
          </div>

          <div v-else-if="lyricsData" class="flex-1 overflow-y-auto">
            <div class="p-2 bg-gray-50 rounded text-sm">
              <div class="font-medium text-gray-800 whitespace-pre-line">{{ lyricsData.lyric }}</div>
            </div>
          </div>

          <div v-else-if="lyricsError" class="text-center py-4 text-red-500">
            {{ lyricsError || '获取歌词失败' }}
          </div>

          <div v-else class="text-center py-4 text-gray-500">
            点击左侧歌曲获取歌词
          </div>
        </div>
      </div>
    </div>
  </BaseDialog>
</template>

<style scoped></style>

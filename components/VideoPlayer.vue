<script setup lang="ts">
import { isEmpty, forEachRight } from 'lodash-es'
import videojs from 'video.js'
import type Player from 'video.js/dist/types/player'

import 'video.js/dist/video-js.css'

import { PlayerStatusEnum, usePlayerStore } from '~/stores/playerStore'
import { useSubtitleStore } from '~/stores/subtitleStore'
import { subtitlesToVttString } from '~/utils/formats/converters/subtitleConverter'

const playerStore = usePlayerStore()
const subtitlesStore = useSubtitleStore()

const videoPlayer = ref<HTMLVideoElement | null>(null)
const vttUrl = ref<string>('')
const vttTrackRef = ref<HTMLTrackElement | null>(null)
const playerContainerRef = ref<HTMLElement | null>(null)
let resizeObserver: ResizeObserver | null = null

let player: Player | null = null

// 清除 vtt 轨道
const clearVttTrack = () => {
  if (vttTrackRef.value) {
    try {
      // eslint-disable-next-line @typescript-eslint/no-explicit-any
      const existingTracks = (player as any).remoteTextTracks()
      if (existingTracks && existingTracks.length > 0) {
        // 从后向前遍历并移除，因为移除元素会改变列表长度和索引
        forEachRight(existingTracks, (track) => {
          if (track.kind === 'subtitles') {
            // 移除指定的远程文本轨道
            // eslint-disable-next-line @typescript-eslint/no-explicit-any
            ;(player as any).removeRemoteTextTrack(track)
          }
        })
      }
    } catch (error) {
      console.error('Failed to clear vtt track:', error)
    }
    vttTrackRef.value = null
  }
  if (vttUrl.value) {
    URL.revokeObjectURL(vttUrl.value)
    vttUrl.value = ''
  }
}

// 生成 vtt blob url
const createBlobUrlFromVttPath = () => {
  const vttString = subtitlesToVttString(subtitlesStore.subtitles)
  console.log('%c AT 🥝 vttString 🥝-55', 'font-size:13px; background:#32f269; color:#76ffad;', vttString)
  const blob = new Blob([vttString], { type: 'text/vtt' })
  return URL.createObjectURL(blob)
}

// 应用 vtt 轨道
const generateVttAndApplyTrack = () => {
  // 检查播放器实例是否存在且未被销毁
  if (!player || player.isDisposed()) return

  // 清除旧字幕
  clearVttTrack()

  // 没有字幕则不添加
  if (isEmpty(subtitlesStore.subtitles)) {
    console.log('No subtitles to display.')
    return
  }

  vttUrl.value = createBlobUrlFromVttPath()

  const trackOptions = {
    kind: 'subtitles' as const,
    src: vttUrl.value,
    srclang: 'zh',
    label: '用户字幕',
    default: true,
  }

  const htmlTrackElement = player.addRemoteTextTrack(trackOptions, false)

  if (htmlTrackElement) {
    htmlTrackElement.addEventListener('load', function () {
      // eslint-disable-next-line @typescript-eslint/no-explicit-any
      const track = (htmlTrackElement as any).track
      vttTrackRef.value = track
      if (track) {
        track.mode = 'showing' // 设置字幕为显示状态
      }
    })
  } else {
    console.warn('Failed to add remote text track or track element is invalid.')
  }
}

// 监听播放状态
watch(
  () => playerStore.status,
  (newStatus, oldStatus) => {
    // 避免重复处理
    if (!player || newStatus === oldStatus) return

    if (newStatus === PlayerStatusEnum.PLAYING) {
      player.play()
    } else if (newStatus === PlayerStatusEnum.PAUSED) {
      player.pause()
    }
  },
)

// 监听全局 seekRequest，实现事件驱动跳转
watch(
  () => playerStore.seekRequest,
  (req, oldReq) => {
    // 有音频文件时，才允许跳转
    if (!playerStore.mediaFile) return
    if (req && (!oldReq || req.key !== oldReq.key) && player) {
      // 确保时间在持续时间之内
      const duration = player.duration()
      if (typeof duration === 'number' && req.time >= 0 && req.time <= duration) {
        player.currentTime(req.time)
      } else {
        console.warn(`Seek request ${req.time} is out of bounds (duration: ${duration})`)
      }
    }
  },
)

// 监听视频源
watch(
  () => playerStore.mediaUrl,
  (newUrl, oldUrl) => {
    // 避免重复处理
    if (newUrl === oldUrl || !videoPlayer.value) return

    if (player && !player.isDisposed()) {
      // 设置视频源
      player.src({ src: playerStore.mediaUrl, type: playerStore.mediaFile?.type || 'video/mp4' })

      // 加载视频
      player.load()
    }

    // 生成字幕
    generateVttAndApplyTrack()
  },
)

// 生成 vtt 文件
watch(
  () => subtitlesStore.subtitles,
  () => {
    generateVttAndApplyTrack()
  },
  { deep: true },
)

// 监听播放速率
watch(
  () => playerStore.playbackRate,
  () => {
    if (player) {
      player.playbackRate(playerStore.playbackRate)
    }
  },
)

const adjustPlayerSize = () => {
  if (!player || !playerContainerRef.value) return
  const containerWidth = playerContainerRef.value.clientWidth
  const containerHeight = playerContainerRef.value.clientHeight
  const videoEl = player.el() as HTMLElement
  if (!videoEl) return
  if (containerWidth > containerHeight) {
    videoEl.style.height = `${containerHeight}px`
    videoEl.style.width = '100%'
  } else if (containerWidth < containerHeight) {
    videoEl.style.height = '100%'
    videoEl.style.width = `${containerWidth}px`
  } else {
    videoEl.style.height = '100%'
    videoEl.style.width = '100%'
  }
}

const initPlayer = () => {
  if (videoPlayer.value) {
    const options = {
      controls: false,
      preload: 'auto',
      fluid: false, // 关闭 fluid 以便手动控制尺寸
      bigPlayButton: false,
    }
    player = videojs(videoPlayer.value, options, () => {
      if (playerContainerRef.value && player) {
        adjustPlayerSize()
        resizeObserver = new ResizeObserver(() => {
          if (player) adjustPlayerSize()
        })
        resizeObserver.observe(playerContainerRef.value)
      }
    })
  }
}

onMounted(() => {
  initPlayer()
})

onUnmounted(() => {
  if (resizeObserver && playerContainerRef.value) {
    resizeObserver.unobserve(playerContainerRef.value)
  }
  resizeObserver = null

  if (player && !player.isDisposed()) {
    player.dispose()
    player = null
  }
  clearVttTrack()
})
</script>

<template>
  <div ref="playerContainerRef" class="w-full h-full flex items-center justify-center bg-black relative select-none">
    <div
      v-if="!playerStore.mediaFile"
      class="text-gray-500 select-none text-sm absolute top-1/2 left-1/2 -translate-x-1/2 -translate-y-1/2 z-10"
    >
      音视频-预览区
    </div>
    <template v-if="playerStore.mediaFile && playerStore.mediaFile.type.startsWith('audio/')">
      <div class="text-[#86909c82] select-none text-xs absolute top-1/2 left-1/2 -translate-x-1/2 -translate-y-1/2">音频文件无视频预览</div>
    </template>
    <template v-else>
      <video ref="videoPlayer" class="video-js">
        <source v-if="playerStore.mediaUrl" :src="playerStore.mediaUrl" type="video/mp4" />
        <!-- 初始字幕轨道将通过 JS 添加 -->
        <p class="vjs-no-js">
          要观看此视频，请启用JavaScript，并考虑升级到web浏览器
          <a href="https://videojs.com/html5-video-support/" target="_blank">supports HTML5 video</a>
        </p>
      </video>
    </template>
  </div>
</template>

<style scoped>
/* Video.js 播放器本身的样式调整 */
.video-js {
  display: block;
  margin: 0 auto;
}
</style>

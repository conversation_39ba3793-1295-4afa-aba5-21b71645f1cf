<script setup lang="ts">
import { Splitpanes, Pane } from 'splitpanes'
import 'splitpanes/dist/splitpanes.css'
import LrcDisplay from '~/components/LrcDisplay.vue'
import VideoPlayer from '~/components/VideoPlayer.vue'

const defaultSizes = [50, 50]
const paneSizes = ref([...defaultSizes])
const showActionBtns = ref(false)
const splitterRect = ref<{ top: number; left: number; width: number; height: number } | null>(null)
let splitterEl: HTMLElement | null = null

function updateSplitterRect() {
  if (splitterEl) {
    const rect = splitterEl.getBoundingClientRect()
    splitterRect.value = {
      top: rect.top + window.scrollY,
      left: rect.left + window.scrollX,
      width: rect.width,
      height: rect.height,
    }
  }
}

function attachSplitterHover() {
  nextTick(() => {
    splitterEl = document.querySelector('.splitpanes__splitter') as HTMLElement
    if (splitterEl) {
      splitterEl.addEventListener('mouseenter', onSplitterEnter)
      splitterEl.addEventListener('mouseleave', onSplitterLeave)
      splitterEl.addEventListener('mousemove', updateSplitterRect)
    }
  })
}
function detachSplitterHover() {
  if (splitterEl) {
    splitterEl.removeEventListener('mouseenter', onSplitterEnter)
    splitterEl.removeEventListener('mouseleave', onSplitterLeave)
    splitterEl.removeEventListener('mousemove', updateSplitterRect)
  }
}
function onSplitterEnter() {
  showActionBtns.value = true
  updateSplitterRect()
}
function onSplitterLeave(e: MouseEvent) {
  const btns = document.getElementById('splitter-action-btns')
  if (btns && e.relatedTarget && btns.contains(e.relatedTarget as Node)) return
  showActionBtns.value = false
}

onMounted(() => {
  attachSplitterHover()
  window.addEventListener('resize', updateSplitterRect)
})
onBeforeUnmount(() => {
  detachSplitterHover()
  window.removeEventListener('resize', updateSplitterRect)
})
</script>

<template>
  <div class="w-full h-full overflow-auto relative">
    <Splitpanes v-model="paneSizes" :push-other-panes="false" horizontal style="height: 100%;" class="splitpanes-rightpane-vertical">
      <Pane :size="paneSizes[0]"><LrcDisplay /></Pane>
      <Pane :size="paneSizes[1]">
        <VideoPlayer />
      </Pane>
    </Splitpanes>
  </div>
</template>

<style scoped>
.splitpanes-rightpane-vertical :deep(.splitpanes__splitter) {
  background: rgba(180, 180, 200, 0.18);
  width: 100% !important;
  max-width: 100% !important;
  min-width: 100% !important;
  height: 7px;
  min-height: 7px;
  max-height: 7px;
  border-radius: 4px;
  position: relative;
  cursor: row-resize;
  transition: background 0.3s, box-shadow 0.3s;
  box-shadow: none;
  border: none;
}
.splitpanes-rightpane-vertical :deep(.splitpanes__splitter:hover) {
  background: rgba(120, 180, 255, 0.28);
  box-shadow: 0 0 8px 2px rgba(120, 180, 255, 0.18);
}
.splitpanes-rightpane-vertical :deep(.splitpanes__splitter::before) {
  content: '';
  position: absolute;
  left: 0;
  right: 0;
  top: -8px !important;
  bottom: -8px !important;
  background: transparent;
  z-index: 1;
  border-radius: 6px;
  transition: background 0.3s;
}
.splitpanes-rightpane-vertical :deep(.splitpanes__splitter:hover::before) {
  background: rgba(120, 180, 255, 0.1);
}
.splitpanes-rightpane-vertical :deep(.splitpanes--horizontal > .splitpanes__splitter) {
  height: 7px !important;
  min-height: 7px !important;
  max-height: 7px !important;
}
</style>

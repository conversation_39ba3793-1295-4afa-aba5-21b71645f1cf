<script setup lang="ts">
import { isEmpty, findLastIndex } from 'lodash-es'
import { useLrcStore } from '~/stores/lrcStore'
import { useSubtitleStore } from '~/stores/subtitleStore'
import { usePlayerStore } from '~/stores/playerStore'
import { formatTimeToString } from '~/utils/processing/time/timeFormatter'
import { lrcToSrt } from '~/utils/formats/converters/subtitleConverter'

const lrcStore = useLrcStore()
const playerStore = usePlayerStore()
const subtitleStore = useSubtitleStore()

// Refs
const containerRef = ref<HTMLDivElement | null>(null)
const fileInputRef = ref<HTMLInputElement | null>(null)
const showTranslations = ref(true)
const copyFeedback = ref<{
  type: 'time' | 'text' | 'translation'
  index: number
} | null>(null)

// 计算当前活动行索引
const activeLineIndex = computed(() => {
  return findLastIndex(lrcStore.lrcLines, (line) => line.time <= playerStore.currentTime)
})

const handleConvertToSrt = () => {
  if (!lrcStore.lrcLines.length) {
    console.warn('没有可转换的 LRC 行')
    return
  }
  const srtSubtitles = lrcToSrt(lrcStore.lrcLines)
  console.log('%c AT 🥝 srtSubtitles 🥝-43', 'font-size:13px; background:#ec3551; color:#ff7995;', srtSubtitles)
  subtitleStore.setSubtitlesFromLrc(srtSubtitles)
}

// 文件上传处理
const handleLrcFileUpload = async (event: Event) => {
  const file = (event.target as HTMLInputElement).files?.[0]
  if (file) {
    await lrcStore.uploadLrc(file)

    handleConvertToSrt()
  }
}

// 复制到剪贴板
const copyToClipboard = async (text: string, type: 'time' | 'text' | 'translation', index: number) => {
  if (!text) {
    return
  }

  try {
    await navigator.clipboard.writeText(text)
    copyFeedback.value = { type, index }
    setTimeout(() => {
      copyFeedback.value = null
    }, 200)
  } catch (error) {
    console.error('复制失败:', error)
  }
}

// 监听活动行变化，自动滚动
watch(
  () => activeLineIndex.value,
  async (newIndex) => {
    if (newIndex === -1 || !containerRef.value) {
      return
    }

    await nextTick()
    const activeElement = containerRef.value.children[newIndex] as HTMLElement
    if (activeElement) {
      activeElement.scrollIntoView({ behavior: 'smooth', block: 'center' })
    }
  },
)

// 处理行点击
const handleLineClick = (time: number) => {
  playerStore.setSeekRequest(time)
}
</script>

<template>
  <div class="h-full flex flex-col relative">
    <input ref="fileInputRef" type="file" accept=".lrc" class="hidden" @change="handleLrcFileUpload" />
    <p v-if="!lrcStore.hasLrc" class="text-gray-500 text-sm select-none absolute top-1/2 left-1/2 -translate-x-1/2 -translate-y-1/2">
      文本-预览区
    </p>

    <!-- 显示 LRC 内容 -->
    <template v-if="lrcStore.hasLrc && !isEmpty(lrcStore.lrcLines)">
      <div ref="containerRef" class="flex-1 overflow-y-auto p-4 flex flex-col items-center w-full group">
        <!-- 显示带时间戳的歌词 -->
        <div
          v-for="(line, index) in lrcStore.lrcLines"
          :key="'lrc-' + index"
          class="my-2 w-full max-w-3xl flex items-center transition-all duration-300"
          :class="{
            'text-primary-500 font-bold': index === activeLineIndex,
            'text-gray-300': index !== activeLineIndex,
          }"
          @click="handleLineClick(line.time)"
        >
          <!-- 时间戳区域 -->
          <div class="flex items-center min-w-[120px] mr-4 relative pl-6">
            <button
              class="absolute left-0 p-1 transition-all opacity-0 group-hover:opacity-100"
              :class="
                copyFeedback?.type === 'time' && copyFeedback?.index === index ? 'text-green-500' : 'text-gray-400 hover:text-primary-400'
              "
              :title="copyFeedback?.type === 'time' && copyFeedback?.index === index ? '已复制!' : '复制时间戳'"
              @click="copyToClipboard(formatTimeToString(line.time), 'time', index)"
            >
              <UIcon v-if="copyFeedback?.type === 'time' && copyFeedback?.index === index" name="i-heroicons-check" class="w-3.5 h-3.5" />
              <UIcon v-else name="i-heroicons-clipboard" class="w-3.5 h-3.5" />
            </button>
            <span class="text-xs font-mono text-gray-500 cursor-pointer" @click="handleLineClick(line.time)">
              {{ formatTimeToString(line.time) }}
            </span>
          </div>

          <!-- 歌词文本区域 -->
          <div class="flex-1 flex flex-col">
            <div class="flex items-center">
              <!-- 普通文本显示 -->
              <span class="flex-1">{{ line.text }}</span>
              <button
                class="ml-2 p-1 transition-all opacity-0 group-hover:opacity-100"
                :class="
                  copyFeedback?.type === 'text' && copyFeedback?.index === index ? 'text-green-500' : 'text-gray-400 hover:text-primary-400'
                "
                :title="copyFeedback?.type === 'text' && copyFeedback?.index === index ? '已复制!' : '复制文本'"
                @click="copyToClipboard(line.text || '', 'text', index)"
              >
                <UIcon v-if="copyFeedback?.type === 'text' && copyFeedback?.index === index" name="i-heroicons-check" class="w-3.5 h-3.5" />
                <UIcon v-else name="i-heroicons-clipboard" class="w-3.5 h-3.5" />
              </button>
            </div>

            <!-- 翻译文本 -->
            <div v-if="showTranslations && line.translationText" class="flex items-center text-sm text-gray-400 mt-1">
              <span class="flex-1">{{ line.translationText }}</span>
              <button
                class="ml-2 p-1 transition-all opacity-0 group-hover:opacity-100"
                :class="
                  copyFeedback?.type === 'translation' && copyFeedback?.index === index
                    ? 'text-green-500'
                    : 'text-gray-400 hover:text-primary-400'
                "
                :title="copyFeedback?.type === 'translation' && copyFeedback?.index === index ? '已复制!' : '复制翻译'"
                @click="copyToClipboard(line.translationText, 'translation', index)"
              >
                <UIcon
                  v-if="copyFeedback?.type === 'translation' && copyFeedback?.index === index"
                  name="i-heroicons-check"
                  class="w-3.5 h-3.5"
                />
                <UIcon v-else name="i-heroicons-clipboard" class="w-3.5 h-3.5" />
              </button>
            </div>
          </div>
        </div>

        <!-- 显示普通文本行 -->
        <div v-for="(text, index) in lrcStore.plainTextLines" :key="'plain-' + index" class="my-2 w-full max-w-3xl flex items-center">
          <div class="flex-1">
            <span class="text-gray-400">{{ text }}</span>
          </div>
        </div>
      </div>
    </template>
  </div>
</template>

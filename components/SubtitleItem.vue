<script setup lang="ts">
import { useSubtitleStore } from '~/stores/subtitleStore'
import { usePlayerStore } from '~/stores/playerStore'
import { useSubtitleEditing } from '~/composables/subtitle/useSubtitleEditing'
import { useSubtitleTimeValidation } from '~/composables/subtitle/useSubtitleTimeValidation'
import { useSubtitleActions } from '~/composables/subtitle/useSubtitleActions'
import { parseSrtTime } from '~/utils/processing/time/timeParser'
import TimeInput from './TimeInput.vue'
import type { Subtitle } from '~/types/subtitle'
import { find, findIndex } from 'lodash-es'

const props = defineProps<{
  subtitle: Subtitle
  nextSubtitle: Subtitle | null
  isLast: boolean
  id: string
  isActive: boolean
}>()

const subtitleStore = useSubtitleStore()
const playerStore = usePlayerStore()

// 获取最新字幕对象
const currentSubtitle = computed(() => find(subtitleStore.subtitles, (s) => s.uuid === props.subtitle.uuid) || props.subtitle)

// 计算上一行结束时间和下一行开始时间
const prevSubtitleEndTime = computed(() => {
  const idx = findIndex(subtitleStore.subtitles, (s) => s.uuid === props.subtitle.uuid)
  if (idx > 0) {
    return subtitleStore.subtitles[idx - 1].endTime
  }
  return null
})
const nextSubtitleStartTime = computed(() => props.nextSubtitle?.startTime || null)

// 编辑相关逻辑
const {
  editing,
  editingText,
  editingTranslationText,
  editingType,
  startTime,
  endTime,
  textareaRef,
  translationTextareaRef,
  startTimeInputRef,
  endTimeInputRef,
  startEdit,
  confirmEdit,
  cancelEdit,
  onStartTime,
  onEndTime,
} = useSubtitleEditing(currentSubtitle, subtitleStore)

// 时间校验相关逻辑
const { isAddDisabled, isStartTimeOrderValid, isEndTimeOrderValid } = useSubtitleTimeValidation(
  startTime,
  endTime,
  nextSubtitleStartTime,
  prevSubtitleEndTime,
)

// 传递给 TimeInput 的顺序校验类型
const startTimeOrderType = computed(() => {
  return isStartTimeOrderValid.value ? true : 'prev'
})
const endTimeOrderType = computed(() => {
  return isEndTimeOrderValid.value ? true : 'next'
})

// 操作相关逻辑
const { addTooltipContent, handleAddSubtitle, handleDeleteSubtitle, handleMergeSubtitle } = useSubtitleActions(
  ref(props.subtitle),
  subtitleStore,
  isAddDisabled,
  ref(props.isLast),
)

// 计算当前字幕是否在循环播放
const isLooping = computed(() => playerStore.loopingSubtitleUuid === props.subtitle.uuid)

const emit = defineEmits(['merge-button-hover', 'merge-button-leave', 'row-blank-click'])

function handleRowClick() {
  emit('row-blank-click', currentSubtitle.value.startTime)
}

// 处理循环播放
function handleLoopToggle(event: MouseEvent) {
  event.stopPropagation()
  if (isLooping.value) {
    playerStore.stopLoop()
  } else {
    const startSeconds = parseSrtTime(props.subtitle.startTime)
    const endSeconds = parseSrtTime(props.subtitle.endTime)
    if (startSeconds !== null && endSeconds !== null) {
      playerStore.startLoop(props.subtitle.uuid, startSeconds, endSeconds)
    }
  }
}

// 监听字幕时间变化，更新循环时间
watch(
  () => [props.subtitle.startTime, props.subtitle.endTime],
  ([newStartTime, newEndTime]) => {
    if (playerStore.loopingSubtitleUuid === props.subtitle.uuid) {
      const startSeconds = parseSrtTime(newStartTime)
      const endSeconds = parseSrtTime(newEndTime)
      playerStore.updateLoopTime(startSeconds, endSeconds)
    }
  },
)
</script>

<template>
  <div
    :id="props.id"
    class="subtitle-hover-row cursor-pointer relative flex flex-col border-b border-gray-700 min-h-28 transition-all duration-200 hover:bg-gray-800/50 hover:shadow-[0_0_15px_rgba(255,255,255,0.05)]"
    :class="{
      'is-active': props.isActive,
      'is-looping': isLooping,
    }"
    @click="handleRowClick"
  >
    <div class="flex items-center gap-2 flex-grow px-2 py-6">
      <span class="w-6 text-center text-xs text-gray-500">
        {{ props.subtitle.id }}
      </span>
      <div class="flex flex-col justify-center">
        <div class="cursor-pointer" @click.stop="(startTimeInputRef as any)?.focus?.()">
          <TimeInput
            ref="startTimeInputRef"
            v-model="startTime"
            label="开始时间"
            :isOrderValid="startTimeOrderType"
            class="w-28"
            @update:modelValue="onStartTime"
          />
        </div>
        <div class="cursor-pointer mt-1" @click.stop="(endTimeInputRef as any)?.focus()">
          <TimeInput
            ref="endTimeInputRef"
            v-model="endTime"
            label="结束时间"
            :isOrderValid="endTimeOrderType"
            class="w-28"
            :maxSeconds="playerStore.mediaFile && playerStore.duration > 0 ? playerStore.duration : undefined"
            @update:modelValue="onEndTime"
          />
        </div>
      </div>
      <div class="flex-1">
        <div>
          <span
            v-if="!editing || editingType !== 'text'"
            class="cursor-pointer w-full inline-block bg-gray-800 rounded-md px-2 py-1"
            :class="{ 'placeholder-text': !props.subtitle.text }"
            @click.stop="startEdit('text')"
          >
            {{ props.subtitle.text || '（点击编辑文本）' }}
          </span>
          <textarea
            v-else-if="editing && editingType === 'text'"
            ref="textareaRef"
            v-model="editingText"
            class="border border-gray-700 rounded px-1 py-0.5 w-full text-sm bg-gray-900 text-gray-100 min-h-12"
            rows="1"
            @blur="confirmEdit"
            @keydown.enter.exact.prevent="confirmEdit"
            @keydown.esc="cancelEdit"
            @click.stop
          />
        </div>
        <div class="mt-1">
          <span
            v-if="(!editing || editingType !== 'translation') && props.subtitle.translationText"
            class="cursor-pointer text-gray-400 text-sm w-full inline-block bg-gray-800 rounded-md px-2 py-1"
            @click.stop="startEdit('translation')"
          >
            {{ props.subtitle.translationText }}
          </span>
          <button
            v-else-if="(!editing || editingType !== 'translation') && !props.subtitle.translationText"
            class="text-xs text-blue-400 hover:underline cursor-pointer bg-transparent border-none p-0"
            @click.stop="startEdit('translation')"
          >
            + 添加翻译
          </button>
          <textarea
            v-else-if="editing && editingType === 'translation'"
            ref="translationTextareaRef"
            v-model="editingTranslationText"
            class="border border-gray-700 rounded px-1 py-0.5 w-full text-sm bg-gray-900 text-gray-400 min-h-12"
            rows="1"
            @blur="confirmEdit"
            @keydown.enter.exact.prevent="confirmEdit"
            @keydown.esc="cancelEdit"
            @click.stop
          />
        </div>
      </div>
      <div class="flex gap-1">
        <UTooltip v-if="playerStore.mediaFile" text="循环播放此片段">
          <UButton
            :icon="isLooping ? 'i-heroicons-arrow-path-solid' : 'i-heroicons-arrow-path'"
            :color="isLooping ? 'primary' : 'gray'"
            size="xs"
            variant="soft"
            @click="handleLoopToggle"
          />
        </UTooltip>
        <UTooltip text="删除当前行">
          <UButton icon="i-heroicons-trash" color="red" size="xs" variant="soft" @click.stop="handleDeleteSubtitle" />
        </UTooltip>
      </div>
    </div>
    <div class="pointer-events-none z-10">
      <div class="pointer-events-auto flex justify-center items-center gap-20 -mt-4 -mb-4 w-full">
        <!-- 添加按钮 -->
        <UTooltip :text="addTooltipContent">
          <UButton
            icon="i-heroicons-plus-circle"
            color="primary"
            size="xs"
            variant="soft"
            :disabled="isAddDisabled"
            @click.stop="handleAddSubtitle"
          />
        </UTooltip>
        <!-- 合并按钮 -->
        <UTooltip :text="props.isLast ? '没有合并的行' : '合并'">
          <UButton
            :disabled="props.isLast"
            icon="i-heroicons-sparkles"
            color="yellow"
            size="xs"
            variant="soft"
            @click.stop="handleMergeSubtitle"
            @mouseover="emit('merge-button-hover', props.subtitle.uuid)"
            @mouseout="emit('merge-button-leave')"
          />
        </UTooltip>
      </div>
    </div>
  </div>
</template>

<style scoped>
/* 渐变+淡色内阴影 */
.subtitle-hover-row:hover {
  background: linear-gradient(90deg, rgba(59, 130, 246, 0.08) 0%, rgba(236, 72, 153, 0.08) 100%);
  box-shadow: 0 0 0 2px rgba(59, 130, 246, 0.15) inset;
}

/* 蓝粉渐变+白色柔和叠加 */
.subtitle-hover-row.is-active {
  background: linear-gradient(90deg, rgba(59, 130, 246, 0.16) 0%, rgba(236, 72, 153, 0.16) 100%), rgba(255, 255, 255, 0.08);
}

/* 循环播放状态样式 */
.subtitle-hover-row.is-looping {
  background: linear-gradient(90deg, rgba(59, 130, 246, 0.16) 0%, rgba(236, 72, 153, 0.16) 100%), rgba(255, 255, 255, 0.08);
  border-left: 2px solid #3b82f6;
}

/* 占位文本样式 */
.placeholder-text {
  color: rgba(156, 163, 175, 0.6);
  font-style: italic;
  opacity: 0.8;
  transition: all 0.2s ease;
}

.placeholder-text:hover {
  opacity: 1;
  color: rgba(156, 163, 175, 0.8);
}
</style>

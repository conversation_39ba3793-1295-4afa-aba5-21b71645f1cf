<script setup lang="ts">
import { useSubtitleStore } from '~/stores/subtitleStore'
import SubtitleList from '~/components/SubtitleList.vue'
import FindReplaceDialog from '~/components/FindReplaceDialog.vue'
import SubtitleToolbar from '~/components/SubtitleToolbar.vue'
import { generateSrt } from '~/utils/formats/generators/srtGenerator'
import { lrcToSrt, subtitleToLrc } from '~/utils/formats/converters/subtitleConverter'
import { parseSrt } from '~/utils/formats/parsers/srtParser'
import { parseVtt } from '~/utils/formats/parsers/vttParser'

const subtitleStore = useSubtitleStore()
const lrcStore = useLrcStore()
// 控制查找/替换对话框的显示状态
const isFindReplaceOpen = ref(false)
const subtitleFileInputRef = ref<HTMLInputElement | null>(null)

/**
 * 导出当前字幕为 SRT 文件。
 */
async function exportSrtFile() {
  // 如果没有字幕数据，则不执行任何操作
  if (subtitleStore.subtitles.length === 0) return

  // 使用 srtUtils 生成 SRT 格式的字符串内容
  const srtContent = generateSrt(subtitleStore.subtitles)
  // 创建一个 Blob 对象，用于承载文件内容
  const blob = new Blob([srtContent], { type: 'text/plain;charset=utf-8' })
  // 创建一个临时的 URL 指向 Blob 对象
  const url = URL.createObjectURL(blob)

  // 创建一个 <a> 元素用于触发下载
  const link = document.createElement('a')
  link.href = url
  // 设置下载的文件名
  link.download = 'subtitles.srt'
  // 将链接添加到 DOM 中（隐藏）
  document.body.appendChild(link)
  // 模拟点击链接以触发下载
  link.click()
  // 下载完成后从 DOM 中移除链接
  document.body.removeChild(link)

  // 释放之前创建的临时 URL
  URL.revokeObjectURL(url)
}

/**
 * 处理批量替换字幕文本。
 * @param replacements - 包含要替换的字幕 UUID、新原文和新的翻译的数组
 */
function handleReplace(
  replacements: {
    uuid: string
    newOriginalText?: string
    newTranslatedText?: string
  }[],
) {
  subtitleStore.replaceTextAndTranslationBatch(replacements)
}

// 触发合并字幕文件上传
const triggerSubtitleFileInput = () => {
  subtitleFileInputRef.value?.click()
}

// 合并字幕文件上传处理
const handleSubtitleFileUpload = async (event: Event) => {
  const file = (event.target as HTMLInputElement).files?.[0]
  if (!file) return
  const ext = file.name.split('.').pop()?.toLowerCase()
  if (ext === 'lrc') {
    await lrcStore.uploadLrc(file)
    const srtSubtitles = lrcToSrt(lrcStore.lrcLines)
    subtitleStore.setSubtitlesFromLrc(srtSubtitles)
  } else if (ext === 'srt') {
    const content = await file.text()
    const srtSubtitles = parseSrt(content)
    subtitleStore.setSubtitlesFromLrc(srtSubtitles)
    const lrcContent = subtitleToLrc(srtSubtitles)
    lrcStore.updateLrcContent('', lrcContent)
  } else if (ext === 'vtt') {
    const content = await file.text()
    const vttSubtitles = parseVtt(content)
    subtitleStore.setSubtitlesFromLrc(vttSubtitles)
    const lrcContent = subtitleToLrc(vttSubtitles)
    lrcStore.updateLrcContent('', lrcContent)
  }
  // 清空 input 以便连续上传同一文件
  ;(event.target as HTMLInputElement).value = ''
}
</script>

<template>
  <div class="flex justify-center h-full">
    <!-- 如果没有字幕数据，显示加载或创建选项 -->
    <div v-if="subtitleStore.subtitles.length === 0" class="flex flex-col items-center justify-center">
      <div class="text-gray-500 text-sm select-none">支持 VTT、SRT、LRC 格式</div>
      <input ref="subtitleFileInputRef" type="file" accept=".vtt,.srt,.lrc" class="hidden" @change="handleSubtitleFileUpload" />
      <UButton class="mt-4" icon="i-heroicons-document-text" @click="triggerSubtitleFileInput"> 导入字幕 </UButton>
    </div>
    <!-- 如果有字幕数据，显示编辑界面 -->
    <div v-else class="relative w-full flex flex-col h-full">
      <SubtitleToolbar :onExport="exportSrtFile" :onFindReplace="() => (isFindReplaceOpen = true)" class="flex-shrink-0" />
      <!-- 字幕列表组件 -->
      <SubtitleList class="flex-grow min-h-0" />
      <!-- 查找/替换对话框 -->
      <FindReplaceDialog :is-open="isFindReplaceOpen" @close="isFindReplaceOpen = false" @replace="handleReplace" />
    </div>
  </div>
</template>

<script setup lang="ts">
import type { SubtitleFilterType, Subtitle } from '~/types/subtitle'
import BaseDialog from './ui/BaseDialog.vue'
import { useSubtitleStore } from '~/stores/subtitleStore'
import { hasTimestampError } from '~/utils/processing/time/timeValidator'

interface Props {
  isOpen: boolean
}

interface Emits {
  (e: 'update:isOpen', value: boolean): void
  (e: 'jump-to', subtitle: Subtitle): void
}

const props = defineProps<Props>()
const emit = defineEmits<Emits>()

const store = useSubtitleStore()

const filterOptions = [
  { value: 'all' as SubtitleFilterType, label: '显示全部', description: '显示所有字幕条目' },
  { value: 'textEmpty' as SubtitleFilterType, label: '原文为空', description: '仅显示原文为空的条目' },
  { value: 'textOnly' as SubtitleFilterType, label: '仅有原文', description: '仅显示有原文但无翻译的条目' },
  { value: 'translationOnly' as SubtitleFilterType, label: '仅有翻译', description: '仅显示有翻译但无原文的条目' },
  { value: 'bothEmpty' as SubtitleFilterType, label: '都为空', description: '仅显示原文和翻译都为空的条目' },
  { value: 'hasContent' as SubtitleFilterType, label: '有原文或翻译', description: '仅显示原文或翻译至少有一个不为空的条目' },
  { value: 'timestampError' as SubtitleFilterType, label: '时间戳错误', description: '显示时间戳格式错误或时间逻辑错误的条目' },
]

const selectedFilter = ref<SubtitleFilterType>(store.currentFilter)

watch(
  () => store.currentFilter,
  (newValue) => {
    selectedFilter.value = newValue
  },
)

function handleApplyFilter() {
  store.setFilter(selectedFilter.value)
  emit('update:isOpen', false)
}

function handleClose() {
  selectedFilter.value = store.currentFilter // 重置为当前筛选状态
  emit('update:isOpen', false)
}

// 计算每种筛选条件的数量
const filterCounts = computed(() => {
  const counts = {} as Record<SubtitleFilterType, number>

  filterOptions.forEach((option) => {
    if (option.value === 'all') {
      counts[option.value] = store.subtitles.length
    } else {
      counts[option.value] = store.subtitles.filter((subtitle, index) => {
        const hasText = subtitle.text.trim() !== ''
        const hasTranslation = subtitle.translationText.trim() !== ''

        switch (option.value) {
          case 'textEmpty':
            return !hasText
          case 'textOnly':
            return hasText && !hasTranslation
          case 'translationOnly':
            return !hasText && hasTranslation
          case 'bothEmpty':
            return !hasText && !hasTranslation
          case 'hasContent':
            return hasText || hasTranslation
          case 'timestampError': {
            const prevSubtitle = index > 0 ? store.subtitles[index - 1] : undefined
            const nextSubtitle = index < store.subtitles.length - 1 ? store.subtitles[index + 1] : undefined
            return hasTimestampError(subtitle, prevSubtitle, nextSubtitle)
          }
          default:
            return true
        }
      }).length
    }
  })

  return counts
})

// 计算筛选结果
const previewSubtitles = computed(() => {
  if (selectedFilter.value === 'all') {
    return store.subtitles
  }

  return store.subtitles.filter((subtitle, index) => {
    const hasText = subtitle.text.trim() !== ''
    const hasTranslation = subtitle.translationText.trim() !== ''

    switch (selectedFilter.value) {
      case 'textEmpty':
        return !hasText
      case 'textOnly':
        return hasText && !hasTranslation
      case 'translationOnly':
        return !hasText && hasTranslation
      case 'bothEmpty':
        return !hasText && !hasTranslation
      case 'hasContent':
        return hasText || hasTranslation
      case 'timestampError': {
        const prevSubtitle = index > 0 ? store.subtitles[index - 1] : undefined
        const nextSubtitle = index < store.subtitles.length - 1 ? store.subtitles[index + 1] : undefined
        return hasTimestampError(subtitle, prevSubtitle, nextSubtitle)
      }
      default:
        return true
    }
  })
})

// 限制预览显示前5条
const previewItems = computed(() => {
  return previewSubtitles.value.slice(0, 5)
})

// 计算总的筛选结果数量
const filteredCount = computed(() => {
  return previewSubtitles.value.length
})

// 判断是否有更多结果
const hasMoreResults = computed(() => {
  return previewSubtitles.value.length > 5
})

// 文本截断显示
function truncateText(text: string, maxLength: number): string {
  if (!text || text.length <= maxLength) {
    return text
  }
  return text.substring(0, maxLength) + '...'
}

// 格式化时间戳显示
function formatTimeForPreview(time: string): string {
  return time
}

// 时间戳错误的视觉指示
function getErrorIndicator(_subtitle: Subtitle): string {
  return '时间戳错误'
}

// 跳转到指定字幕
function jumpToSubtitle(subtitle: Subtitle): void {
  emit('jump-to', subtitle)

  // 关闭弹窗
  emit('update:isOpen', false)
}
</script>

<template>
  <BaseDialog :isOpen="props.isOpen" title="筛选字幕" :showClose="true" @close="handleClose" @update:isOpen="emit('update:isOpen', $event)">
    <div class="flex gap-4 py-2 h-[50vh]">
      <div class="w-80 space-y-2 overflow-y-auto pr-2">
        <div
          v-for="option in filterOptions"
          :key="option.value"
          class="flex items-start gap-3 p-3 rounded-lg border cursor-pointer transition-all"
          :class="{
            'border-primary-500 bg-primary-900/20': selectedFilter === option.value,
            'border-gray-700 hover:border-gray-600': selectedFilter !== option.value,
          }"
          @click="selectedFilter = option.value"
        >
          <input v-model="selectedFilter" :value="option.value" type="radio" :name="'filter-option'" class="mt-1" />
          <div class="flex-1">
            <div class="flex items-center justify-between mb-1">
              <span class="font-medium text-white">{{ option.label }}</span>
              <span class="text-sm text-gray-400 bg-gray-800 px-2 py-1 rounded">
                {{ filterCounts[option.value] }}
              </span>
            </div>
            <p class="text-xs text-gray-400 leading-relaxed">{{ option.description }}</p>
          </div>
        </div>
      </div>
      <div class="flex-1 bg-gray-800/50 border border-gray-700 rounded-lg p-4 flex flex-col">
        <div class="flex items-center justify-between mb-4">
          <h4 class="font-medium text-white">筛选预览</h4>
          <span class="text-sm text-gray-400">{{ previewItems.length }} / {{ filteredCount }} 条</span>
        </div>

        <div v-if="previewItems.length === 0" class="flex-1 flex items-center justify-center text-gray-400">
          <div class="text-center">
            <div class="text-lg mb-2">📝</div>
            <div>暂无匹配结果</div>
          </div>
        </div>

        <div v-else class="flex-1 space-y-3 overflow-y-auto">
          <div
            v-for="subtitle in previewItems"
            :key="subtitle.uuid"
            class="p-3 bg-gray-900/50 rounded-lg border border-gray-600 hover:border-gray-500 cursor-pointer transition-all"
            @click="jumpToSubtitle(subtitle)"
          >
            <div class="flex items-center justify-between mb-2">
              <span class="text-xs text-gray-400">#{{ subtitle.id }}</span>
              <span class="text-xs text-gray-400"
                >{{ formatTimeForPreview(subtitle.startTime) }} → {{ formatTimeForPreview(subtitle.endTime) }}</span
              >
            </div>
            <div class="space-y-1">
              <div class="text-sm text-gray-200">原文: {{ truncateText(subtitle.text, 30) || '(空)' }}</div>
              <div class="text-sm text-gray-300">翻译: {{ truncateText(subtitle.translationText, 30) || '(空)' }}</div>
            </div>
            <div v-if="selectedFilter === 'timestampError'" class="mt-2">
              <span class="text-xs px-2 py-1 rounded bg-red-900/30 text-red-400">
                {{ getErrorIndicator(subtitle) }}
              </span>
            </div>
          </div>

          <div v-if="hasMoreResults" class="text-center text-gray-400 text-sm py-2">
            还有 {{ filteredCount - previewItems.length }} 条...
          </div>
        </div>
      </div>
    </div>

    <template #footer>
      <UButton color="primary" @click="handleApplyFilter"> 应用筛选 </UButton>
    </template>
  </BaseDialog>
</template>

<script setup lang="ts">
import { useSubtitleStore } from '~/stores/subtitleStore'
import type { Subtitle } from '~/types/subtitle'
import SubtitleJumpDialog from './SubtitleJumpDialog.vue'
import SubtitleFilterDialog from './SubtitleFilterDialog.vue'
import { isEmpty } from 'lodash-es'

const props = defineProps<{
  onExport: () => void
  onFindReplace: () => void
}>()

const store = useSubtitleStore()
const showJumpDialog = ref(false)
const showFilterDialog = ref(false)

function handleJumpTo(subtitle: Subtitle) {
  const el = document.getElementById(`subtitle-item-${subtitle.uuid}`)
  if (el) {
    el.scrollIntoView({ behavior: 'smooth', block: 'center' })
  }
}

// 获取当前筛选的显示文本
const currentFilterText = computed(() => {
  switch (store.currentFilter) {
    case 'all':
      return '全部'
    case 'textEmpty':
      return '文本为空'
    case 'textOnly':
      return '仅有文本'
    case 'translationOnly':
      return '仅有翻译'
    case 'bothEmpty':
      return '都为空'
    case 'hasContent':
      return '有内容'
    default:
      return '全部'
  }
})

// 判断是否有激活的筛选
const hasActiveFilter = computed(() => store.currentFilter !== 'all')
</script>

<template>
  <div>
    <div class="sticky top-0 z-20 bg-gray-800/80 backdrop-blur-sm shadow-sm px-4 py-2">
      <div class="max-w-screen-xl mx-auto flex items-center gap-2">
        <UButton icon="i-heroicons-arrow-uturn-left" size="sm" :disabled="!store.canUndo" @click="store.undo()"> </UButton>
        <UButton icon="i-heroicons-arrow-uturn-right" size="sm" :disabled="!store.canRedo" @click="store.redo()"> </UButton>
        <UButton icon="i-heroicons-magnifying-glass" size="sm" :disabled="isEmpty(store.subtitles)" @click="props.onFindReplace">
          查找/替换
        </UButton>
        <UButton icon="i-heroicons-arrow-down-circle" size="sm" :disabled="isEmpty(store.subtitles)" @click="showJumpDialog = true">
          跳转
        </UButton>
        <!-- 筛选按钮 -->
        <UButton
          icon="i-heroicons-funnel"
          size="sm"
          :disabled="isEmpty(store.subtitles)"
          :color="hasActiveFilter ? 'primary' : 'gray'"
          @click="showFilterDialog = true"
        >
          筛选{{ hasActiveFilter ? `(${currentFilterText})` : '' }}
        </UButton>
      </div>
    </div>
    <SubtitleJumpDialog v-model:isOpen="showJumpDialog" :subtitles="store.filteredSubtitles" @jump-to="handleJumpTo" />
    <SubtitleFilterDialog v-model:isOpen="showFilterDialog" @jump-to="handleJumpTo" />
  </div>
</template>

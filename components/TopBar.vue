<script setup lang="ts">
import OnlineLrcDialog from '~/components/OnlineLrcDialog.vue'
import { useTopBarLogic } from '~/composables/ui/useTopBarLogic'

const {
  isOnlineLrcModalOpen,
  audioFileInputRef,
  hasValidChanges,
  triggerAudioFileInput,
  handleAudioFileUpload,
  handleFinish,
  handleGetLrc,
  handleApplyOnlineLrc,
  subtitleFileInputRef,
  triggerSubtitleFileInput,
  handleSubtitleFileUpload,
} = useTopBarLogic()
</script>

<template>
  <header class="w-full border-b border-gray-300 border-solid bg-[#181e29]">
    <div class="flex justify-between items-center px-6 py-3">
      <div class="text-lg font-semibold text-white">字幕编辑器</div>
      <div class="flex gap-2">
        <!-- 在线歌词 -->
        <UButton color="sky" icon="i-heroicons-cloud" @click="handleGetLrc"> 在线歌词 </UButton>

        <!-- 合并字幕导入按钮 -->
        <input ref="subtitleFileInputRef" type="file" accept=".vtt,.srt,.lrc" class="hidden" @change="handleSubtitleFileUpload" />
        <UButton icon="i-heroicons-document-arrow-up" @click="triggerSubtitleFileInput"> 导入字幕 </UButton>

        <!-- 获取媒体文件 -->
        <input ref="audioFileInputRef" type="file" accept="audio/*" class="hidden" @change="handleAudioFileUpload" />
        <UButton color="indigo" icon="i-heroicons-film" @click="triggerAudioFileInput"> 导入媒体 </UButton>

        <!-- 有音频和歌词时才可以点击完成按钮 -->
        <UButton color="emerald" :disabled="!hasValidChanges" icon="i-heroicons-check-circle" @click="handleFinish">完成</UButton>
      </div>
    </div>
  </header>

  <!-- 歌词获取弹窗 -->
  <OnlineLrcDialog
    :isOpen="isOnlineLrcModalOpen"
    title="获取歌词"
    @close="isOnlineLrcModalOpen = false"
    @update:isOpen="isOnlineLrcModalOpen = $event"
    @apply="handleApplyOnlineLrc"
  />
</template>

<style scoped></style>

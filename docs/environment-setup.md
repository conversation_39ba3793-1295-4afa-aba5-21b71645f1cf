# 环境配置指南

## 必需的环境变量

请在项目根目录创建 `.env` 文件，并添加以下环境变量：

```bash
# 火山引擎语音识别服务配置
VOLCENGINE_ACCESS_KEY=your_volcengine_access_key_here
VOLCENGINE_SECRET_KEY=your_volcengine_secret_key_here

# 火山引擎方舟平台配置 (DeepSeek V3)
ARK_API_KEY=your_ark_api_key_here
DEEPSEEK_MODEL_ID=your_deepseek_model_id_here

# Nuxt 公共配置
NUXT_PUBLIC_API_BASE=/api
```

## 获取 API Key 的步骤

### 1. 火山引擎方舟平台 API Key

1. 访问 [火山引擎方舟平台](https://ark.cn-beijing.volces.com/)
2. 注册并登录账户
3. 进入 **API 管理** 页面
4. 创建新的 API Key
5. 复制 API Key 并设置为 `ARK_API_KEY` 环境变量

### 2. DeepSeek 模型 ID

1. 在火山引擎方舟平台中，进入 **模型服务** 页面
2. 找到 DeepSeek V3 模型
3. 查看模型的 **端点 ID** 或 **模型 ID**
4. 将其设置为 `DEEPSEEK_MODEL_ID` 环境变量

### 3. 火山引擎语音识别（可选）

如果需要使用语音识别功能：

1. 访问 [火山引擎控制台](https://console.volcengine.com/)
2. 开通语音技术服务
3. 获取 Access Key 和 Secret Key
4. 分别设置为 `VOLCENGINE_ACCESS_KEY` 和 `VOLCENGINE_SECRET_KEY`

## 配置验证

启动项目后，可以通过以下方式验证配置：

1. 访问 `/deepseek` 页面
2. 尝试发送一条测试消息
3. 查看浏览器控制台和服务器日志是否有错误信息

## 注意事项

- **安全性**：不要将 `.env` 文件提交到版本控制系统
- **权限**：确保 API Key 有足够的权限调用相应的服务
- **配额**：注意监控 API 使用量，避免超出配额限制
- **网络**：确保服务器能够访问火山引擎的 API 端点

## 故障排除

### 常见错误

1. **401 未授权**：检查 API Key 是否正确
2. **404 模型未找到**：检查模型 ID 是否正确
3. **403 权限不足**：确认 API Key 有访问该模型的权限
4. **500 服务器错误**：查看服务器日志获取详细错误信息

### 调试方法

1. 检查环境变量是否正确加载：
   ```bash
   # 在 Nuxt 服务器端代码中
   console.log('ARK_API_KEY:', process.env.ARK_API_KEY ? '已设置' : '未设置')
   console.log('DEEPSEEK_MODEL_ID:', process.env.DEEPSEEK_MODEL_ID ? '已设置' : '未设置')
   ```

2. 使用 curl 直接测试 API：
   ```bash
   curl -X POST https://ark.cn-beijing.volces.com/api/v3/chat/completions \
     -H "Authorization: Bearer YOUR_API_KEY" \
     -H "Content-Type: application/json" \
     -d '{
       "model": "YOUR_MODEL_ID",
       "messages": [{"role": "user", "content": "Hello"}]
     }'
   ``` 
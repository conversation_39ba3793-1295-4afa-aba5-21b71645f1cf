# VideoLingo 时间戳对齐实现详解

## 概述

VideoLingo 项目的时间戳对齐是视频翻译流程中的关键环节，负责将翻译后的句子与原始音频的精确时间戳进行匹配。该系统采用了**多层次、字符级精确匹配算法**，确保翻译字幕与音频的完美同步。

## 整体架构

```
音频输入 → WhisperX转录 → 词级时间戳 → 字符串构建 → 句子匹配 → 时间戳映射 → 输出字幕
```

## 核心实现流程

### 1. 词级时间戳获取阶段

#### 1.1 WhisperX 双阶段处理

```python
# core/asr_backend/whisperX_local.py

def transcribe_audio(raw_audio_file, vocal_audio_file, start, end):
    # -------------------------
    # 阶段1: 使用原始音频进行转录
    # -------------------------
    model = whisperx.load_model(model_name, device, compute_type=compute_type, 
                               language=whisper_language, vad_options=vad_options, 
                               asr_options=asr_options, download_root=MODEL_DIR)
    
    result = model.transcribe(raw_audio_segment, batch_size=batch_size, print_progress=True)
    
    # -------------------------  
    # 阶段2: 使用人声音频进行时间戳对齐
    # -------------------------
    model_a, metadata = whisperx.load_align_model(language_code=result["language"], device=device)
    result = whisperx.align(result["segments"], model_a, metadata, vocal_audio_segment, 
                           device, return_char_alignments=False)
    
    # 调整时间戳偏移量
    for segment in result['segments']:
        segment['start'] += start
        segment['end'] += start
        for word in segment['words']:
            if 'start' in word:
                word['start'] += start
            if 'end' in word:
                word['end'] += start
    
    return result
```

**关键特点：**
- 使用原始音频获得文本内容
- 使用分离后的人声音频获得精确时间戳
- 支持分段处理，自动调整时间偏移

### 2. 核心对齐算法实现

#### 2.1 字符串构建与位置映射

```python
# core/_6_gen_sub.py

def get_sentence_timestamps(df_words, df_sentences):
    time_stamp_list = []
    
    # ------------
    # 构建完整字符串和位置映射表
    # ------------
    full_words_str = ''
    position_to_word_idx = {}
    
    for idx, word in enumerate(df_words['text']):
        clean_word = remove_punctuation(word.lower())
        start_pos = len(full_words_str)
        full_words_str += clean_word
        
        # 建立每个字符位置到词索引的映射
        for pos in range(start_pos, len(full_words_str)):
            position_to_word_idx[pos] = idx
```

**数据结构示例：**
```
词列表: ["Hello", "world", "this", "is", "a", "test"]
完整字符串: "helloworldthisisatest"
位置映射: {0:0, 1:0, 2:0, 3:0, 4:0, 5:1, 6:1, 7:1, 8:1, 9:1, ...}
```

#### 2.2 句子匹配算法

```python
def get_sentence_timestamps(df_words, df_sentences):
    # ... 前面的代码 ...
    
    current_pos = 0
    for idx, sentence in df_sentences['Source'].items():
        # 清理句子：去标点、转小写、去空格
        clean_sentence = remove_punctuation(sentence.lower()).replace(" ", "")
        sentence_len = len(clean_sentence)
        
        match_found = False
        # 滑动窗口搜索匹配
        while current_pos <= len(full_words_str) - sentence_len:
            if full_words_str[current_pos:current_pos+sentence_len] == clean_sentence:
                # 找到匹配，获取起始和结束词的索引
                start_word_idx = position_to_word_idx[current_pos]
                end_word_idx = position_to_word_idx[current_pos + sentence_len - 1]
                
                # 提取对应的时间戳
                time_stamp_list.append((
                    float(df_words['start'][start_word_idx]),
                    float(df_words['end'][end_word_idx])
                ))
                
                current_pos += sentence_len
                match_found = True
                break
            current_pos += 1
            
        # 错误处理：未找到匹配
        if not match_found:
            print(f"\n⚠️ Warning: No exact match found for sentence: {sentence}")
            show_difference(clean_sentence, 
                          full_words_str[current_pos:current_pos+len(clean_sentence)])
            print("\nOriginal sentence:", df_sentences['Source'][idx])
            raise ValueError("❎ No match found for sentence.")
    
    return time_stamp_list
```

#### 2.3 文本预处理函数

```python
def remove_punctuation(text):
    """移除标点符号和多余空格"""
    text = re.sub(r'\s+', ' ', text)      # 多个空格合并为一个
    text = re.sub(r'[^\w\s]', '', text)   # 移除标点符号
    return text.strip()

def show_difference(str1, str2):
    """显示两个字符串的差异位置"""
    min_len = min(len(str1), len(str2))
    diff_positions = []
    
    for i in range(min_len):
        if str1[i] != str2[i]:
            diff_positions.append(i)
    
    if len(str1) != len(str2):
        diff_positions.extend(range(min_len, max(len(str1), len(str2))))
    
    print("Difference positions:")
    print(f"Expected sentence: {str1}")
    print(f"Actual match: {str2}")
    print("Position markers: " + "".join("^" if i in diff_positions else " " 
                                        for i in range(max(len(str1), len(str2)))))
    print(f"Difference indices: {diff_positions}")
```

### 3. 时间戳后处理

#### 3.1 主对齐函数

```python
def align_timestamp(df_text, df_translate, subtitle_output_configs: list, 
                   output_dir: str, for_display: bool = True):
    """对齐时间戳并添加新的时间戳列到 df_translate"""
    df_trans_time = df_translate.copy()

    # 处理时间戳 ⏰
    time_stamp_list = get_sentence_timestamps(df_text, df_translate)
    df_trans_time['timestamp'] = time_stamp_list
    df_trans_time['duration'] = df_trans_time['timestamp'].apply(lambda x: x[1] - x[0])

    # 消除间隙 🕳️
    for i in range(len(df_trans_time)-1):
        delta_time = df_trans_time.loc[i+1, 'timestamp'][0] - df_trans_time.loc[i, 'timestamp'][1]
        if 0 < delta_time < 1:  # 小于1秒的间隙
            # 将当前句子的结束时间延伸到下一句的开始时间
            df_trans_time.at[i, 'timestamp'] = (
                df_trans_time.loc[i, 'timestamp'][0], 
                df_trans_time.loc[i+1, 'timestamp'][0]
            )

    # 转换为SRT格式
    df_trans_time['timestamp'] = df_trans_time['timestamp'].apply(
        lambda x: convert_to_srt_format(x[0], x[1])
    )

    return df_trans_time
```

#### 3.2 SRT格式转换

```python
def convert_to_srt_format(start_time, end_time):
    """将时间(秒)转换为格式: hours:minutes:seconds,milliseconds"""
    def seconds_to_hmsm(seconds):
        hours = int(seconds // 3600)
        minutes = int((seconds % 3600) // 60)
        seconds = seconds % 60
        milliseconds = int(seconds * 1000) % 1000
        return f"{hours:02d}:{minutes:02d}:{int(seconds):02d},{milliseconds:03d}"

    start_srt = seconds_to_hmsm(start_time)
    end_srt = seconds_to_hmsm(end_time)
    return f"{start_srt} --> {end_srt}"
```

## 数据流和文件依赖

### 输入数据
- `df_text`：来自 `_2_CLEANED_CHUNKS.xlsx`，包含词级时间戳数据
- `df_translate`：来自 `_5_SPLIT_SUB.xlsx`，包含翻译后的句子

### 输出配置
```python
# 字幕输出配置
SUBTITLE_OUTPUT_CONFIGS = [ 
    ('src.srt', ['Source']),                    # 源语言字幕
    ('trans.srt', ['Translation']),             # 翻译字幕
    ('src_trans.srt', ['Source', 'Translation']), # 双语字幕(源+译)
    ('trans_src.srt', ['Translation', 'Source'])  # 双语字幕(译+源)
]

# 音频配音字幕配置
AUDIO_SUBTITLE_OUTPUT_CONFIGS = [
    ('src_subs_for_audio.srt', ['Source']),
    ('trans_subs_for_audio.srt', ['Translation'])
]
```

### 主执行流程
```python
def align_timestamp_main():
    # 读取数据
    df_text = pd.read_excel(_2_CLEANED_CHUNKS)
    df_text['text'] = df_text['text'].str.strip('"').str.strip()
    df_translate = pd.read_excel(_5_SPLIT_SUB)
    df_translate['Translation'] = df_translate['Translation'].apply(clean_translation)
    
    # 生成显示字幕
    align_timestamp(df_text, df_translate, SUBTITLE_OUTPUT_CONFIGS, _OUTPUT_DIR)
    
    # 生成音频配音字幕
    df_translate_for_audio = pd.read_excel(_5_REMERGED)
    df_translate_for_audio['Translation'] = df_translate_for_audio['Translation'].apply(clean_translation)
    align_timestamp(df_text, df_translate_for_audio, AUDIO_SUBTITLE_OUTPUT_CONFIGS, _AUDIO_DIR)
```

## 算法特点和优势

### 1. **精确性**
- 字符级匹配确保精确对应
- 词级时间戳提供高精度基础
- 双重验证机制避免匹配错误

### 2. **鲁棒性**
- 文本预处理消除标点符号干扰
- 滑动窗口搜索处理位置偏移
- 详细错误提示便于调试

### 3. **效率性**
- 一次性构建映射表，避免重复计算
- 线性搜索算法，时间复杂度可控
- 批量处理减少I/O开销

### 4. **灵活性**
- 支持多种字幕输出格式
- 可配置的间隙处理策略
- 模块化设计便于扩展

## 使用示例

```python
# 基本用法
from core._6_gen_sub import align_timestamp_main

# 执行时间戳对齐
align_timestamp_main()

# 自定义配置
df_text = pd.read_excel("word_timestamps.xlsx")
df_translate = pd.read_excel("translations.xlsx")

custom_configs = [('custom.srt', ['Translation'])]
result_df = align_timestamp(df_text, df_translate, custom_configs, "output/")
```

## 错误处理

### 常见问题
1. **匹配失败**：翻译文本与原文不对应
2. **时间戳缺失**：词级时间戳数据不完整
3. **编码问题**：特殊字符处理错误

### 调试方法
- 检查 `show_difference` 输出的差异位置
- 验证输入数据的完整性
- 确认文本预处理的正确性

---

## 自适应时间戳对齐方案

### 设计背景

现有实现依赖词级时间戳，但实际使用中经常只能获得句子级时间戳。为此设计了**自适应时间戳对齐方案**，能够同时支持词级和句子级时间戳。

### 整体架构扩展

```
输入数据 → 类型检测 → 策略选择 → 对齐处理 → 统一后处理 → 输出字幕
    │
    ├─ 词级时间戳 → 字符串匹配算法 (原有实现)
    │
    └─ 句子级时间戳 → 直接映射/相似度匹配/权重分配/混合策略
```

## 自适应检测与接口

### 1. 时间戳类型检测

```python
def detect_timestamp_type(df_source):
    """检测时间戳数据类型：词级 or 句子级"""
    
    # 检查数据结构特征
    if 'words' in df_source.columns:
        return 'word_level'
    
    # 通过数据量判断（词通常比句子多）
    if len(df_source) > 50:
        return 'word_level'
    elif 'start' in df_source.columns and 'end' in df_source.columns:
        return 'sentence_level'
    else:
        raise ValueError("无法识别时间戳数据格式")

def validate_timestamp_data(df_source, timestamp_type):
    """验证时间戳数据完整性"""
    
    required_columns = {
        'word_level': ['text', 'start', 'end'],
        'sentence_level': ['text', 'start', 'end']
    }
    
    missing_columns = []
    for col in required_columns[timestamp_type]:
        if col not in df_source.columns:
            missing_columns.append(col)
    
    if missing_columns:
        raise ValueError(f"缺少必要列: {missing_columns}")
    
    # 检查时间戳数据有效性
    if df_source['start'].isna().any() or df_source['end'].isna().any():
        raise ValueError("存在无效的时间戳数据")
```

### 2. 统一接口设计

```python
def align_timestamp_adaptive(df_source, df_translate, output_configs, output_dir, strategy='auto'):
    """
    自适应时间戳对齐 - 支持词级和句子级
    
    Args:
        df_source: 源数据（包含时间戳）
        df_translate: 翻译数据  
        output_configs: 输出配置
        output_dir: 输出目录
        strategy: 对齐策略 ('auto', 'word_level', 'sentence_level', 'direct', 'similarity', 'weighted', 'hybrid')
    
    Returns:
        DataFrame: 包含时间戳的翻译数据
    """
    
    # 验证输入数据
    if strategy == 'auto':
        timestamp_type = detect_timestamp_type(df_source)
        validate_timestamp_data(df_source, timestamp_type)
    else:
        timestamp_type = 'word_level' if strategy == 'word_level' else 'sentence_level'
        validate_timestamp_data(df_source, timestamp_type)
    
    # 选择对齐方法
    if timestamp_type == 'word_level' and strategy in ['auto', 'word_level']:
        return align_timestamp_word_level(df_source, df_translate, output_configs, output_dir)
    else:
        return align_timestamp_sentence_level(df_source, df_translate, output_configs, output_dir, strategy)

def align_timestamp_word_level(df_source, df_translate, output_configs, output_dir):
    """词级时间戳对齐（原有实现）"""
    return align_timestamp(df_source, df_translate, output_configs, output_dir)
```

## 句子级时间戳对齐实现

### 1. 策略选择机制

```python
def align_timestamp_sentence_level(df_source, df_translate, output_configs, output_dir, strategy='auto'):
    """句子级时间戳对齐"""
    
    df_trans_time = df_translate.copy()
    
    # 自动选择策略
    if strategy == 'auto':
        alignment_strategy = determine_alignment_strategy(df_source, df_translate)
    else:
        alignment_strategy = strategy
    
    # 执行对齐
    if alignment_strategy == 'direct':
        timestamp_list = direct_sentence_mapping(df_source, df_translate)
    elif alignment_strategy == 'similarity':
        timestamp_list = similarity_sentence_matching(df_source, df_translate)
    elif alignment_strategy == 'weighted':
        timestamp_list = length_weighted_alignment(df_source, df_translate)
    elif alignment_strategy == 'hybrid':
        timestamp_list = hybrid_sentence_alignment(df_source, df_translate)
    else:
        timestamp_list = smart_sentence_alignment(df_source, df_translate)
    
    df_trans_time['timestamp'] = timestamp_list
    df_trans_time['duration'] = df_trans_time['timestamp'].apply(lambda x: x[1] - x[0])
    
    return post_process_timestamps(df_trans_time, output_configs, output_dir)

def determine_alignment_strategy(df_source, df_translate):
    """智能确定对齐策略"""
    
    source_count = len(df_source)
    translate_count = len(df_translate)
    ratio = translate_count / source_count if source_count > 0 else 1
    
    # 句子数量相等或接近，优先直接映射
    if 0.9 <= ratio <= 1.1:
        return 'direct'
    
    # 翻译句子较多（可能被拆分），使用长度权重
    elif ratio > 1.2:
        return 'weighted'
    
    # 差异较大，使用混合策略
    else:
        return 'hybrid'
```

### 2. 直接映射策略

```python
def direct_sentence_mapping(df_source, df_translate):
    """直接一对一句子映射"""
    
    source_count = len(df_source)
    translate_count = len(df_translate)
    
    timestamp_list = []
    
    if source_count == translate_count:
        # 完全一对一映射
        for i in range(translate_count):
            timestamp_list.append((
                float(df_source.iloc[i]['start']),
                float(df_source.iloc[i]['end'])
            ))
    
    elif translate_count > source_count:
        # 翻译句子更多，需要分配时间
        for i in range(translate_count):
            source_idx = min(i, source_count - 1)
            source_start = df_source.iloc[source_idx]['start']
            source_end = df_source.iloc[source_idx]['end']
            
            # 在源句子时间范围内等分
            sentences_in_source = min(translate_count - i, translate_count - source_idx * (translate_count // source_count))
            duration_per_sentence = (source_end - source_start) / max(sentences_in_source, 1)
            
            start_time = source_start + (i % (translate_count // source_count)) * duration_per_sentence
            end_time = start_time + duration_per_sentence
            
            timestamp_list.append((float(start_time), float(end_time)))
    
    else:
        # 源句子更多，需要合并时间
        source_per_translate = source_count / translate_count
        
        for i in range(translate_count):
            start_source_idx = int(i * source_per_translate)
            end_source_idx = int((i + 1) * source_per_translate) - 1
            end_source_idx = min(end_source_idx, source_count - 1)
            
            start_time = df_source.iloc[start_source_idx]['start']
            end_time = df_source.iloc[end_source_idx]['end']
            
            timestamp_list.append((float(start_time), float(end_time)))
    
    return timestamp_list
```

### 3. 相似度匹配策略

```python
from difflib import SequenceMatcher

def similarity_sentence_matching(df_source, df_translate):
    """基于文本相似度的句子匹配"""
    
    def text_similarity(text1, text2):
        """计算文本相似度"""
        clean_text1 = remove_punctuation(text1.lower())
        clean_text2 = remove_punctuation(text2.lower())
        return SequenceMatcher(None, clean_text1, clean_text2).ratio()
    
    timestamp_list = []
    used_source_indices = set()
    
    for _, translate_row in df_translate.iterrows():
        translate_text = translate_row['Source']  # 使用源文本进行匹配
        
        best_match_idx = -1
        best_similarity = 0
        
        # 寻找最佳匹配的源句子
        for idx, source_row in df_source.iterrows():
            if idx in used_source_indices:
                continue
                
            similarity = text_similarity(source_row['text'], translate_text)
            
            if similarity > best_similarity and similarity > 0.6:  # 相似度阈值
                best_similarity = similarity
                best_match_idx = idx
        
        if best_match_idx != -1:
            timestamp_list.append((
                float(df_source.iloc[best_match_idx]['start']),
                float(df_source.iloc[best_match_idx]['end'])
            ))
            used_source_indices.add(best_match_idx)
        else:
            # 如果没找到匹配，使用位置估算
            estimated_timestamp = estimate_timestamp_by_position(df_source, len(timestamp_list), len(df_translate))
            timestamp_list.append(estimated_timestamp)
    
    return timestamp_list

def estimate_timestamp_by_position(df_source, current_index, total_count):
    """基于位置估算时间戳"""
    
    if len(df_source) == 0:
        return (0.0, 2.0)  # 默认2秒
    
    # 计算在源数据中的相对位置
    relative_position = current_index / max(total_count - 1, 1)
    source_index = int(relative_position * (len(df_source) - 1))
    source_index = min(source_index, len(df_source) - 1)
    
    return (
        float(df_source.iloc[source_index]['start']),
        float(df_source.iloc[source_index]['end'])
    )
```

### 4. 长度权重分配策略

```python
def length_weighted_alignment(df_source, df_translate):
    """基于长度权重的时间戳分配"""
    
    # 计算翻译文本的长度权重
    translate_lengths = [len(str(text).strip()) for text in df_translate['Translation']]
    total_translate_length = sum(translate_lengths)
    
    if total_translate_length == 0:
        # 如果没有长度信息，退回到直接映射
        return direct_sentence_mapping(df_source, df_translate)
    
    timestamp_list = []
    current_source_idx = 0
    current_time_offset = 0
    
    for i, translate_length in enumerate(translate_lengths):
        # 计算当前翻译句子的时间权重
        length_ratio = translate_length / total_translate_length
        
        if current_source_idx < len(df_source):
            # 获取当前源句子的时间信息
            source_start = df_source.iloc[current_source_idx]['start']
            source_end = df_source.iloc[current_source_idx]['end']
            source_duration = source_end - source_start
            
            # 计算分配的时间长度
            allocated_duration = source_duration * length_ratio
            
            # 确定实际的开始和结束时间
            actual_start = source_start + current_time_offset
            actual_end = actual_start + allocated_duration
            
            # 检查是否超出当前源句子的范围
            if actual_end > source_end:
                # 结束当前源句子，移动到下一个
                actual_end = source_end
                current_source_idx += 1
                current_time_offset = 0
            else:
                current_time_offset = actual_end - source_start
            
            timestamp_list.append((float(actual_start), float(actual_end)))
        
        else:
            # 源句子用完了，使用最后一个时间戳
            if timestamp_list:
                last_end = timestamp_list[-1][1]
                timestamp_list.append((last_end, last_end + 2.0))
            else:
                timestamp_list.append((0.0, 2.0))
    
    return timestamp_list
```

### 5. 混合策略实现

```python
def hybrid_sentence_alignment(df_source, df_translate):
    """混合对齐策略：结合多种方法"""
    
    # 第一步：使用相似度匹配找到高置信度匹配
    high_confidence_matches = []
    low_confidence_items = []
    
    for i, translate_row in df_translate.iterrows():
        best_match = find_best_similarity_match(df_source, translate_row, threshold=0.8)
        
        if best_match is not None:
            high_confidence_matches.append((i, best_match))
        else:
            low_confidence_items.append(i)
    
    # 第二步：为高置信度匹配分配时间戳
    timestamp_list = [None] * len(df_translate)
    used_source_indices = set()
    
    for translate_idx, source_idx in high_confidence_matches:
        timestamp_list[translate_idx] = (
            float(df_source.iloc[source_idx]['start']),
            float(df_source.iloc[source_idx]['end'])
        )
        used_source_indices.add(source_idx)
    
    # 第三步：为低置信度项使用位置插值
    remaining_sources = df_source[~df_source.index.isin(used_source_indices)]
    
    for translate_idx in low_confidence_items:
        # 基于相邻高置信度匹配进行插值
        estimated_timestamp = interpolate_timestamp(
            timestamp_list, translate_idx, remaining_sources
        )
        timestamp_list[translate_idx] = estimated_timestamp
    
    return timestamp_list

def find_best_similarity_match(df_source, translate_row, threshold=0.8):
    """寻找最佳相似度匹配"""
    
    translate_text = translate_row['Source']
    best_match_idx = None
    best_similarity = 0
    
    for idx, source_row in df_source.iterrows():
        similarity = SequenceMatcher(
            None, 
            remove_punctuation(source_row['text'].lower()), 
            remove_punctuation(translate_text.lower())
        ).ratio()
        
        if similarity > best_similarity and similarity >= threshold:
            best_similarity = similarity
            best_match_idx = idx
    
    return best_match_idx

def interpolate_timestamp(timestamp_list, target_index, remaining_sources):
    """插值计算时间戳"""
    
    # 寻找前后最近的已分配时间戳
    prev_timestamp = None
    next_timestamp = None
    
    for i in range(target_index - 1, -1, -1):
        if timestamp_list[i] is not None:
            prev_timestamp = timestamp_list[i]
            break
    
    for i in range(target_index + 1, len(timestamp_list)):
        if timestamp_list[i] is not None:
            next_timestamp = timestamp_list[i]
            break
    
    # 基于前后时间戳插值
    if prev_timestamp and next_timestamp:
        # 线性插值
        ratio = 0.5  # 简单取中点
        start_time = prev_timestamp[1] + ratio * (next_timestamp[0] - prev_timestamp[1])
        end_time = start_time + (next_timestamp[1] - next_timestamp[0]) * ratio
        return (start_time, end_time)
    
    elif prev_timestamp:
        # 只有前面的时间戳，向后延续
        duration = prev_timestamp[1] - prev_timestamp[0]
        return (prev_timestamp[1], prev_timestamp[1] + duration)
    
    elif next_timestamp:
        # 只有后面的时间戳，向前延续
        duration = next_timestamp[1] - next_timestamp[0]
        return (next_timestamp[0] - duration, next_timestamp[0])
    
    else:
        # 都没有，使用剩余源数据估算
        if len(remaining_sources) > 0:
            source_row = remaining_sources.iloc[0]
            return (float(source_row['start']), float(source_row['end']))
        else:
            return (0.0, 2.0)  # 默认值
```

## 统一后处理

```python
def post_process_timestamps(df_translate, output_configs, output_dir):
    """统一的时间戳后处理"""
    
    # 第一步：排序检查
    df_translate = df_translate.sort_values(by='timestamp', key=lambda x: x.apply(lambda t: t[0]))
    df_translate = df_translate.reset_index(drop=True)
    
    # 第二步：消除重叠和间隙
    for i in range(len(df_translate) - 1):
        current_start, current_end = df_translate.iloc[i]['timestamp']
        next_start, next_end = df_translate.iloc[i + 1]['timestamp']
        
        # 处理重叠
        if current_end > next_start:
            # 调整当前句子的结束时间
            mid_point = (current_end + next_start) / 2
            df_translate.at[i, 'timestamp'] = (current_start, mid_point)
            df_translate.at[i + 1, 'timestamp'] = (mid_point, next_end)
        
        # 处理小间隙（小于1秒）
        elif 0 < next_start - current_end < 1:
            # 延长当前句子到下一句开始
            df_translate.at[i, 'timestamp'] = (current_start, next_start)
    
    # 第三步：转换为SRT格式
    df_translate['timestamp'] = df_translate['timestamp'].apply(
        lambda x: convert_to_srt_format(x[0], x[1])
    )
    
    # 第四步：文本清理
    if 'Translation' in df_translate.columns:
        df_translate['Translation'] = df_translate['Translation'].apply(
            lambda x: re.sub(r'[，。]', ' ', str(x)).strip()
        )
    
    # 第五步：生成字幕文件
    if output_dir and output_configs:
        generate_subtitle_files(df_translate, output_configs, output_dir)
    
    return df_translate

def generate_subtitle_files(df_translate, output_configs, output_dir):
    """生成各种格式的字幕文件"""
    
    os.makedirs(output_dir, exist_ok=True)
    
    def generate_subtitle_string(df, columns):
        subtitle_parts = []
        for i, row in df.iterrows():
            subtitle_parts.append(f"{i+1}")
            subtitle_parts.append(row['timestamp'])
            
            for col in columns:
                if col in row and pd.notna(row[col]):
                    subtitle_parts.append(str(row[col]).strip())
            
            subtitle_parts.append("")  # 空行分隔
        
        return '\n'.join(subtitle_parts).strip()
    
    for filename, columns in output_configs:
        subtitle_str = generate_subtitle_string(df_translate, columns)
        with open(os.path.join(output_dir, filename), 'w', encoding='utf-8') as f:
            f.write(subtitle_str)
```

## 完整使用示例

```python
# ------------
# 基本使用
# ------------

# 自动检测并对齐
result = align_timestamp_adaptive(
    df_source=df_text, 
    df_translate=df_translate,
    output_configs=SUBTITLE_OUTPUT_CONFIGS,
    output_dir="output/",
    strategy='auto'
)

# ------------
# 指定策略使用
# ------------

# 强制使用句子级直接映射
result = align_timestamp_adaptive(
    df_source=df_sentences,
    df_translate=df_translate, 
    output_configs=SUBTITLE_OUTPUT_CONFIGS,
    output_dir="output/",
    strategy='direct'
)

# 使用相似度匹配（适合句子顺序有变化的情况）
result = align_timestamp_adaptive(
    df_source=df_sentences,
    df_translate=df_translate,
    output_configs=SUBTITLE_OUTPUT_CONFIGS, 
    output_dir="output/",
    strategy='similarity'
)

# 使用长度权重分配（适合翻译文本被拆分的情况）
result = align_timestamp_adaptive(
    df_source=df_sentences,
    df_translate=df_translate,
    output_configs=SUBTITLE_OUTPUT_CONFIGS,
    output_dir="output/", 
    strategy='weighted'
)

# 使用混合策略（最智能，适合复杂情况）
result = align_timestamp_adaptive(
    df_source=df_sentences,
    df_translate=df_translate,
    output_configs=SUBTITLE_OUTPUT_CONFIGS,
    output_dir="output/",
    strategy='hybrid'
)

# ------------
# 批量处理示例
# ------------

def batch_align_timestamps(input_files, strategy='auto'):
    """批量处理多个文件的时间戳对齐"""
    
    results = []
    
    for source_file, translate_file in input_files:
        try:
            df_source = pd.read_excel(source_file)
            df_translate = pd.read_excel(translate_file)
            
            result = align_timestamp_adaptive(
                df_source=df_source,
                df_translate=df_translate,
                output_configs=SUBTITLE_OUTPUT_CONFIGS,
                output_dir=f"output/{os.path.basename(source_file)}/",
                strategy=strategy
            )
            
            results.append({
                'source_file': source_file,
                'translate_file': translate_file, 
                'success': True,
                'result': result
            })
            
        except Exception as e:
            results.append({
                'source_file': source_file,
                'translate_file': translate_file,
                'success': False,
                'error': str(e)
            })
    
    return results
```

## 方案特点和优势

### 1. **自适应性**
- 自动检测时间戳数据类型
- 智能选择最适合的对齐策略
- 支持多种fallback机制

### 2. **灵活性**
- 支持手动指定对齐策略
- 可配置的阈值和参数
- 易于扩展新的对齐算法

### 3. **鲁棒性**
- 多重验证确保数据完整性
- 详细的错误处理和恢复机制
- 兼容各种数据格式

### 4. **兼容性**
- 完全兼容现有词级时间戳对齐
- 保持相同的输出格式
- 无缝集成到现有流程

### 5. **可扩展性**
- 模块化设计便于添加新策略
- 支持自定义对齐算法
- 灵活的配置系统

---

*此扩展方案为VideoLingo项目提供了完整的自适应时间戳对齐解决方案，能够处理各种实际应用场景中的时间戳对齐需求。*

---

*此文档详细说明了 VideoLingo 项目中时间戳对齐的完整实现机制，包含所有关键代码和处理流程。* 
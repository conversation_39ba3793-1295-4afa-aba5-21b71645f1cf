# VideoLingo 逐行翻译核心逻辑

VideoLingo 项目的逐行翻译核心逻辑主要在 `core/translate_lines.py` 文件中实现，并依赖 `core/prompts.py` 生成的提示词。其核心采用创新的“两步翻译策略”来确保翻译的准确性和表达的自然流畅性。

## 一、 两步翻译策略

核心的翻译逻辑在 `core/translate_lines.py` 的 `translate_lines` 函数中实现：

```python
# core/translate_lines.py
def translate_lines(lines, previous_content_prompt, after_cotent_prompt, things_to_note_prompt, summary_prompt, index = 0):
    shared_prompt = generate_shared_prompt(previous_content_prompt, after_cotent_prompt, summary_prompt, things_to_note_prompt)

    # Retry translation if the length of the original text and the translated text are not the same, or if the specified key is missing
    def retry_translation(prompt, length, step_name):
        # ... (retry logic, see Quality Assurance section) ...

    ## Step 1: Faithful to the Original Text
    prompt1 = get_prompt_faithfulness(lines, shared_prompt)
    faith_result = retry_translation(prompt1, len(lines.split(\'\\n\')), \'faithfulness\')

    for i in faith_result:
        faith_result[i][\"direct\"] = faith_result[i][\"direct\"].replace(\'\\n\', \' \')

    # If reflect_translate is False or not set, use faithful translation directly
    reflect_translate = load_key(\'reflect_translate\')
    if not reflect_translate:
        translate_result = \"\\n\".join([faith_result[i][\"direct\"].strip() for i in faith_result])
        # ... (display table) ...
        return translate_result, lines

    ## Step 2: Express Smoothly
    prompt2 = get_prompt_expressiveness(faith_result, lines, shared_prompt)
    express_result = retry_translation(prompt2, len(lines.split(\'\\n\')), \'expressiveness\')

    # ... (display table) ...

    translate_result = \"\\n\".join([express_result[i][\"free\"].replace(\'\\n\', \' \').strip() for i in express_result])

    if len(lines.split(\'\\n\')) != len(translate_result.split(\'\\n\')):\n        # ... (error handling) ...
        raise ValueError(f\'Origin ···{lines}···,\\nbut got ···{translate_result}···\')

    return translate_result, lines
```

### 步骤一：忠实翻译 (Faithfulness)

此步骤旨在准确无误地传达原文的含义，并保持术语的一致性。

- **执行模块**: `core/translate_lines.py` 中的 `translate_lines` 函数（见上述代码）。
- **提示词生成**: 调用 `core/prompts.py` 中的 `get_prompt_faithfulness` 函数。

  ````python
  # core/prompts.py
  def get_prompt_faithfulness(lines, shared_prompt):
      TARGET_LANGUAGE = load_key("target_language")
      line_splits = lines.split(\'\\n\')

      json_dict = {}
      for i, line in enumerate(line_splits, 1):
          json_dict[f\"{i}\"] = {"origin": line, "direct": f"direct {TARGET_LANGUAGE} translation {i}."}
      json_format = json.dumps(json_dict, indent=2, ensure_ascii=False)

      src_language = load_key("whisper.detected_language")
      prompt_faithfulness = f\'\'\'
  ## Role
  You are a professional Netflix subtitle translator, fluent in both {src_language} and {TARGET_LANGUAGE}, as well as their respective cultures.
  Your expertise lies in accurately understanding the semantics and structure of the original {src_language} text and faithfully translating it into {TARGET_LANGUAGE} while preserving the original meaning.

  ## Task
  We have a segment of original {src_language} subtitles that need to be directly translated into {TARGET_LANGUAGE}. These subtitles come from a specific context and may contain specific themes and terminology.

  1. Translate the original {src_language} subtitles into {TARGET_LANGUAGE} line by line
  2. Ensure the translation is faithful to the original, accurately conveying the original meaning
  3. Consider the context and professional terminology

  {shared_prompt}

  <translation_principles>
  1. Faithful to the original: Accurately convey the content and meaning of the original text, without arbitrarily changing, adding, or omitting content.
  2. Accurate terminology: Use professional terms correctly and maintain consistency in terminology.
  3. Understand the context: Fully comprehend and reflect the background and contextual relationships of the text.
  </translation_principles>

  ## INPUT
  <subtitles>
  {lines}
  </subtitles>

  ## Output in only JSON format and no other text
  ```json
  {json_format}
  ````

  Note: Start you answer with `json and end with `, do not add any other text. \'\'\' return prompt_faithfulness.strip()

  ````
  *   **角色设定**: 将 AI 模型设定为一名专业的 Netflix 字幕翻译师，精通源语言和目标语言及其文化背景。
  *   **核心任务**:
      1.  逐行将源语言字幕直接翻译成目标语言。
      2.  确保翻译忠实于原文，准确传达原始意义。
      3.  考虑上下文和专业术语。
  *   **翻译原则**: (见提示词中的 `<translation_principles>`)
  *   **共享上下文 (`shared_prompt`)**:
      ```python
      # core/prompts.py
      def generate_shared_prompt(previous_content_prompt, after_cotent_prompt, summary_prompt, things_to_note_prompt):
          return f\'\'\'### Context Information
      <previous_content>
      {previous_content_prompt}
      </previous_content>

      <subsequent_content>
      {after_content_prompt}
      </subsequent_content>

      ### Content Summary
      {summary_prompt}

      ### Points to Note
      {things_to_note_prompt}\'\'\'
      ```
      提示词中会包含由 `generate_shared_prompt` 函数生成的上下文信息，包括前文内容、后文内容、内容摘要以及需要注意的术语。
  *   **输出格式**: 要求 AI 以 JSON 格式输出，其中每个序号对应一行翻译，包含 `origin` (原文) 和 `direct` (直接翻译的文本)。
  ````

- **处理逻辑**:
  1.  生成忠实翻译的提示词。
  2.  调用大语言模型 (LLM) (`ask_gpt`) 进行翻译。
  3.  对返回结果中的换行符进行处理，替换为空格: `faith_result[i]["direct"] = faith_result[i]["direct"].replace('\\n', ' ')`。

### 步骤二：表达优化 (Expressiveness)

此步骤是可选的，当配置文件 `config.yaml` 中的 `reflect_translate` 设置为 `true` 时触发。其目标是在忠实翻译的基础上，进一步优化译文的自然流畅性，使其更符合目标语言的表达习惯和文化背景。

- **执行模块**: `core/translate_lines.py` 中的 `translate_lines` 函数（见上述代码）。
- **提示词生成**: 调用 `core/prompts.py` 中的 `get_prompt_expressiveness` 函数。

  ````python
  # core/prompts.py
  def get_prompt_expressiveness(faithfulness_result, lines, shared_prompt):
      TARGET_LANGUAGE = load_key("target_language")
      json_format = {
          key: {
              "origin": value["origin"],
              "direct": value["direct"],
              "reflect": "your reflection on direct translation",
              "free": "your free translation"
          }
          for key, value in faithfulness_result.items()
      }
      json_format = json.dumps(json_format, indent=2, ensure_ascii=False)

      src_language = load_key("whisper.detected_language")
      prompt_expressiveness = f\'\'\'
  ## Role
  You are a professional Netflix subtitle translator and language consultant.
  Your expertise lies not only in accurately understanding the original {src_language} but also in optimizing the {TARGET_LANGUAGE} translation to better suit the target language\'s expression habits and cultural background.

  ## Task
  We already have a direct translation version of the original {src_language} subtitles.
  Your task is to reflect on and improve these direct translations to create more natural and fluent {TARGET_LANGUAGE} subtitles.

  1. Analyze the direct translation results line by line, pointing out existing issues
  2. Provide detailed modification suggestions
  3. Perform free translation based on your analysis
  4. Do not add comments or explanations in the translation, as the subtitles are for the audience to read
  5. Do not leave empty lines in the free translation, as the subtitles are for the audience to read

  {shared_prompt}

  <Translation Analysis Steps>
  Please use a two-step thinking process to handle the text line by line:

  1. Direct Translation Reflection:
     - Evaluate language fluency
     - Check if the language style is consistent with the original text
     - Check the conciseness of the subtitles, point out where the translation is too wordy

  2. {TARGET_LANGUAGE} Free Translation:
     - Aim for contextual smoothness and naturalness, conforming to {TARGET_LANGUAGE} expression habits
     - Ensure it\'s easy for {TARGET_LANGUAGE} audience to understand and accept
     - Adapt the language style to match the theme (e.g., use casual language for tutorials, professional terminology for technical content, formal language for documentaries)
  </Translation Analysis Steps>

  ## INPUT
  <subtitles>
  {lines}
  </subtitles>

  ## Output in only JSON format and no other text
  ```json
  {json_format}
  ````

  Note: Start you answer with `json and end with `, do not add any other text. \'\'\' return prompt_expressiveness.strip()

  ```
  *   **输入**: 此函数接收第一步“忠实翻译”的结果 (`faith_result`)作为输入。
  *   **角色设定**: 将 AI 模型设定为一名专业的 Netflix 字幕翻译师和语言顾问。
  *   **核心任务**: (见提示词中的 "Task")
  *   **翻译分析步骤**: (见提示词中的 `<Translation Analysis Steps>`)
  *   **共享上下文**: 与忠实翻译步骤类似，提示词中包含共享的上下文信息。
  *   **输出格式**: 要求 AI 以 JSON 格式输出，其中每个序号对应一行翻译，包含 `origin` (原文), `direct` (直接翻译), `reflect` (对直接翻译的思考或反思), 和 `free` (优化后的自由翻译文本)。
  ```

- **处理逻辑**:
  1.  生成表达优化的提示词。
  2.  调用大语言模型 (LLM) (`ask_gpt`) 进行翻译优化。
  3.  最终的翻译结果取自 `free` 字段: `translate_result = "\\n".join([express_result[i]["free"].replace('\\n', ' ').strip() for i in express_result])`。

## 二、 质量保证机制

为了确保翻译的质量和稳定性，系统中集成了以下机制：

1.  **格式验证 (`valid_translate_result`)**:

    ```python
    # core/translate_lines.py
    def valid_translate_result(result: dict, required_keys: list, required_sub_keys: list):
        # Check for the required key
        if not all(key in result for key in required_keys):
            return {"status": "error", "message": f"Missing required key(s): {\', \'.join(set(required_keys) - set(result.keys()))}"}

        # Check for required sub-keys in all items
        for key in result:
            if not all(sub_key in result[key] for sub_key in required_sub_keys):
                return {"status": "error", "message": f"Missing required sub-key(s) in item {key}: {\', \'.join(set(required_sub_keys) - set(result[key].keys()))}"}

        return {"status": "success", "message": "Translation completed"}
    ```

    - 在接收到 LLM 的翻译结果后，会检查返回的 JSON 数据是否包含所有必需的键（例如，每一行的序号）和子键（例如，`direct` 或 `free`）。
    - 如果格式不正确，会标记为错误。

2.  **重试机制 (`retry_translation`)**:

    ```python
    # core/translate_lines.py
    # (within translate_lines function)
    def retry_translation(prompt, length, step_name):
        def valid_faith(response_data):
            return valid_translate_result(response_data, [str(i) for i in range(1, length+1)], [\'direct\'])
        def valid_express(response_data):
            return valid_translate_result(response_data, [str(i) for i in range(1, length+1)], [\'free\'])
        for retry in range(3):
            if step_name == \'faithfulness\':
                result = ask_gpt(prompt+retry* " ", resp_type=\'json\', valid_def=valid_faith, log_title=f\'translate_{step_name}\')
            elif step_name == \'expressiveness\':
                result = ask_gpt(prompt+retry* " ", resp_type=\'json\', valid_def=valid_express, log_title=f\'translate_{step_name}\')
            if len(lines.split(\'\\n\')) == len(result): # Check if line count matches
                return result
            if retry != 2:
                console.print(f\'[yellow]⚠️ {step_name.capitalize()} translation of block {index} failed, Retry...[/yellow]\')
        raise ValueError(f\'[red]❌ {step_name.capitalize()} translation of block {index} failed after 3 retries. Please check `output/gpt_log/error.json` for more details.[/red]\')
    ```

    - 如果在翻译过程中（无论是忠实翻译还是表达优化），返回结果的行数与输入原文的行数不匹配，或者格式验证失败，系统会触发重试机制。
    - 最多重试3次。
    - 每次重试时，会在提示词末尾添加额外的空格 (`prompt+retry* " "`)，这是一种避免 LLM 返回缓存结果的技巧。
    - 如果3次重试后仍然失败，则会抛出错误。

3.  **长度匹配**:
    ```python
    # core/translate_lines.py
    # (at the end of translate_lines function)
    if len(lines.split(\'\\n\')) != len(translate_result.split(\'\\n\')):
        console.print(Panel(f\'[red]❌ Translation of block {index} failed, Length Mismatch, Please check `output/gpt_log/translate_expressiveness.json`[/red]\'))
        raise ValueError(f\'Origin ···{lines}···,\\nbut got ···{translate_result}···\')
    ```
    - 在整个逐行翻译流程结束后，会再次校验最终输出的翻译文本行数是否与原始文本行数一致。
    - 如果不一致，会记录错误并抛出异常，防止因行数不匹配导致后续字幕处理出现问题。

## 三、 结果输出

- 如果**表达优化 (Expressiveness)** 被禁用 (`reflect_translate: false`)，则最终的翻译结果直接采用**忠实翻译 (Faithfulness)** 步骤中的 `direct` 翻译。
- 如果**表达优化 (Expressiveness)** 被启用，则最终的翻译结果采用该步骤中的 `free` 翻译。
- 翻译结果会合并成一个多行字符串，并由 `translate_lines` 函数返回。

## 四、 与整体流程的结合 (`core/_4_2_translate.py`)

`core/translate_lines.py` 中的逐行翻译逻辑是被 `core/_4_2_translate.py` (在 `translate_chunk` 函数中) 调用的。后者负责：

1.  **智能分块**: 将长文本分割成适合 LLM 处理的小块。
2.  **上下文构建**: 为每个文本块准备前文、后文、主题摘要和术语信息。
3.  **并发翻译**: 并行处理多个文本块的翻译，以提高效率。
4.  **结果整合与验证**: 将各分块的翻译结果合并，并通过相似度匹配等方式验证其正确性。

通过这种分层设计，VideoLingo 能够高效地处理长视频的翻译任务，同时通过两步翻译策略和严格的质量控制来保证最终译文的质量。

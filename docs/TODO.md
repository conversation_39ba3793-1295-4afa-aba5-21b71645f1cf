1. ✅lrc 字幕句子和翻译对齐问题，目前把翻译跟下一句对齐了
2. ✅波形可视化中句子和翻译没对上，展示有问题
3. ✅音频文件区域句子和翻译也没有对上
4. ✅https://github.com/mantas-done/subtitles?tab=readme-ov-file 看看这个项目中对于 lrc 转 srt 是怎么实现的；lrc 的格式都做了什么预处理。
5. ✅最后一句的翻译丢了，看看 lrc 转 json 是怎么做的
6. ✅生成一些 lrc 文件，用来测试
7. ✅lrc 操作优化，展示文件名
8. ✅lrc 操作优化，重新上传文件按钮
9. ✅LRC 的时间戳 00:00:01.123 格式改为 00:00:01,123
10. ✅srt 编辑 00:00:01,123 如果输入句号(. 。 ，)自动转为逗号（,）
11. ❌srt 编辑：时间戳那里加个粘贴icon按钮，方便快速粘贴时间戳
12. ✅优化 lrc 把重复的逻辑提取出来，优化函数代码。
13. ✅lrc 转换 srt 已经包含了翻译，在 srt 编辑器中把翻译展示出来。
14. ✅srt 编辑器中也有 800 变 799 的问题，精度问题按照 lrt 的方式处理
15. ✅srt 编辑器中最后一个有时间戳却使用了 +2s，应该用时间戳，排查一下是不是 lrc 转换 srt 的时候丢失了，还是没丢失没用上。
16. ✅搞个测试用例，专门自动跑 test/lrc_files 的 lrc 文件
17. ❌很大的可能性是用户去歌词网站复制一个歌词下来，然后导入到编辑器中。（需要做一个支持复制粘贴区），.txt 文件支持的支持，或者是 lrc 文件编辑区
18. ❌如果只有歌词，没有时间戳是否能跟音乐对上（难，这还是要借助 whisper，但 whisper对于段落时间分割也不是很准，如果让用户自己分割其实也蛮复杂的）
19. ✅实现 lrc 界面和逻辑
20. ✅调整UI(暂缓，等其他功能都实现后再做)
21. ✅单元测试 test
22. ✅查看 parse lrc 的 json 是啥样的
23. ✅nuxt 的 dev tools storage 怎么看，需要 token 「在项目启动的控制台里」
24. ✅lrc 转出来的数据少了 endTime，数据格式参考之前的 c 项目
25. ✅替换按钮点击没反应
26. ✅合并逻辑有点问题，要把下一行的文本跟上一行拼接到一起，现在是下一行的文本合并没了
27. ✅传入 srt 格式，导出 srt 格式 「不支持导入导出 srt（不符合普通用户的操作逻辑）」
28. ✅流程考虑一下：导入 LRC > srt 编辑 > 完成
29. ✅点击文案触发 input focus
30. ✅srt编辑器的编辑栏抽成组件
31. ✅尝试用 音频波形功能点.md 文件和next项目功能，来让ai实现 nuxt 的音频波形功能
32. ✅用 v0.dev 实现波形，提示词：@meersagor/wavesurfer-vue 和 wavesurfer.js 实现。
33. ✅播放音乐时有大量的计算，导致卡顿。
34. ✅歌词编辑区域：合并和添加按钮的位置以及 icon 的提示文案，需要实现
35. ✅编辑器模块：如果有歌词，则在UI层面把原文和翻译框（虚线）出来，可以让 AI 设计一下
36. ❌交互性增强: 是否允许用户在显示界面上直接点击编辑歌词或微调时间戳？（这会增加复杂度）「编辑字幕区就可以了，没必要在展示区做编辑功能，会过于混乱」
37. 需要支持带有更复杂标签（如 <00:10.50>）的 LRC 格式
38. ❌实时编辑与保存: 与其仅仅是"导入"，是否需要实现真正的 LRC 编辑功能？比如调整时间、修改文本、添加/删除行，并能将修改后的结果导出为 LRC 或 SRT 文件？「暂不考虑 lrc 的编辑能力」
39. ❌结束时间确定逻辑: lrcToSrt 使用下一行的开始时间作为当前行的结束时间。这在某些情况下可能不理想（比如两行歌词间有较长间隙）。是否需要提供不同的策略，比如设置一个默认的持续时间，或者允许手动调整？
40. ✅播放器波形上的歌词拖动功能
41. ✅srt 编辑器中留一个放翻译的
42. ✅编辑器：歌词的触控区域放大
43. ✅添加翻译：当翻译为空时，失去焦点展示成添加翻译
44. ✅合并按钮是把下一行跟当前行合并
45. ✅文本增加个底部颜色
46. ✅合并按钮逻辑：如果有翻译的话需要把翻译也合并上
47. ✅合并按钮，浮动上去的时候有一个谁跟谁合并的页面上的框选效果
48. ✅查找替换要支持翻译的替换
49. ❌撤销功能要支持时间戳的撤销，编辑改变时间戳后要能撤销时间戳，「时间戳只有输入正确的时候才能撤销恢复」
50. ✅查找和替换弹窗，当输入文字的时候弹窗一直在跳动（可能是由于搜索的内容长度变化导致的），体验很不好，怎么做能让体验更好。
51. ✅查找/替换：表头固定宽度（原文、翻译）
52. ✅把 lrc 的时间戳毫秒部分也改为逗号 00:00:20,000
53. 🤔流程：填写歌曲名称，歌手 > 选择（根据歌名获取在线歌词，自己上传歌词）> 编辑器界面 > 完成 > 再次编辑歌曲（需要有 lrc、歌曲）> 完成更新

54. ❓nuxt store 缓存是存在哪儿
55. ❓store 中逻辑很多，是否可以抽到 utils 中，很多逻辑都在 stores 中，是否合理？
56. 项目中的常量设置到常量文件中，通过引用的方式已使用，不要硬编码。比如 00:00:02 添加一行的2s结束时间。
57. 项目中的函数尽量使用 lodash-es,是否可以设置一个 cursor rules 来限制 AI 的输出

58. ❌lrc: 重新上传功能：如果是相同的文件重新上传，支持重新转换 srt，可以取个巧（判断是否跟当前使用的文件相同，如果相同则重新生成 srt，不同则重新生成 lrc）
59. ✅lrc: 时间展示还是展示 00:00:16.500，点分割毫秒，而不是逗号。「目的是标准的 lrc 格式就应该是用点，至于粘贴到 srt 编辑器的时候会自动转换为 srt 的标准格式」
60. ❌lrc:（重新）上传 lrc 文件后新的 lrc 会导致 字幕更新，让历史记录支持撤销和恢复
61. ✅高 - lrc: 当一首歌完成后，用户重新编辑。需要把 json 数据转换为 lrc 回显在页面上。
62. ✅高 - lrc: 转换为json的逻辑在看看，尤其是结尾是中文的时间，需要处理一下
63. ❌高 - lrc: 为什么lrc的时间转换成了 time 呢？跟其他统一用时间戳更好，因为有很多计算需求
64. 低 - lrc: 支持 lrc <> 这种格式的转换
65. 低 - lrc: 给一些（多个）标准 lrc 格式的示例
66. 低 - lrc: 做一个操作指引，先导入 lrc，然后 lrc 导入到字幕编辑器，然后再生成音乐
67. 低 - lrc: 如果用户上传的 lrc 完全不符合规范，则提示，并给出规范的 lrc 文件，好让用户更新 lrc 文件后再重新上传。（不然那传了 lrc 单页面上展示空的体验不好）

68. ✅音频波形: 问题：改动 srt 编辑器的时候才在音频波形器上出现歌词；期望：场景一：先上传的音频，则当生成 srt 时就出现歌词。
69. ✅音频波形: 区域的歌词区间拖拽功能待实现（参考开源项目）
70. ✅音频波形: 拖拽歌词同步更新 srt 的时间戳
71. ✅音频波形: 歌词拖拽相邻自动吸附、重复等功能，参考 demo 实现。
72. ✅音频波形: 播放开始暂停按钮的 hover 样式有点小问题
73. ✅音频波形: 点击开始播放时，播放按钮会抖动一下，排查一下什么原因
74. ✅音频波形: 点击进度条更新时间戳
75. ✅音频波形: 添加lrc和音频后 > 拖动id1 > 添加一行字幕 > 拖动id1 > id1的时间没有改变
76. ✅音频波形: 歌词分段播放基础功能
77. ✅音频波形: 歌词分段播放，修改开始和结束时间后要同步更新播放时间
78. ✅音频波形: 歌词展示要清晰，时间戳展示也要清晰
79. ✅音频波形: 进度条上浮动展示鼠标移动位置的时间
80. ✅低-音频波形: 如果没有上传音频，则在中间展示上传音频文件按钮，样式调整。
81. ✅低-音频波形: 波形区的选择音频文件和播放进度条的样式怎样设计会更协调
82. ✅高-音频波形: 点击波形跳转进度
83. ❌高-音频波形: 点击进度条，更新进度时有性能问题，点击后发现在缓缓的变化进度。（不算啥问题，进度条缓慢缩回，音频时间还是很准确的）
84. ✅高-音频波形: 波形上展示翻译文本
85. ✅高-音频波形: 如果播完，播放按钮复位（开始播放状态）
86. ✅高-音频波形: 波形进度一卡一卡，猜测是跟每一秒都更新进度有关。「优化了watch 监听 playerStore.currentTime 更新 波形 watch」。
87. 低-音频波形: 在点击进度条时，更新波形的进度，目前是通过 watch seekRequest 更新，当 seekRequest 变化时就会触发波形的更新，有些场景会触发多次，需进一步优化。「比如变更歌词，会触发多次，因为变更歌词会触发 seekRequest 变化」

88. ✅srt: 当鼠标移动到item模块上时加个 hover 背景色
89. ✅srt: 播放的时候歌词编辑区也要跟着时间戳滚动
90. ✅srt: hover 背景色再调整一下，可以参考合并的背景色
91. ✅srt: 滚动的时候，歌词编辑区的歌词要滚动到中间
92. ✅srt: 播放的时候要立即执行滚动，而不是等3秒后滚动
93. ✅srt: 点击item，音乐也要跟着滚动到当前时间点
94. ✅srt: lrc 的歌词滚动是怎么实现的？我看正在播放的时候滚动跟时间播放不冲突，过一会儿又会自动定位到正在播放的歌词了（是不是，等下一条匹配到才会滚动过去）「在 LrcDisplay.vue 中，歌词的自动滚动是通过监听 activeLineIndex （当前活动行索引）实现的。每当 activeLineIndex 变化时，会自动滚动到对应的歌词行。 activeLineIndex 的计算逻辑是：遍历所有歌词行，找到最后一个时间小于等于当前播放时间的行索引。所以，歌词滚动的行为是：只有当播放器的当前时间进入下一句歌词时， activeLineIndex 才会变化并触发滚动。如果你在播放过程中手动滚动歌词，界面不会立即跳回当前播放行，只有等到播放器时间进入下一句歌词时，才会自动定位到对应的歌词行。这正是你观察到的现象。」「srt 逻辑跟 lrc 保持一致」
95. ✅srt: 页面无法滚动了，样式问题，是有两个滚动条，不知道是不是自动滚动歌词居中导致的。
96. ✅srt: 合并按钮的 hover 背景没有对齐，样式有问题了布局导致的
97. ✅srt: 为什么选择 lrc 歌词，定位不到 srt 的歌词，但是选择 srt 的歌词可以定位到 lrc 的歌词「原因见 docs 下的 SRT 与 LRC 歌词高亮/定位不同步分析.md」
98. ✅srt: 合并的 hover 定位问题
99. ✅srt: 添加一行的 end 时间不能超过下一行的 start 时间
100. ✅srt: 变更开始和结束时间，开始时间的设置：如果有上一行，则开始时间不能小于上一行的结束时间，可以等于上一行的结束时间，如果没有上一行则则不做判断；结束时间的设置：如果有下一行，则结束时间不得小于下一行的开始时间，可以等于下一行的开始时间，如果没有下一行则不限制。
101. ✅srt: 最多输入 HH:MM:SS,mmm 这么多位数，多了自动处理一下。
102. ✅srt: 添加一行时，判断上一个的结束时间，添加的开始时间不能大于上一行的结束时间。
103. ✅srt: 如果删除了正在循环播放的字幕，需要清除循环的设置
104. ✅srt: 撤销和恢复失效了「history字段取新的，专用于管理历史的字段」
105. ✅srt: 如果撤销/重做操作影响了正在循环的字幕
106. ✅srt: 点击编辑文本，换个颜色，白色跟就是文本似的，需要跟已填写的有区别，表明是占位的提示文本
107. ✅高-srt: 当已经上传了音频时，限制结束时间不能设置超出音频总长度
108. ✅高-srt: 查找替换弹窗，当页面缩小时没有自适应缩小
109. ❌低-srt: 点击光标所在位置，播放器播放到该位置「不好做」
110. ✅低-srt: 播放到分段的位置卡顿了一下，看看是不是跟 setInterval 30 有关「如果没有开启循环播放则跳过检测」。「跟循环检测无关，检测前判断了是否是循环播放」
111. 低-srt: （低优先级）可以考虑下 syncRegions 的性能，目前实现的逻辑是变更字幕，移除所有字幕块重新生成

112. ✅高-整体: 保存歌曲，将 srt 的格式转换为符合要求的 json
113. ✅高-整体: 修改完成保存时，过滤 srt 中原文为空的段落
114. ✅高-整体: 顶部增加一个导航栏，用于展示，上传 lrc，上传音频文件，完成按钮。
115. ✅高-顶栏: 上传按钮加上上传 icon
116. 中-整体: 保存歌曲，将音频也上传到服务端
117. 中-整体: c端用户练习中，支持一首歌根据 start end 时间播放音乐的时段
118. 中-整体: 点击保存的时候上传音频/视频到 oss；进入页面的时候回显就用 url，如果是 url 则不用上传 oss。
119. 低-整体: 在编辑端，增加一个进入歌曲的入口，比如是创建歌曲。
120. 低-整体: 设计一下用户输入歌曲歌名、歌手信息，获取在线歌词，然后进入编辑端的逻辑。（考虑用户同时传了 lrc 文件的场景）
121. 低-整体: 在编辑端，点击编辑歌曲，能够进入该歌曲的编辑页面，编辑页面通过 json 生成 lrc 字幕。 lrc 字幕生成 srt

122. ✅循环播放: 微调的时候，要更新循环播放的范围。
123. ✅循环播放: 为什么切tab，就没有循环播放了。「浏览器限制，通过 setTimeout 解决」
124. ✅循环播放: 开始暂停按钮失效了
125. ✅循环播放: 在 srt 中编辑了结束时间，循环播放还往后冲了一段不够精准，这是为什么？「是因为通过定时器做的循环时间改为30毫秒」
126. ✅高-循环播放: 如果没有上传音频则不展示循环播放按钮。
127. ❌低-循环播放: 按钮的位置可以再考虑一下「可以等实际使用的时候再考虑」

128. ✅高-技术: 支持mp4视频
129. 低-技术: 单元测试，utils stores component 的单元测试

130. ✅高-媒体: 将波形静音，播放视频的声音。
131. ✅高-媒体: 为什么放弃转换为 srt 而选择了转成 vtt 「vtt 是 w3c推荐标准，浏览器原生支持，样式化强大，定位支持精准控制，注释支持注释」
132. ✅高-媒体: 视频上展示字幕
133. ✅高-媒体: 视频上同时展示原文和翻译字幕
134. ✅高-媒体: 生成 vtt 字幕
135. ✅高-媒体: 监听 srt 的改变，同步生成新的 vtt。在 VideoPlayer 中监听变化更新 vtt
136. ✅高-媒体: 编辑了字幕后，需要重新生成 vtt，字幕需要更新（目前合并了字幕后 vtt 更新了，但播放器上没有更新）「在组件上加 :key」
137. ✅高-媒体: 字幕编辑器中更新后，在视频上的字幕有点展示异常
138. ✅高-媒体: 如果正在播放中，改变了字幕内容，播放会有问题。（应该是重新生成 vtt 这块处理需要调整）
139. ✅高-媒体: 倍速和片段循环视频播放不正确
140. ✅高-媒体: 宽度要按比例设置合理的宽度，不然屏幕太小的时候，视频会变形
141. ✅高-媒体: “音频文件无视频预览” 在视频区域展示，参考火山视频。

142. ✅高-其他: 输入歌曲名称，获取在线 lrc
143. ✅高-其他: 实现在线歌词的查看功能
144. ❌高-其他: 修复在线歌词的应用功能，目前应用转换后不正确「暂不支持中文歌曲」
145. ✅高-其他: 左右支持拖拽（歌词编辑器和右侧）
146. ✅低-其他: 接入 https://trpc.io/
147. ✅中-其他: 获取的歌词只有中文没有翻译，希望找一个带翻译的资源

148. ✅高-在线歌词: 接入网易云
149. ✅中-在线歌词: 搜索歌曲分页

150. ✅高-扩展功能：支持 srt 导入
151. ✅高-扩展功能：支持 vtt 导入
152. ✅高-扩展功能: 对接火山 api，音/视频生成字幕：

- A：音频文件有这几处限制：
- 音频时长需小于一小时；
- 音频大小需小于150MB；
- 支持MP3、MP4、WAV、OGG等音视频格式。

157. ✅高-扩展功能: 对接火山引擎文本翻译功能「200w字符免费，超过200w 需要付费 49每月」
158. ✅高-扩展功能：设计导入方案「页面上太多导入了，lrc/vtt/srt/音频/视频，需要设计一个导入方案，让用户可以方便的导入这些文件」
159. ✅中-扩展功能: srt 编辑器样式调整，给 text 和翻译纵向增加间距「现在文字比较多就挤在一起了，浮动的按钮就把文字给盖住了」
160. ✅中-扩展功能: srt 编辑器输入序号快速跳转
161. ✅高-字幕编辑器: 增加快速跳转功能，输入序号或者时间戳或者歌词文本，跳转到对应的位置
162. ✅高-字幕编辑器: 增加筛选功能，筛选是否有翻译的文本

163. ✅高-弹窗: 弹窗内容超出滚动，这个高度如何设计「body flex-1 超出容器高度滚动」
164. ✅高-字幕编辑器: 筛选弹窗底部用 footer 插槽
165. ✅高-筛选: 支持搜索时间：秒、分、时间字符串格式
166. ✅高-筛选: 不同的条件用不同的输入框
167. ✅高-字幕编辑器: 增加筛选功能，筛选时间戳有错误的条目

168. 高-用户端：加载一整个音视频文件，然后根据时间戳，播放上一句下一句。
169. 高-用户端：可听音乐、可看视频。

170. 高-扩展功能: 通过火山识别，将音频识别为文本，然后将用户带或者不带时间戳的文本，跟火山识别出来的文本对齐。「目的是火山识别的文本的时间戳更准确，将用户的文本与火山的对齐，达到了用户文本的时间戳更准确的目的」

171. 高-时间轴对齐：火山识别，时间轴对齐功能。

172. 高-实现音频识别和翻译功能
173. ✅高-对接LLM模型，提取摘要

174. ✅高-cursor: 规则重新生成「页面和组件以及composables 的规则」
175. 高-单元测试: 接口的单元测试和函数的单元测试，每次运行先跑一遍测试用例

176. 高-composables: 分模块管理
177. 高-音频波形: 波形上选中和未选中的进度颜色差别要大一点，现在区别不明显。

# DeepSeek V3 代理 API 使用指南

本项目已集成火山引擎的 DeepSeek V3 模型代理 API，基于 tRPC 架构实现，支持流式和非流式聊天对话。

## 环境配置

在 `.env` 文件中添加以下环境变量：

```bash
# 火山引擎方舟平台 API Key
ARK_API_KEY=your_ark_api_key_here

# DeepSeek 模型 ID (例如: deepseek-v3)
DEEPSEEK_MODEL_ID=your_deepseek_model_id_here
```

## API 接口

### 1. 非流式聊天对话

**路由**: `deepseek.chat`
**类型**: `mutation`

#### 输入参数
```typescript
{
  messages: Array<{
    role: 'system' | 'user' | 'assistant'
    content: string
  }>
  stream?: boolean // 可选，默认 false
  temperature?: number // 可选，0-2，默认 0.7
  max_tokens?: number // 可选，1-16000，默认 4000
  top_p?: number // 可选，0-1，默认 0.9
}
```

#### 返回结果
```typescript
{
  id: string
  object: string
  created: number
  model: string
  choices: Array<{
    index: number
    message: {
      role: 'assistant'
      content: string
    }
    finish_reason: string | null
  }>
  usage: {
    prompt_tokens: number
    completion_tokens: number
    total_tokens: number
  }
}
```

### 2. 流式聊天对话

**路由**: `deepseek.chatStream`
**类型**: `subscription`

#### 输入参数
与非流式接口相同

#### 返回结果
流式返回字符串片段，可用于实时显示 AI 回复内容。

## 前端使用示例

### Vue 组件中使用

```vue
<template>
  <div class="chat-container">
    <div class="messages">
      <div v-for="(message, index) in messages" :key="index" class="message">
        <div class="role">{{ message.role }}:</div>
        <div class="content">{{ message.content }}</div>
      </div>
    </div>
    
    <div class="input-area">
      <textarea v-model="userInput" placeholder="请输入您的问题..." @keyup.enter="sendMessage" />
      <button @click="sendMessage" :disabled="isLoading">发送</button>
      <button @click="sendStreamMessage" :disabled="isLoading">流式发送</button>
    </div>
    
    <div v-if="streamContent" class="stream-content">
      <div class="role">assistant:</div>
      <div class="content">{{ streamContent }}</div>
    </div>
  </div>
</template>

<script setup>
import { ref } from 'vue'

// tRPC 客户端 (假设已配置)
const { $trpc } = useNuxtApp()

const messages = ref([
  { role: 'system', content: '你是一个有用的AI助手' }
])
const userInput = ref('')
const isLoading = ref(false)
const streamContent = ref('')

// 非流式聊天
async function sendMessage() {
  if (!userInput.value.trim() || isLoading.value) return
  
  const newMessage = { role: 'user', content: userInput.value }
  messages.value.push(newMessage)
  
  isLoading.value = true
  
  try {
    const response = await $trpc.deepseek.chat.mutate({
      messages: [...messages.value],
      temperature: 0.7,
      max_tokens: 2000
    })
    
    const assistantMessage = {
      role: 'assistant',
      content: response.choices[0].message.content
    }
    
    messages.value.push(assistantMessage)
    userInput.value = ''
    
  } catch (error) {
    console.error('聊天失败:', error)
    alert('聊天失败，请重试')
  } finally {
    isLoading.value = false
  }
}

// 流式聊天
async function sendStreamMessage() {
  if (!userInput.value.trim() || isLoading.value) return
  
  const newMessage = { role: 'user', content: userInput.value }
  messages.value.push(newMessage)
  
  isLoading.value = true
  streamContent.value = ''
  
  try {
    // 创建流式订阅
    const subscription = $trpc.deepseek.chatStream.subscribe({
      messages: [...messages.value],
      temperature: 0.7,
      max_tokens: 2000
    })
    
    subscription.on('data', (chunk) => {
      streamContent.value += chunk
    })
    
    subscription.on('complete', () => {
      // 流式完成，将内容添加到消息列表
      const assistantMessage = {
        role: 'assistant',
        content: streamContent.value
      }
      messages.value.push(assistantMessage)
      streamContent.value = ''
      userInput.value = ''
      isLoading.value = false
    })
    
    subscription.on('error', (error) => {
      console.error('流式聊天失败:', error)
      alert('流式聊天失败，请重试')
      isLoading.value = false
    })
    
  } catch (error) {
    console.error('启动流式聊天失败:', error)
    alert('启动流式聊天失败，请重试')
    isLoading.value = false
  }
}
</script>

<style scoped>
.chat-container {
  max-width: 800px;
  margin: 0 auto;
  padding: 20px;
}

.messages {
  border: 1px solid #ddd;
  height: 400px;
  overflow-y: auto;
  padding: 10px;
  margin-bottom: 20px;
}

.message {
  margin-bottom: 10px;
  padding: 10px;
  border-radius: 5px;
  background-color: #f5f5f5;
}

.role {
  font-weight: bold;
  color: #666;
}

.content {
  margin-top: 5px;
}

.input-area {
  display: flex;
  gap: 10px;
}

.input-area textarea {
  flex: 1;
  padding: 10px;
  border: 1px solid #ddd;
  border-radius: 5px;
  resize: vertical;
}

.input-area button {
  padding: 10px 20px;
  background-color: #007bff;
  color: white;
  border: none;
  border-radius: 5px;
  cursor: pointer;
}

.input-area button:disabled {
  background-color: #ccc;
  cursor: not-allowed;
}

.stream-content {
  margin-top: 10px;
  padding: 10px;
  border: 1px solid #007bff;
  border-radius: 5px;
  background-color: #e7f3ff;
}
</style>
```

### Composables 使用

```typescript
// composables/useDeepseek.ts
export const useDeepseek = () => {
  const { $trpc } = useNuxtApp()
  
  const chatWithDeepseek = async (messages: Array<{role: string, content: string}>, options = {}) => {
    try {
      const response = await $trpc.deepseek.chat.mutate({
        messages,
        temperature: 0.7,
        max_tokens: 2000,
        ...options
      })
      
      return response.choices[0]?.message?.content || ''
    } catch (error) {
      console.error('DeepSeek 聊天失败:', error)
      throw error
    }
  }
  
  const streamChatWithDeepseek = (messages: Array<{role: string, content: string}>, onChunk: (chunk: string) => void, options = {}) => {
    return $trpc.deepseek.chatStream.subscribe({
      messages,
      temperature: 0.7,
      max_tokens: 2000,
      ...options
    }, {
      onData: onChunk,
      onError: (error) => {
        console.error('DeepSeek 流式聊天失败:', error)
      }
    })
  }
  
  return {
    chatWithDeepseek,
    streamChatWithDeepseek
  }
}
```

## 测试

### 使用 curl 测试

```bash
# 测试非流式接口
curl -X POST http://localhost:6001/api/trpc/deepseek.chat \
  -H "Content-Type: application/json" \
  -d '{
    "json": {
      "messages": [
        {"role": "system", "content": "你是一个有用的AI助手"},
        {"role": "user", "content": "你好"}
      ],
      "temperature": 0.7,
      "max_tokens": 1000
    }
  }'
```

## 注意事项

1. **API Key 安全**: 确保 `ARK_API_KEY` 和 `DEEPSEEK_MODEL_ID` 正确配置且安全存储
2. **速率限制**: 根据火山引擎的配额合理控制请求频率
3. **错误处理**: 实现适当的错误处理和重试机制
4. **流式处理**: 流式接口适合长内容生成，可提供更好的用户体验
5. **Token 消耗**: 注意监控 token 使用量，合理设置 `max_tokens` 参数

## 故障排除

### 常见错误

1. **配置错误**: 检查环境变量是否正确设置
2. **API Key 失效**: 确认 ARK_API_KEY 有效且有足够权限
3. **模型 ID 错误**: 确认 DEEPSEEK_MODEL_ID 正确且可用
4. **网络问题**: 检查到火山引擎 API 的网络连接

### 调试方法

1. 查看服务器日志中的错误信息
2. 使用浏览器开发者工具检查网络请求
3. 测试 API Key 和模型 ID 的有效性

## 扩展功能

你可以根据需要扩展以下功能：

1. **对话历史管理**: 实现对话记录的持久化存储
2. **多轮对话优化**: 控制上下文长度，避免 token 超限
3. **个性化配置**: 为不同用户提供个性化的模型参数
4. **监控和分析**: 添加使用统计和性能监控 
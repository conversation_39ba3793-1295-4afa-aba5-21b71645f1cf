# 技术栈对比：主项目 (Nuxt) vs 当前项目 (Next.js)

此文档总结了基于 `package.json` 文件分析得出的两个项目之间的主要技术栈差异。

## 核心差异

1.  **前端框架:**

    - **主项目:** Nuxt 3 (基于 Vue.js)
    - **当前项目:** Next.js (基于 React)
    - **影响:** 这是最根本的区别，导致组件模型、路由、状态管理、数据获取策略完全不同。

2.  **UI 库与样式:**

    - **主项目:** Naive UI, Nuxt UI, Tailwind CSS, DaisyUI, FormKit AutoAnimate
    - **当前项目:** Radix UI (基础组件), Tailwind CSS, Lucide Icons, Tabler Icons
    - **共同点:** 都使用 Tailwind CSS。
    - **差异点:** 组件库选择不同 (Naive/Nuxt UI vs Radix)。

3.  **状态管理:**

    - **主项目:** Pinia
    - **当前项目:** 未在 `package.json` 中明确指定 (可能使用 React Context 或其他未列出的库)。

4.  **数据获取/API 层:**

    - **主项目:** tRPC, ofetch
    - **当前项目:** 未在 `package.json` 中明确指定 (可能使用 Next.js API Routes 或 fetch/axios)。

5.  **后端与数据库:**
    - **主项目:** Drizzle ORM, MySQL2, Mongoose (表明有后端和数据库交互)
    - **当前项目:** `package.json` 中未包含明确的后端或数据库依赖。

## 特定功能库差异

- **主项目:**
  - AI: `@langchain/core`, `@langchain/openai`
  - 云存储: `cos-js-sdk-v5`, `cos-nodejs-sdk-v5`
  - 图像处理: `sharp`
  - 动画: `animejs`
  - 模糊搜索: `fuse.js`
  - JWT: `jose`
  - 其他: `youtube-transcript`
- **当前项目:**
  - 音频处理/可视化: `wavesurfer.js`, `@wavesurfer/react`
  - 音频播放: `react-player`
  - 动画: `motion`

## 开发与构建工具差异

- **主项目:**
  - 测试: Vitest
  - Linter/Formatter: ESLint, Prettier
  - 构建/核心: Nuxt CLI, VueUse, TypeScript
- **当前项目:**
  - Linter/Formatter: BiomeJS
  - 构建/核心: Next.js CLI, TypeScript, Babel React Compiler (实验性)

## 迁移建议

### 第一阶段：基础设施迁移

1. 将项目框架从 Next.js 迁移到 Nuxt 3
2. 设置 Tailwind CSS (已有基础)
3. 引入 Naive UI 和 Nuxt UI 替代 Radix UI
4. 配置 ESLint 和 Prettier 替代 BiomeJS

### 第二阶段：核心功能迁移

1. 实现基本的页面路由结构
2. 设置 Pinia 状态管理
3. 配置 tRPC 客户端
4. 将 React 组件重写为 Vue 组件

### 第三阶段：特定功能迁移

1. 寻找 wavesurfer.js 的 Vue 替代方案或创建 Vue 包装器
2. 将 react-player 替换为 Vue 音频播放器
3. 将 motion 动画库替换为 animejs

### 第四阶段：优化和完善

1. 性能优化
2. 代码重构
3. 测试用例编写
4. 文档更新

## 总结

两个项目在核心框架 (Vue vs React) 上存在根本性差异，这导致了生态系统、库选择和开发模式上的巨大不同。将当前项目 (Next.js/React) 迁移至主项目 (Nuxt/Vue) 的技术架构，意味着需要重写大部分前端代码，包括组件、状态管理、路由和数据获取逻辑。样式层 (Tailwind CSS) 是少数有重叠的部分，但 UI 组件库仍需替换。特定功能库也需要找到 Vue/Nuxt 生态中的对应替代品或重新实现。

建议采用渐进式迁移策略，按照上述阶段逐步进行，确保每个阶段都经过充分测试后再进入下一阶段。这样可以最大限度地降低迁移风险，保证系统稳定性。

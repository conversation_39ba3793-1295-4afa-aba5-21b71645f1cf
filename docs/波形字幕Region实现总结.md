# 波形字幕 Region 实现总结

本总结梳理了在波形图上实现 SRT 字幕可视化、可拖拽编辑、内容和样式自定义 Region 的核心技术思路，供团队开发和维护参考。

---

## 1. 字幕与 Region 的一一映射
- 使用 `Map<string, Region>` 存储字幕 uuid 与 Region 实例的映射，便于后续查找和操作。

相关文件：
- `components/AudioWaveform.vue`：负责实现 uuid 与 Region 的映射逻辑，管理 Region 的创建、查找和同步。
- `stores/subtitleStore.ts`：管理字幕的全局状态，提供字幕数据源。

## 2. 自定义 Region 内容
- 实现 `getRegionContentHtml(start, text, end)`，生成带有起止时间和字幕文本的 HTML 字符串。
- 创建 Region 时，设置 `content` 字段为上述 HTML，使 Region 内部可显示时间和字幕。

相关文件：
- `components/AudioWaveform.vue`：实现 getRegionContentHtml 方法，并在创建 Region 时设置 content 字段，实现字幕内容的可视化。

## 3. Region 批量同步与刷新
- 每次字幕数据变化时，先清空所有 Region 和映射，再遍历字幕批量创建 Region。
- 创建 Region 时，设置 id、start、end、content、color、drag、resize、minLength 等参数，并存入映射。
- 创建后调用 `styleRegionHandles(region)`，为 Region 左右 handle 添加虚线、箭头等自定义样式。

相关文件：
- `components/AudioWaveform.vue`：实现批量同步和刷新 Region 的逻辑，负责根据字幕数据动态生成和更新 Region。
- `stores/subtitleStore.ts`：字幕数据变化时触发 Region 的同步。

## 4. 拖拽/调整 Region 后的内容刷新与同步
- 监听 `region-updated` 事件，回调中获取 uuid，查找字幕，做防重叠调整（如有），更新 Region 的 start/end/content，并刷新 handle 样式。
- 同步更新全局字幕状态（如 Pinia store）。

相关文件：
- `components/AudioWaveform.vue`：负责监听 region-updated 事件，处理拖拽和调整后的同步逻辑。
- `composables/useWaveSurfer.ts`：底层集成 Region 插件，注册 region-updated 事件。
- `stores/subtitleStore.ts`：负责字幕时间的更新和全局状态同步。

## 5. Region 激活与高亮
- 维护 `activeRegionId` 响应式变量，自动高亮当前播放或点击的 Region。
- 高亮 Region 使用更明显的色块，普通 Region 使用浅色块。

相关文件：
- `components/AudioWaveform.vue`：实现 activeRegionId 的响应式管理和 Region 高亮逻辑。

## 6. 事件注册与清理
- 波形初始化后注册 `region-updated` 事件，组件卸载时清理监听，防止内存泄漏。

相关文件：
- `components/AudioWaveform.vue`：负责事件的注册与清理，确保组件生命周期内资源管理合理。
- `composables/useWaveSurfer.ts`：底层事件注册和解绑。

## 7. 其他辅助功能
- 支持点击 Region 跳转、字幕展示区、键盘切换等交互。
- 所有样式和内容均可通过 DOM 操作和 CSS 进一步美化。

相关文件：
- `components/AudioWaveform.vue`：实现 Region 点击跳转、字幕展示区、键盘切换等交互功能。
- `components/SubtitleEditor.vue`：负责字幕的批量替换、导入导出等辅助功能。
- `utils/srtUtils.ts`、`types/subtitle.ts`：提供 SRT 解析、生成和字幕类型定义。

---

**一句话总结：**
通过 uuid-Region 映射、content 字段自定义、事件监听与同步、handle 样式定制，实现了"波形上可视化、可交互、内容丰富的 SRT 字幕拖拽编辑"体验。 
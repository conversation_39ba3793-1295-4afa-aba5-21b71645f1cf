# 逐行翻译功能使用指南

## 功能概述

逐行翻译功能基于 VideoLingo 的两步翻译策略，为语音识别结果提供高质量的翻译服务。该功能采用先进的 AI 翻译技术，确保翻译的准确性和自然流畅性。

## 核心特性

### 两步翻译策略

1. **忠实翻译 (Faithfulness)**

   - 准确传达原文含义
   - 保持术语一致性
   - 忠实于原始语义结构

2. **表达优化 (Expressiveness)** (可选)
   - 优化语言流畅性
   - 适应目标语言表达习惯
   - 调整语言风格匹配内容主题

### 质量保证机制

- **格式验证**: 确保返回结果格式正确
- **重试机制**: 最多3次重试，提高成功率
- **行数匹配**: 验证翻译结果与原文行数一致
- **上下文感知**: 支持前文后文信息增强翻译质量

## 使用流程

### 1. 上传音视频文件

- 支持 WAV、M4A、MP3、MP4、MOV、OGG 格式
- 文件大小限制：小于 150MB
- 推荐使用 WAV 格式以获得最佳兼容性

### 2. 进行语音识别

- 配置识别参数（语言、内容类型等）
- 等待语音识别完成
- 获得字幕识别结果

### 3. 配置翻译参数

- **目标语言**: 选择要翻译到的语言
- **源语言**: 设置原始语言
- **反射翻译**: 启用/禁用两步翻译策略

### 4. 执行翻译

- 点击"逐行翻译"按钮
- 系统自动执行两步翻译流程
- 实时显示翻译进度

### 5. 查看和导出结果

- 查看翻译统计信息
- 浏览翻译结果内容
- 下载翻译结果文件

## 配置选项

### 翻译配置

```typescript
interface LineTranslationConfig {
  targetLanguage: string // 目标语言
  sourceLanguage: string // 源语言
  reflectTranslate: boolean // 是否启用两步翻译
  maxRetryAttempts: number // 最大重试次数
  timeoutMs: number // 超时时间
}
```

### 支持的语言

- 简体中文
- 繁体中文
- English
- 日本語
- 한국어
- Français
- Español

## 技术实现

### 组合式函数: `useLineByLineTranslation`

```typescript
const {
  isTranslating, // 翻译状态
  translationError, // 错误信息
  faithfulResults, // 忠实翻译结果
  expressiveResults, // 表达优化结果
  finalResults, // 最终翻译结果
  config, // 翻译配置
  getTranslationStatistics, // 统计信息
  translateRecognitionResult, // 翻译方法
  resetTranslation, // 重置状态
  updateConfig, // 更新配置
} = useLineByLineTranslation()
```

### API 调用

使用非流式模式调用 DeepSeek API：

```typescript
const response = await fetch('/api/deepseek/stream', {
  method: 'POST',
  headers: {
    'Content-Type': 'application/json',
  },
  body: JSON.stringify({
    messages: [{ role: 'user', content: prompt }],
    stream: false,
  }),
})
```

## 与智能分块功能的对比

| 特性       | 智能分块翻译           | 逐行翻译               |
| ---------- | ---------------------- | ---------------------- |
| 分块策略   | 按语义和字符数智能分块 | 逐行处理               |
| 并发处理   | 支持多线程并发         | 串行处理               |
| 上下文处理 | 前后文增强             | 简化上下文             |
| 适用场景   | 长文本、复杂内容       | 短文本、实时翻译       |
| 翻译质量   | 基于上下文的高质量翻译 | 基于两步策略的精准翻译 |

## 最佳实践

### 翻译质量优化

1. **启用两步翻译**: 对于重要内容，建议启用反射翻译功能
2. **选择合适的源语言**: 确保源语言设置与实际内容匹配
3. **提供上下文信息**: 在可能的情况下提供内容摘要和术语说明

### 性能优化

1. **合理设置重试次数**: 根据网络状况调整重试参数
2. **监控翻译进度**: 关注翻译状态和错误信息
3. **及时处理结果**: 完成翻译后及时下载和保存结果

## 故障排除

### 常见问题

1. **翻译失败**

   - 检查网络连接
   - 验证 API 配置
   - 检查输入内容格式

2. **行数不匹配**

   - 原文包含空行或特殊字符
   - AI 返回格式不正确
   - 系统会自动重试解决

3. **翻译质量问题**
   - 尝试调整源语言设置
   - 启用两步翻译策略
   - 提供更详细的上下文信息

### 错误代码

- `翻译API错误`: API 调用失败
- `缺少必需的键`: JSON 格式验证失败
- `原文行数与翻译行数不匹配`: 结果验证失败
- `重试次数耗尽`: 多次重试后仍然失败

## 开发者文档

### 扩展翻译语言

1. 在配置选项中添加新语言
2. 更新提示词模板
3. 测试翻译质量和准确性

### 自定义翻译策略

1. 修改 `getPromptFaithfulness` 函数
2. 调整 `getPromptExpressiveness` 函数
3. 更新验证逻辑

### 集成其他翻译服务

1. 创建新的 API 调用函数
2. 适配响应格式处理逻辑
3. 更新错误处理机制

## 更新日志

### v1.0.0

- 初始版本发布
- 支持两步翻译策略
- 集成质量保证机制
- 提供完整的用户界面

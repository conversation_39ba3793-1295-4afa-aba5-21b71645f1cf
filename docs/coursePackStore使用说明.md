# 课程包Store使用说明

## 概述

`useCoursePackStore` 是一个用于管理课程包和课程信息的Pinia store，提供了完整的课程包数据管理功能。

## 文件位置

- **Store文件**: `stores/coursePackStore.ts`
- **测试页面**: `pages/test-course-pack-store.vue`
- **使用说明**: `docs/coursePackStore使用说明.md`

## 快速开始

访问测试页面查看store的功能演示：
```
http://localhost:3000/test-course-pack-store
```

## 主要功能

### 1. 状态管理
- 课程包列表管理
- 当前选中课程包的详细信息
- 当前选中课程的详细信息
- 加载状态和错误处理

### 2. 数据持久化
- 自动保存选中的课程包ID和课程ID到localStorage
- 页面刷新后保持选中状态

## 使用方法

### 基本用法

```vue
<script setup>
import { useCoursePackStore } from '~/stores/coursePackStore'

const coursePackStore = useCoursePackStore()

// 获取课程包列表
await coursePackStore.fetchCoursePackList()

// 选择课程包
coursePackStore.selectCoursePack('coursePackId')

// 选择课程
coursePackStore.selectCourse('courseId')
</script>
```

### 状态访问

```vue
<template>
  <div>
    <!-- 加载状态 -->
    <div v-if="coursePackStore.isLoading">加载中...</div>
    
    <!-- 错误状态 -->
    <div v-if="coursePackStore.hasError" class="error">
      {{ coursePackStore.error?.message }}
    </div>
    
    <!-- 课程包列表 -->
    <div v-for="pack in coursePackStore.coursePackList" :key="pack.id">
      <h3>{{ pack.title }}</h3>
      <p>{{ pack.description }}</p>
      <button @click="coursePackStore.selectCoursePack(pack.id)">
        选择课程包
      </button>
    </div>
    
    <!-- 当前课程包的课程列表 -->
    <div v-if="coursePackStore.currentCoursePack">
      <h2>{{ coursePackStore.currentCoursePack.title }}</h2>
      <div v-for="course in coursePackStore.currentCourseList" :key="course.id">
        <h4>{{ course.title }}</h4>
        <button @click="coursePackStore.selectCourse(course.id)">
          选择课程
        </button>
      </div>
    </div>
    
    <!-- 当前课程详情 -->
    <div v-if="coursePackStore.currentCourse">
      <h2>{{ coursePackStore.currentCourse.title }}</h2>
      <p>{{ coursePackStore.currentCourse.description }}</p>
      
      <!-- 课程导航 -->
      <div>
        <button 
          @click="coursePackStore.goToPrevCourse()"
          :disabled="!coursePackStore.hasPrevCourse"
        >
          上一课程
        </button>
        <span>
          {{ coursePackStore.currentCourseIndex + 1 }} / 
          {{ coursePackStore.currentCourseList.length }}
        </span>
        <button 
          @click="coursePackStore.goToNextCourse()"
          :disabled="!coursePackStore.hasNextCourse"
        >
          下一课程
        </button>
      </div>
    </div>
  </div>
</template>
```

## API 参考

### 状态 (State)

| 属性 | 类型 | 描述 |
|------|------|------|
| `status` | `CoursePackStatusEnum` | 当前store状态 |
| `error` | `Error \| null` | 错误信息 |
| `coursePackList` | `AdminCoursePack[]` | 课程包列表 |
| `currentCoursePack` | `CoursePackWithCoursesResponse \| null` | 当前选中的课程包 |
| `currentCourse` | `AdminCourseDetailData \| null` | 当前选中的课程 |
| `selectedCoursePackId` | `string \| null` | 选中的课程包ID |
| `selectedCourseId` | `string \| null` | 选中的课程ID |

### 计算属性 (Computed)

| 属性 | 类型 | 描述 |
|------|------|------|
| `isLoading` | `boolean` | 是否正在加载 |
| `hasError` | `boolean` | 是否有错误 |
| `currentCourseList` | `CourseInPackResponse[]` | 当前课程包的课程列表 |
| `currentCourseIndex` | `number` | 当前课程在列表中的索引 |
| `hasNextCourse` | `boolean` | 是否有下一课程 |
| `hasPrevCourse` | `boolean` | 是否有上一课程 |

### 方法 (Actions)

| 方法 | 参数 | 描述 |
|------|------|------|
| `fetchCoursePackList()` | - | 获取课程包列表 |
| `fetchCoursePackDetail(coursePackId)` | `string` | 获取课程包详情 |
| `fetchCourseDetail(coursePackId, courseId)` | `string, string` | 获取课程详情 |
| `selectCoursePack(coursePackId)` | `string` | 选择课程包 |
| `selectCourse(courseId)` | `string` | 选择课程 |
| `goToNextCourse()` | - | 切换到下一课程 |
| `goToPrevCourse()` | - | 切换到上一课程 |
| `setError(error)` | `Error \| null` | 设置错误状态 |
| `clearError()` | - | 清除错误状态 |
| `reset()` | - | 重置所有状态 |

## 状态枚举

```typescript
export const enum CoursePackStatusEnum {
  IDLE = 'idle',      // 空闲状态
  LOADING = 'loading', // 加载中
  LOADED = 'loaded',   // 已加载
  ERROR = 'error',     // 错误状态
}
```

## 注意事项

1. **自动数据获取**: 选择课程包或课程时会自动获取相应的详细数据
2. **错误处理**: 所有异步操作都包含错误处理，错误信息会存储在 `error` 状态中
3. **数据持久化**: 选中的课程包ID和课程ID会自动保存到localStorage
4. **防重复请求**: 相同的请求不会重复发送，避免不必要的网络请求
5. **状态同步**: 选择课程时会自动确保课程包数据也是最新的

## 与其他Store的集成

这个store可以与其他store（如 `playerStore`、`subtitleStore`）配合使用：

```vue
<script setup>
import { useCoursePackStore } from '~/stores/coursePackStore'
import { usePlayerStore } from '~/stores/playerStore'

const coursePackStore = useCoursePackStore()
const playerStore = usePlayerStore()

// 当选择课程时，可以同时加载媒体文件
watch(() => coursePackStore.currentCourse, (course) => {
  if (course?.mediaUrl) {
    // 这里可以设置播放器的媒体源
    // playerStore.setMediaUrl(course.mediaUrl)
  }
})
</script>
```

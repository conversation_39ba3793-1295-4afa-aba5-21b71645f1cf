# VideoLingo 分块翻译与时间戳对齐机制详解

## 概述

VideoLingo 采用了巧妙的分块翻译策略，既提供了丰富的上下文信息来提高翻译质量，又保持了句子级别的精确时间戳对齐。本文档详细解析这一核心机制的工作原理。

## 核心问题解答

**Q: 分块大小是600字符，输出是一句一句还是一段一段？**  
**A: 输出是一句一句的翻译结果！**

**Q: 如何实现时间戳对齐？**  
**A: 通过ASR词级时间戳 + 句子边界映射实现精确对齐**

---

## 完整数据流程

```
原始视频文件
        ↓
    ASR语音识别
        ↓
词级时间戳数据 (df_text: word + start + end)
        ↓
按意义分割句子 (_3_2_SPLIT_BY_MEANING)
        ↓
文本分块处理 (600字符/块，最多10句)
        ↓
并发分块翻译 (逐句翻译，保持句子数量)
        ↓
结果重组 (src_text + trans_text 句子列表)
        ↓
时间戳对齐 (句子 → ASR词汇位置映射)
        ↓
最终字幕输出 (每句译文 + 对应时间戳)
```

---

## 详细机制分析

### 1. 文本分块策略

#### 分块函数：`split_chunks_by_chars()`

```python
def split_chunks_by_chars(chunk_size, max_i): 
    """按字符数分割文本，返回多行文本块列表"""
    with open(_3_2_SPLIT_BY_MEANING, "r", encoding="utf-8") as file:
        sentences = file.read().strip().split('\n')  # 已按句子分割

    chunks = []
    chunk = ''
    sentence_count = 0
    for sentence in sentences:
        if len(chunk) + len(sentence + '\n') > chunk_size or sentence_count == max_i:
            chunks.append(chunk.strip())
            chunk = sentence + '\n'
            sentence_count = 1
        else:
            chunk += sentence + '\n'  # 多个句子组成一个chunk
            sentence_count += 1
    chunks.append(chunk.strip())
    return chunks
```

#### 分块特点

- **输入源**：`_3_2_SPLIT_BY_MEANING` 文件（已按句子分割）
- **分块规则**：
  - 字符数限制：600字符
  - 句子数限制：最多10句
  - 边界处理：在句子边界处分割
- **输出格式**：包含多个句子的文本块列表

#### 示例分块效果

```
原始句子：
1. "Hello, I'm Andrew Ng."
2. "I'm a computer science professor at Stanford."
3. "I was early in neural network development."
4. "I created Coursera and deeplearning.ai courses."
...

分块结果：
Chunk 1: 
"Hello, I'm Andrew Ng.\nI'm a computer science professor at Stanford.\nI was early in neural network development."

Chunk 2:
"I created Coursera and deeplearning.ai courses.\n..."
```

### 2. 分块翻译机制

#### 翻译函数：`translate_lines()`

```python
def translate_lines(lines, previous_content_prompt, after_content_prompt, things_to_note_prompt, summary_prompt, index=0):
    # lines是包含多行的字符串，每行是一个句子
    shared_prompt = generate_shared_prompt(...)
    
    # 第一步：忠实翻译
    line_splits = lines.split('\n')
    json_dict = {}
    for i, line in enumerate(line_splits, 1):
        json_dict[f"{i}"] = {"origin": line, "direct": f"direct translation {i}."}
    
    # AI返回JSON格式，保证一句对一句
    faith_result = retry_translation(...)
    
    # 第二步：表达优化（可选）
    if reflect_translate:
        express_result = retry_translation(...)
        translate_result = "\n".join([express_result[i]["free"].strip() for i in express_result])
    else:
        translate_result = "\n".join([faith_result[i]["direct"].strip() for i in faith_result])
    
    return translate_result, lines
```

#### 翻译特点

- **输入格式**：多行文本块（每行一个句子）
- **处理方式**：AI逐句翻译，保持句子数量完全一致
- **质量保证**：严格验证原文行数 = 译文行数
- **输出格式**：按行分割的翻译结果字符串

#### JSON格式示例

**忠实翻译阶段输入**：
```json
{
  "1": {"origin": "Hello, I'm Andrew Ng.", "direct": "你好，我是吴恩达。"},
  "2": {"origin": "I'm a professor at Stanford.", "direct": "我是斯坦福大学的教授。"},
  "3": {"origin": "I work on neural networks.", "direct": "我从事神经网络研究。"}
}
```

**表达优化阶段输出**：
```json
{
  "1": {
    "origin": "Hello, I'm Andrew Ng.",
    "direct": "你好，我是吴恩达。",
    "reflect": "直译准确但略显生硬",
    "free": "大家好，我是吴恩达"
  },
  "2": {
    "origin": "I'm a professor at Stanford.",
    "direct": "我是斯坦福大学的教授。",
    "reflect": "翻译准确",
    "free": "我是斯坦福大学的教授"
  }
}
```

### 3. 结果重组处理

#### 重组函数：`translate_all()`

```python
def translate_all():
    # 并发翻译所有块
    chunks = split_chunks_by_chars(chunk_size=600, max_i=10)
    # ... 并发处理 ...
    
    # 重组结果
    src_text, trans_text = [], []
    for i, chunk in enumerate(chunks):
        chunk_lines = chunk.split('\n')
        src_text.extend(chunk_lines)  # 逐句添加原文
        
        # 匹配对应的翻译结果
        best_match = max(matching_results, key=lambda x: x[1])
        trans_text.extend(best_match[0][2].split('\n'))  # 逐句添加译文
    
    # 创建句子级数据框
    df_translate = pd.DataFrame({'Source': src_text, 'Translation': trans_text})
```

#### 重组特点

- **数据结构**：两个平行的句子列表
  - `src_text`：原文句子列表
  - `trans_text`：译文句子列表
- **长度保证**：两个列表长度完全一致
- **顺序保证**：句子顺序与原始顺序完全一致

### 4. 时间戳对齐机制

#### 核心函数：`get_sentence_timestamps()`

```python
def get_sentence_timestamps(df_words, df_sentences):
    """
    df_words: ASR词级时间戳数据 [word, start, end]
    df_sentences: 句子级数据 [Source, Translation]
    """
    time_stamp_list = []
    
    # 1. 构建完整词汇字符串和位置映射
    full_words_str = ''
    position_to_word_idx = {}
    
    for idx, word in enumerate(df_words['text']):
        clean_word = remove_punctuation(word.lower())
        start_pos = len(full_words_str)
        full_words_str += clean_word
        # 记录每个字符位置对应的词索引
        for pos in range(start_pos, len(full_words_str)):
            position_to_word_idx[pos] = idx
    
    # 2. 为每个句子找到对应的时间戳
    current_pos = 0
    for idx, sentence in df_sentences['Source'].items():
        clean_sentence = remove_punctuation(sentence.lower()).replace(" ", "")
        sentence_len = len(clean_sentence)
        
        # 在词汇字符串中查找匹配位置
        while current_pos <= len(full_words_str) - sentence_len:
            if full_words_str[current_pos:current_pos+sentence_len] == clean_sentence:
                # 找到匹配！获取开始和结束词的索引
                start_word_idx = position_to_word_idx[current_pos]
                end_word_idx = position_to_word_idx[current_pos + sentence_len - 1]
                
                # 从词级时间戳获取句子时间戳
                time_stamp_list.append((
                    float(df_words['start'][start_word_idx]),
                    float(df_words['end'][end_word_idx])
                ))
                
                current_pos += sentence_len
                break
            current_pos += 1
    
    return time_stamp_list
```

#### 对齐算法步骤

1. **构建词汇映射**
   - 将ASR的所有词汇连接成一个字符串
   - 记录每个字符位置对应的词索引

2. **句子匹配**
   - 对每个原文句子进行清理（去标点、转小写）
   - 在词汇字符串中查找完全匹配的位置

3. **时间戳提取**
   - 根据匹配位置确定句子的开始词和结束词
   - 从对应词汇的时间戳计算句子级时间戳

4. **时间戳分配**
   - 将句子时间戳分配给对应的翻译结果

#### 对齐示例

```
ASR词汇数据：
[{word: "Hello", start: 0.0, end: 0.5},
 {word: "I'm", start: 0.6, end: 0.8},
 {word: "Andrew", start: 0.9, end: 1.2},
 {word: "Ng", start: 1.3, end: 1.5}]

词汇字符串：
"helloimandrewng"
 ^     ^       ^
 0     4       9

句子："Hello, I'm Andrew Ng"
清理后："helloimandrewng"

匹配结果：
- 位置：0-14
- 开始词：Hello (start: 0.0)
- 结束词：Ng (end: 1.5)
- 句子时间戳：(0.0, 1.5)
```

### 5. 最终输出格式

#### 时间戳对齐函数：`align_timestamp()`

```python
def align_timestamp(df_text, df_translate, subtitle_output_configs, output_dir, for_display=True):
    df_trans_time = df_translate.copy()

    # 获取句子级时间戳
    time_stamp_list = get_sentence_timestamps(df_text, df_translate)
    df_trans_time['timestamp'] = time_stamp_list
    df_trans_time['duration'] = df_trans_time['timestamp'].apply(lambda x: x[1] - x[0])

    # 处理时间间隙
    for i in range(len(df_trans_time)-1):
        delta_time = df_trans_time.loc[i+1, 'timestamp'][0] - df_trans_time.loc[i, 'timestamp'][1]
        if 0 < delta_time < 1:
            df_trans_time.at[i, 'timestamp'] = (
                df_trans_time.loc[i, 'timestamp'][0], 
                df_trans_time.loc[i+1, 'timestamp'][0]
            )

    # 转换为SRT格式
    df_trans_time['timestamp'] = df_trans_time['timestamp'].apply(
        lambda x: convert_to_srt_format(x[0], x[1])
    )

    return df_trans_time
```

#### 最终数据结构

```python
df_trans_time = pd.DataFrame({
    'Source': ['Hello, I\'m Andrew Ng.', 'I\'m a professor at Stanford.', ...],
    'Translation': ['大家好，我是吴恩达', '我是斯坦福大学的教授', ...],
    'timestamp': ['00:00:00,000 --> 00:00:01,500', '00:00:01,600 --> 00:00:03,200', ...],
    'duration': [1.5, 1.6, ...]
})
```

---

## 设计优势

### 1. 翻译质量提升
- **上下文感知**：600字符分块提供丰富语境
- **术语一致性**：全局术语表确保专业词汇统一
- **风格连贯性**：前后文参考保持翻译风格一致

### 2. 时间戳精确性
- **句子级精度**：每句译文都有精确时间戳
- **边界对齐**：严格按句子边界分割，避免时间错位
- **智能间隙处理**：自动处理小于1秒的时间间隙

### 3. 系统稳定性
- **并发处理**：多线程翻译提高效率
- **错误恢复**：翻译失败自动重试机制
- **数据一致性**：严格验证原文与译文行数匹配

### 4. 可扩展性
- **模块化设计**：各步骤独立，便于优化
- **配置灵活**：支持不同分块大小和翻译模式
- **格式多样**：支持多种字幕格式输出

---

## 关键代码位置

| 功能模块 | 文件路径 | 核心函数 |
|---------|---------|---------|
| 文本分块 | `core/_4_2_translate.py` | `split_chunks_by_chars()` |
| 分块翻译 | `core/translate_lines.py` | `translate_lines()` |
| 结果重组 | `core/_4_2_translate.py` | `translate_all()` |
| 时间戳对齐 | `core/_6_gen_sub.py` | `get_sentence_timestamps()` |
| 上下文构建 | `core/prompts.py` | `generate_shared_prompt()` |

---

## 总结

VideoLingo 的分块翻译与时间戳对齐机制实现了一个巧妙的平衡：

1. **分块翻译**提供上下文，提升翻译质量
2. **句子边界保持**确保时间戳精确对齐
3. **并发处理**提高系统效率
4. **严格验证**保证数据一致性

这种设计使得 VideoLingo 能够在保持高翻译质量的同时，实现精确的字幕时间同步，是视频翻译系统的一个重要创新。 
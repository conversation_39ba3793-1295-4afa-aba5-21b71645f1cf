"dependencies": {
    "@julebu/shared": "workspace:*",
    "@langchain/core": "^0.2.6",
    "@langchain/openai": "^0.1.2",
    "@logto/vue": "^2.2.13",
    "@paralleldrive/cuid2": "^2.2.2",
    "@trpc/client": "^10.45.2",
    "@trpc/server": "^10.45.2",
    "@volcengine/openapi": "^1.20.0",
    "@vueuse/core": "^13.0.0",
    "animejs": "^3.2.2",
    "compromise": "^14.13.0",
    "corenlp-ts": "^0.0.1",
    "cos-js-sdk-v5": "^1.8.6",
    "cos-nodejs-sdk-v5": "^2.14.1",
    "date-fns": "^4.1.0",
    "drizzle-orm": "^0.35.1",
    "fuse.js": "^7.0.0",
    "jose": "^5.5.0",
    "lodash-es": "^4.17.21",
    "lru-cache": "^10.3.0",
    "mongoose": "^8.4.1",
    "mysql2": "^3.11.3",
    "naive-ui": "^2.38.2",
    "ofetch": "^1.4.1",
    "path-to-regexp": "^8.2.0",
    "pinia-plugin-persistedstate": "^4.1.3",
    "sharp": "^0.33.4",
    "superjson": "^2.2.1",
    "trpc-nuxt": "^0.10.21",
    "uuid": "^11.0.2",
    "vue-draggable-plus": "^0.5.0",
    "vue-loading-overlay": "^6.0",
    "vue-sonner": "^1.2.5",
    "youtube-transcript": "^1.2.1",
    "zod": "^3.23.8"
  },
  "devDependencies": {
    "@formkit/auto-animate": "^0.8.2",
    "@iconify-json/heroicons": "^1.2.1",
    "@iconify-json/ph": "^1.2.1",
    "@nuxt/content": "^2.12.1",
    "@nuxt/devtools": "^1.3.3",
    "@nuxt/eslint": "^0.3.13",
    "@nuxt/icon": "^1.8.2",
    "@nuxt/image": "^1.7.0",
    "@nuxt/ui": "^2.16.0",
    "@pinia/nuxt": "^0.5.1",
    "@types/archiver": "^6.0.2",
    "@types/fs-extra": "^11.0.4",
    "@types/inquirer": "^9.0.7",
    "@types/node": "^20.14.2",
    "@types/prettier": "^3.0.0",
    "@vueuse/nuxt": "^10.10.0",
    "archiver": "^7.0.1",
    "autoprefixer": "^10.4.19",
    "daisyui": "^4.12.2",
    "drizzle-kit": "0.26.2",
    "env-cmd": "^10.1.0",
    "fs-extra": "^11.2.0",
    "inquirer": "^10.1.8",
    "nuxt": "^3.11.2",
    "postcss": "^8.4.38",
    "prettier": "^3.3.1",
    "prettier-plugin-tailwindcss": "^0.5.14",
    "tailwindcss": "^3.4.14",
    "tsx": "^4.17.0",
    "typescript": "^4.9.5",
    "vitest": "^1.6.0"
  }
# VideoLingo 翻译实现流程分析

## 项目概述

VideoLingo 是一个基于 AI 的视频翻译和配音系统，其核心翻译模块采用了先进的多步骤翻译策略，从语义理解到最终译文生成，实现了高质量的视频字幕翻译。

## 整体架构

```
输入视频 → 语音识别 → 语义分割 → 摘要生成 → 翻译处理 → 字幕对齐 → 输出视频
                                    ↑
                               本文档重点分析
```

## 核心翻译流程

### 第一阶段：摘要生成 (`_4_1_summarize.py`)

#### 功能概述
从分段文本中提取主题摘要和专业术语，为后续翻译提供全局语境支持。

#### 详细流程

**1. 文本预处理**
```python
def combine_chunks():
    # 合并分段文本
    with open(_3_2_SPLIT_BY_MEANING, 'r', encoding='utf-8') as file:
        sentences = file.readlines()
    combined_text = ' '.join(cleaned_sentences)
    return combined_text[:load_key('summary_length')]  # 截取前N个字符
```

**2. 自定义术语加载**
- 从 `custom_terms.xlsx` 读取用户预定义术语
- 格式：`src (源语言)` | `tgt (目标语言)` | `note (说明)`

**3. LLM 摘要生成**
- **角色设定**：视频翻译专家和术语顾问
- **任务要求**：
  - 生成两句话的视频主题总结
  - 提取专业术语并提供翻译和解释
  - 排除已有的自定义术语
- **输出格式**：严格的 JSON 结构

**4. 质量验证**
```python
def valid_summary(response_data):
    required_keys = {'src', 'tgt', 'note'}
    # 验证格式完整性
    return {"status": "success/error", "message": "..."}
```

#### 输出文件
- `output/log/terminology.json`：包含主题摘要和术语表

---

### 第二阶段：翻译执行 (`_4_2_translate.py`)

#### 功能概述
采用分块并发翻译策略，确保翻译质量的同时提升处理效率。

#### 详细流程

**1. 智能分块**
```python
def split_chunks_by_chars(chunk_size=600, max_i=10):
    # 按字符数和句子数双重限制分块
    # 确保每块大小适中，便于LLM处理
```

**2. 上下文构建**
- **前文信息**：获取前一块的最后 3 行
- **后文信息**：获取后一块的前 2 行
- **术语信息**：从摘要阶段提取的专业术语
- **主题信息**：视频整体主题描述

**3. 并发翻译**
```python
with concurrent.futures.ThreadPoolExecutor(max_workers=load_key("max_workers")) as executor:
    futures = []
    for i, chunk in enumerate(chunks):
        future = executor.submit(translate_chunk, chunk, chunks, theme_prompt, i)
        futures.append(future)
```

**4. 结果匹配与验证**
- 使用相似度算法确保翻译结果与源文本正确对应
- 相似度阈值：0.9（低于此值会抛出异常）
- 自动排序确保翻译顺序正确

**5. 字幕优化**
- 对超长字幕进行智能修剪
- 考虑时长限制，保持语义完整

#### 输出文件
- `output/log/translation_results.xlsx`：完整翻译结果

---

### 第三阶段：逐行翻译核心 (`translate_lines.py`)

#### 功能概述
翻译模块的核心实现，采用创新的"两步翻译策略"确保译文质量。

#### 两步翻译策略

**步骤一：忠实翻译 (Faithfulness)**

*目标*：准确传达原文含义，保持术语一致性

*提示词特点*：
- 角色：专业 Netflix 字幕翻译师
- 要求：逐行忠实翻译，不改变、增加或省略内容
- 输出：JSON 格式，包含 `origin` 和 `direct` 字段

*关键原则*：
1. 忠实原文：准确传达内容和含义
2. 术语准确：正确使用专业术语并保持一致性
3. 理解语境：充分理解文本背景和上下文关系

**步骤二：表达优化 (Expressiveness)** *(可选)*

*目标*：提升译文自然性，符合目标语言表达习惯

*触发条件*：`config.yaml` 中 `reflect_translate: true`

*提示词特点*：
- 角色：语言顾问和优化专家
- 任务：基于忠实翻译进行表达优化
- 分析步骤：
  1. 评估语言流畅性
  2. 检查语言风格一致性
  3. 优化字幕简洁性
- 输出：JSON 格式，包含 `reflect` 和 `free` 字段

#### 质量保证机制

**1. 格式验证**
```python
def valid_translate_result(result: dict, required_keys: list, required_sub_keys: list):
    # 检查必需键值
    # 验证子键完整性
    return {"status": "success/error", "message": "..."}
```

**2. 重试机制**
- 翻译失败时最多重试 3 次
- 每次重试添加空格避免缓存
- 验证原文行数与译文行数一致

**3. 长度匹配**
```python
if len(lines.split('\n')) != len(translate_result.split('\n')):
    raise ValueError(f'Length Mismatch...')
```

---

## 提示词设计精髓

### 摘要提示词 (`get_summary_prompt`)

**设计理念**：全局理解 + 术语提取

**关键特性**：
- **角色精准定位**：视频翻译专家 + 术语顾问
- **任务层次化**：主题总结 → 术语提取 → 说明补充
- **输出标准化**：严格 JSON 格式约束
- **智能排重**：自动排除已有自定义术语

**示例结构**：
```json
{
  "theme": "视频主题的两句话总结",
  "terms": [
    {
      "src": "源语言术语",
      "tgt": "目标语言翻译", 
      "note": "简要说明"
    }
  ]
}
```

### 翻译提示词设计

**忠实翻译提示词** (`get_prompt_faithfulness`)
- **核心理念**：准确性优先
- **角色设定**：专业字幕翻译师
- **质量要求**：不改变、不增加、不省略

**表达优化提示词** (`get_prompt_expressiveness`)  
- **核心理念**：自然性提升
- **角色设定**：语言顾问
- **优化方向**：流畅性、风格一致性、简洁性

**共享上下文构建** (`generate_shared_prompt`)
```
### Context Information
<previous_content>前文内容</previous_content>
<subsequent_content>后文内容</subsequent_content>

### Content Summary
主题信息

### Points to Note  
术语信息
```

---

## 技术特性分析

### 1. 质量控制体系

| 控制点 | 机制 | 目的 |
|--------|------|------|
| 格式验证 | JSON 结构检查 | 确保输出可解析 |
| 长度匹配 | 行数一致性验证 | 防止遗漏或重复 |
| 相似度检查 | SequenceMatcher | 确保翻译对应关系 |
| 重试机制 | 最多 3 次重试 | 提高成功率 |

### 2. 性能优化策略

**并发处理**
- 使用 `ThreadPoolExecutor` 多线程翻译
- 工作线程数可配置（`max_workers`）
- 实时进度显示

**智能分块**
- 字符数限制：600 字符/块
- 句子数限制：10 句/块
- 避免 LLM token 限制

**上下文窗口**
- 前文：3 行
- 后文：2 行  
- 保持翻译连贯性

### 3. 配置灵活性

**语言配置**
```yaml
target_language: '简体中文'
whisper:
  detected_language: 'en'
```

**翻译策略**
```yaml
reflect_translate: true  # 是否启用两步翻译
pause_before_translate: false  # 是否在翻译前暂停
```

**性能调优**
```yaml
max_workers: 4  # 并发线程数
summary_length: 8000  # 摘要长度限制
```

---

## 数据流转图

```mermaid
graph TD
    A[原始分段文本] --> B[combine_chunks<br/>文本合并]
    B --> C[get_summary<br/>摘要生成]
    C --> D[terminology.json<br/>术语表]
    
    A --> E[split_chunks_by_chars<br/>智能分块]
    E --> F[translate_chunk<br/>并发翻译]
    D --> F
    
    F --> G[translate_lines<br/>逐行翻译]
    G --> H{reflect_translate?}
    
    H -->|true| I[两步翻译<br/>忠实+表达]
    H -->|false| J[单步翻译<br/>仅忠实]
    
    I --> K[结果匹配验证]
    J --> K
    K --> L[translation_results.xlsx<br/>最终翻译结果]
```

---

## 关键文件说明

| 文件路径 | 功能 | 关键方法 |
|----------|------|----------|
| `core/_4_1_summarize.py` | 摘要生成 | `get_summary()` |
| `core/_4_2_translate.py` | 翻译控制 | `translate_all()` |
| `core/translate_lines.py` | 核心翻译 | `translate_lines()` |
| `core/prompts.py` | 提示词管理 | `get_*_prompt()` |
| `config.yaml` | 配置管理 | 翻译参数配置 |
| `custom_terms.xlsx` | 自定义术语 | 用户术语表 |

---

## 输出文件结构

```
output/
├── log/
│   ├── terminology.json          # 摘要和术语表
│   ├── translation_results.xlsx  # 翻译结果
│   └── gpt_log/                  # GPT调用日志
│       ├── summary.json
│       ├── translate_faithfulness.json
│       └── translate_expressiveness.json
└── audio/                        # 音频相关文件
```

---

## 创新特点总结

1. **两步翻译策略**：忠实性与表达性的平衡
2. **全局语境感知**：摘要指导翻译过程
3. **术语一致性保证**：专业术语表管理
4. **智能质量控制**：多层验证机制
5. **高效并发处理**：提升翻译效率
6. **灵活配置设计**：适应不同翻译需求

VideoLingo 的翻译实现体现了现代 AI 翻译系统的先进理念：通过深度理解视频内容，结合专业术语管理，采用多步骤翻译策略，最终实现高质量、高效率的视频字幕翻译。 
# VideoLingo 逐行翻译核心逻辑总结

VideoLingo 项目的逐行翻译核心逻辑主要在 `core/translate_lines.py` 文件中实现，并依赖 `core/prompts.py` 生成的提示词。其核心采用创新的"两步翻译策略"来确保翻译的准确性和表达的自然流畅性。

## 执行流程概述

**重要说明**：逐行翻译是在并发翻译过程中执行的，而不是在并发翻译后执行的。

### 正确的执行时序

```
主线程: 文本分块 → 启动并发任务
         ↓
线程1: translate_chunk → translate_lines (两步翻译)
线程2: translate_chunk → translate_lines (两步翻译)  
线程3: translate_chunk → translate_lines (两步翻译)
线程4: translate_chunk → translate_lines (两步翻译)
         ↓
主线程: 收集结果 → 相似度匹配 → 结果整合 → 保存文件
```

### 详细执行流程

1. **文本分块** (`core/_4_2_translate.py` - `split_chunks_by_chars`)
2. **并发任务分发** (`core/_4_2_translate.py` - `translate_all`)
3. **逐行翻译** (`core/translate_lines.py` - `translate_lines`) - **在每个并发线程中执行**
4. **结果收集与整合** (`core/_4_2_translate.py` - 相似度匹配和数据整合)

## 一、并发翻译架构

### 主函数 - translate_all

```python
# core/_4_2_translate.py
@check_file_exists(_4_2_TRANSLATION)
def translate_all():
    console.print("[bold green]Start Translating All...[/bold green]")
    chunks = split_chunks_by_chars(chunk_size=600, max_i=10)
    with open(_4_1_TERMINOLOGY, 'r', encoding='utf-8') as file:
        theme_prompt = json.load(file).get('theme')

    # 🔄 Use concurrent execution for translation
    with Progress(SpinnerColumn(), TextColumn("[progress.description]{task.description}"), transient=True) as progress:
        task = progress.add_task("[cyan]Translating chunks...", total=len(chunks))
        with concurrent.futures.ThreadPoolExecutor(max_workers=load_key("max_workers")) as executor:
            futures = []
            for i, chunk in enumerate(chunks):
                # 每个并发任务都会调用 translate_chunk
                future = executor.submit(translate_chunk, chunk, chunks, theme_prompt, i)
                futures.append(future)
            results = []
            for future in concurrent.futures.as_completed(futures):
                results.append(future.result())
                progress.update(task, advance=1)

    results.sort(key=lambda x: x[0])  # Sort results based on original order
    # ... 后续结果处理 ...
```

### 单块翻译函数 - translate_chunk

```python
# core/_4_2_translate.py
def translate_chunk(chunk, chunks, theme_prompt, i):
    things_to_note_prompt = search_things_to_note_in_prompt(chunk)
    previous_content_prompt = get_previous_content(chunks, i)
    after_content_prompt = get_after_content(chunks, i)
    # 在这里调用逐行翻译核心函数！
    translation, english_result = translate_lines(chunk, previous_content_prompt, after_content_prompt, things_to_note_prompt, theme_prompt, i)
    return i, english_result, translation
```

## 二、逐行翻译核心逻辑

### 核心函数 - translate_lines

```python
# core/translate_lines.py
def translate_lines(lines, previous_content_prompt, after_cotent_prompt, things_to_note_prompt, summary_prompt, index = 0):
    shared_prompt = generate_shared_prompt(previous_content_prompt, after_cotent_prompt, summary_prompt, things_to_note_prompt)

    # Retry translation if the length of the original text and the translated text are not the same, or if the specified key is missing
    def retry_translation(prompt, length, step_name):
        def valid_faith(response_data):
            return valid_translate_result(response_data, [str(i) for i in range(1, length+1)], ['direct'])
        def valid_express(response_data):
            return valid_translate_result(response_data, [str(i) for i in range(1, length+1)], ['free'])
        for retry in range(3):
            if step_name == 'faithfulness':
                result = ask_gpt(prompt+retry* " ", resp_type='json', valid_def=valid_faith, log_title=f'translate_{step_name}')
            elif step_name == 'expressiveness':
                result = ask_gpt(prompt+retry* " ", resp_type='json', valid_def=valid_express, log_title=f'translate_{step_name}')
            if len(lines.split('\n')) == len(result):
                return result
            if retry != 2:
                console.print(f'[yellow]⚠️ {step_name.capitalize()} translation of block {index} failed, Retry...[/yellow]')
        raise ValueError(f'[red]❌ {step_name.capitalize()} translation of block {index} failed after 3 retries. Please check `output/gpt_log/error.json` for more details.[/red]')

    ## Step 1: Faithful to the Original Text
    prompt1 = get_prompt_faithfulness(lines, shared_prompt)
    faith_result = retry_translation(prompt1, len(lines.split('\n')), 'faithfulness')

    for i in faith_result:
        faith_result[i]["direct"] = faith_result[i]["direct"].replace('\n', ' ')

    # If reflect_translate is False or not set, use faithful translation directly
    reflect_translate = load_key('reflect_translate')
    if not reflect_translate:
        translate_result = "\n".join([faith_result[i]["direct"].strip() for i in faith_result])
        # ... display table ...
        return translate_result, lines

    ## Step 2: Express Smoothly  
    prompt2 = get_prompt_expressiveness(faith_result, lines, shared_prompt)
    express_result = retry_translation(prompt2, len(lines.split('\n')), 'expressiveness')

    # ... display table ...

    translate_result = "\n".join([express_result[i]["free"].replace('\n', ' ').strip() for i in express_result])

    if len(lines.split('\n')) != len(translate_result.split('\n')):
        console.print(Panel(f'[red]❌ Translation of block {index} failed, Length Mismatch, Please check `output/gpt_log/translate_expressiveness.json`[/red]'))
        raise ValueError(f'Origin ···{lines}···,\nbut got ···{translate_result}···')

    return translate_result, lines
```

## 三、两步翻译策略

### 步骤一：忠实翻译 (Faithfulness)

**目标**：准确无误地传达原文的含义，保持术语一致性

#### 提示词生成函数

```python
# core/prompts.py
def get_prompt_faithfulness(lines, shared_prompt):
    TARGET_LANGUAGE = load_key("target_language")
    line_splits = lines.split('\n')
    
    json_dict = {}
    for i, line in enumerate(line_splits, 1):
        json_dict[f"{i}"] = {"origin": line, "direct": f"direct {TARGET_LANGUAGE} translation {i}."}
    json_format = json.dumps(json_dict, indent=2, ensure_ascii=False)

    src_language = load_key("whisper.detected_language")
    prompt_faithfulness = f'''
## Role
You are a professional Netflix subtitle translator, fluent in both {src_language} and {TARGET_LANGUAGE}, as well as their respective cultures. 
Your expertise lies in accurately understanding the semantics and structure of the original {src_language} text and faithfully translating it into {TARGET_LANGUAGE} while preserving the original meaning.

## Task
We have a segment of original {src_language} subtitles that need to be directly translated into {TARGET_LANGUAGE}. These subtitles come from a specific context and may contain specific themes and terminology.

1. Translate the original {src_language} subtitles into {TARGET_LANGUAGE} line by line
2. Ensure the translation is faithful to the original, accurately conveying the original meaning
3. Consider the context and professional terminology

{shared_prompt}

<translation_principles>
1. Faithful to the original: Accurately convey the content and meaning of the original text, without arbitrarily changing, adding, or omitting content.
2. Accurate terminology: Use professional terms correctly and maintain consistency in terminology.
3. Understand the context: Fully comprehend and reflect the background and contextual relationships of the text.
</translation_principles>

## INPUT
<subtitles>
{lines}
</subtitles>

## Output in only JSON format and no other text
```json
{json_format}
```

Note: Start you answer with ```json and end with ```, do not add any other text.
'''
    return prompt_faithfulness.strip()
```

#### 关键特性

- **角色设定**：专业 Netflix 字幕翻译师，精通源语言和目标语言
- **翻译原则**：忠实原文、术语准确、理解语境
- **输出格式**：JSON 格式，包含 `origin` (原文) 和 `direct` (直接翻译)
- **处理逻辑**：对结果中的换行符进行处理，替换为空格

### 步骤二：表达优化 (Expressiveness)

**目标**：在忠实翻译基础上优化表达自然性，符合目标语言习惯

**触发条件**：`config.yaml` 中 `reflect_translate: true`

#### 提示词生成函数

```python
# core/prompts.py
def get_prompt_expressiveness(faithfulness_result, lines, shared_prompt):
    TARGET_LANGUAGE = load_key("target_language")
    json_format = {
        key: {
            "origin": value["origin"],
            "direct": value["direct"],
            "reflect": "your reflection on direct translation",
            "free": "your free translation"
        }
        for key, value in faithfulness_result.items()
    }
    json_format = json.dumps(json_format, indent=2, ensure_ascii=False)

    src_language = load_key("whisper.detected_language")
    prompt_expressiveness = f'''
## Role
You are a professional Netflix subtitle translator and language consultant.
Your expertise lies not only in accurately understanding the original {src_language} but also in optimizing the {TARGET_LANGUAGE} translation to better suit the target language's expression habits and cultural background.

## Task
We already have a direct translation version of the original {src_language} subtitles.
Your task is to reflect on and improve these direct translations to create more natural and fluent {TARGET_LANGUAGE} subtitles.

1. Analyze the direct translation results line by line, pointing out existing issues
2. Provide detailed modification suggestions
3. Perform free translation based on your analysis
4. Do not add comments or explanations in the translation, as the subtitles are for the audience to read
5. Do not leave empty lines in the free translation, as the subtitles are for the audience to read

{shared_prompt}

<Translation Analysis Steps>
Please use a two-step thinking process to handle the text line by line:

1. Direct Translation Reflection:
   - Evaluate language fluency
   - Check if the language style is consistent with the original text
   - Check the conciseness of the subtitles, point out where the translation is too wordy

2. {TARGET_LANGUAGE} Free Translation:
   - Aim for contextual smoothness and naturalness, conforming to {TARGET_LANGUAGE} expression habits
   - Ensure it's easy for {TARGET_LANGUAGE} audience to understand and accept
   - Adapt the language style to match the theme (e.g., use casual language for tutorials, professional terminology for technical content, formal language for documentaries)
</Translation Analysis Steps>
   
## INPUT
<subtitles>
{lines}
</subtitles>

## Output in only JSON format and no other text
```json
{json_format}
```

Note: Start you answer with ```json and end with ```, do not add any other text.
'''
    return prompt_expressiveness.strip()
```

#### 关键特性

- **角色设定**：专业字幕翻译师和语言顾问
- **输入依赖**：基于第一步忠实翻译的结果
- **分析步骤**：
  1. **直接翻译反思**：评估流畅性、风格一致性、简洁性
  2. **自由翻译优化**：追求自然流畅、易于理解、风格适配
- **输出格式**：JSON 格式，包含 `origin`、`direct`、`reflect`、`free`
- **最终输出**：采用 `free` 字段作为最终翻译结果

## 四、上下文构建机制

### 共享上下文生成

```python
# core/prompts.py
def generate_shared_prompt(previous_content_prompt, after_content_prompt, summary_prompt, things_to_note_prompt):
    return f'''### Context Information
<previous_content>
{previous_content_prompt}
</previous_content>

<subsequent_content>
{after_content_prompt}
</subsequent_content>

### Content Summary
{summary_prompt}

### Points to Note
{things_to_note_prompt}'''
```

### 上下文获取函数

```python
# core/_4_2_translate.py
def get_previous_content(chunks, chunk_index):
    return None if chunk_index == 0 else chunks[chunk_index - 1].split('\n')[-3:] # Get last 3 lines

def get_after_content(chunks, chunk_index):
    return None if chunk_index == len(chunks) - 1 else chunks[chunk_index + 1].split('\n')[:2] # Get first 2 lines
```

### 上下文信息组成

1. **前文信息**：前一块的最后3行，提供语境背景
2. **后文信息**：后一块的前2行，提供前瞻性信息
3. **内容摘要**：来自摘要阶段的主题信息
4. **专业术语**：从当前块中搜索的相关术语

## 五、质量保证机制

### 1. 格式验证

```python
# core/translate_lines.py
def valid_translate_result(result: dict, required_keys: list, required_sub_keys: list):
    # Check for the required key
    if not all(key in result for key in required_keys):
        return {"status": "error", "message": f"Missing required key(s): {', '.join(set(required_keys) - set(result.keys()))}"}
    
    # Check for required sub-keys in all items
    for key in result:
        if not all(sub_key in result[key] for sub_key in required_sub_keys):
            return {"status": "error", "message": f"Missing required sub-key(s) in item {key}: {', '.join(set(required_sub_keys) - set(result[key].keys()))}"}

    return {"status": "success", "message": "Translation completed"}
```

**功能**：检查 LLM 返回的 JSON 数据格式完整性

### 2. 重试机制

```python
# core/translate_lines.py (within translate_lines function)
def retry_translation(prompt, length, step_name):
    def valid_faith(response_data):
        return valid_translate_result(response_data, [str(i) for i in range(1, length+1)], ['direct'])
    def valid_express(response_data):
        return valid_translate_result(response_data, [str(i) for i in range(1, length+1)], ['free'])
    for retry in range(3):
        if step_name == 'faithfulness':
            result = ask_gpt(prompt+retry* " ", resp_type='json', valid_def=valid_faith, log_title=f'translate_{step_name}')
        elif step_name == 'expressiveness':
            result = ask_gpt(prompt+retry* " ", resp_type='json', valid_def=valid_express, log_title=f'translate_{step_name}')
        if len(lines.split('\n')) == len(result):
            return result
        if retry != 2:
            console.print(f'[yellow]⚠️ {step_name.capitalize()} translation of block {index} failed, Retry...[/yellow]')
    raise ValueError(f'[red]❌ {step_name.capitalize()} translation of block {index} failed after 3 retries. Please check `output/gpt_log/error.json` for more details.[/red]')
```

**特性**：
- 最多重试3次
- 每次重试添加空格避免缓存
- 验证行数匹配
- 失败后抛出详细错误信息

### 3. 长度匹配验证

```python
# core/translate_lines.py (at the end of translate_lines function)
if len(lines.split('\n')) != len(translate_result.split('\n')):
    console.print(Panel(f'[red]❌ Translation of block {index} failed, Length Mismatch, Please check `output/gpt_log/translate_expressiveness.json`[/red]'))
    raise ValueError(f'Origin ···{lines}···,\nbut got ···{translate_result}···')
```

**功能**：确保最终输出的翻译文本行数与原始文本行数完全一致

## 六、智能分块算法

### 分块函数

```python
# core/_4_2_translate.py
def split_chunks_by_chars(chunk_size, max_i): 
    """Split text into chunks based on character count, return a list of multi-line text chunks"""
    with open(_3_2_SPLIT_BY_MEANING, "r", encoding="utf-8") as file:
        sentences = file.read().strip().split('\n')

    chunks = []
    chunk = ''
    sentence_count = 0
    for sentence in sentences:
        if len(chunk) + len(sentence + '\n') > chunk_size or sentence_count == max_i:
            chunks.append(chunk.strip())
            chunk = sentence + '\n'
            sentence_count = 1
        else:
            chunk += sentence + '\n'
            sentence_count += 1
    chunks.append(chunk.strip())
    return chunks
```

### 双重限制机制

| 限制类型 | 默认值 | 作用 | 优势 |
|---------|-------|------|------|
| **字符数限制** | 600字符 | 防止LLM token溢出 | 确保处理效率 |
| **句子数限制** | 10句话 | 保持语义完整性 | 避免句子截断 |

## 七、结果整合与验证

### 相似度匹配

```python
# core/_4_2_translate.py
def similar(a, b):
    return SequenceMatcher(None, a, b).ratio()

# 在 translate_all 函数中
for i, chunk in enumerate(chunks):
    chunk_lines = chunk.split('\n')
    src_text.extend(chunk_lines)
    
    # Calculate similarity between current chunk and translation results
    chunk_text = ''.join(chunk_lines).lower()
    matching_results = [(r, similar(''.join(r[1].split('\n')).lower(), chunk_text)) 
                      for r in results]
    best_match = max(matching_results, key=lambda x: x[1])
    
    # Check similarity and handle exceptions
    if best_match[1] < 0.9:
        console.print(f"[yellow]Warning: No matching translation found for chunk {i}[/yellow]")
        raise ValueError(f"Translation matching failed (chunk {i})")
    elif best_match[1] < 1.0:
        console.print(f"[yellow]Warning: Similar match found (chunk {i}, similarity: {best_match[1]:.3f})[/yellow]")
        
    trans_text.extend(best_match[0][2].split('\n'))
```

**质量控制标准**：
- 相似度 = 1.0：完美匹配，直接使用
- 相似度 ≥ 0.9：可接受匹配，使用并警告
- 相似度 < 0.9：匹配失败，抛出异常

## 八、技术特性总结

### 核心优势

1. **两步翻译策略**：忠实翻译 + 表达优化，确保准确性和流畅性
2. **并发处理**：多线程并行翻译，提高处理效率
3. **智能分块**：双重限制机制，平衡处理效率和语义完整性
4. **上下文感知**：前后文信息+主题摘要+术语信息，保证翻译连贯性
5. **质量保证**：多层验证机制，确保输出质量

### 配置灵活性

- **翻译模式**：可通过 `reflect_translate` 开关选择单步或两步翻译
- **并发控制**：通过 `max_workers` 配置并发线程数
- **语言配置**：支持多种源语言和目标语言组合
- **分块参数**：可调整字符数和句子数限制

### 输出文件

- **主要输出**：`output/log/translation_results.xlsx` - 完整翻译结果
- **日志输出**：`output/gpt_log/` - 详细的翻译过程日志
- **错误记录**：`output/gpt_log/error.json` - 翻译失败的详细信息

通过这种精心设计的架构，VideoLingo 能够高效地处理长视频的翻译任务，同时通过两步翻译策略和严格的质量控制来保证最终译文的专业性和可读性。 
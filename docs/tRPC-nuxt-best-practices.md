# tRPC 在 Nuxt 中的请求最佳实践

## 概述

本文档总结了在 Nuxt 3 项目中使用 tRPC 发送请求的最佳实践，包括常见问题的分析和解决方案。

## 目录

- [核心问题分析](#核心问题分析)
- [最佳实践模式](#最佳实践模式)
- [什么时候使用什么方案](#什么时候使用什么方案)
- [实际案例对比](#实际案例对比)
- [常见错误和解决方法](#常见错误和解决方法)
- [项目配置要点](#项目配置要点)

## 核心问题分析

### 常见错误：tRPC 客户端未初始化

**错误信息：**
```
throw new Error('tRPC 客户端未初始化，请确保只在客户端调用')
```

**产生原因：**
1. `useLazyAsyncData` 或 `useAsyncData` 在服务器端执行
2. tRPC 客户端插件配置为仅在客户端可用（`if (import.meta.client)`）
3. 服务器端无法访问 `nuxtApp.$trpc`

**根本原因：**
```typescript
// plugins/trpc.client.ts
export default defineNuxtPlugin(() => {
  if (import.meta.client) {  // 🔍 关键：只在客户端初始化
    const trpc = createTRPCNuxtClient<AppRouter>({...})
    return { provide: { trpc } }
  }
})
```

## 最佳实践模式

### 1. 直接客户端调用模式 (推荐用于用户交互)

**适用场景：** 搜索、表单提交、用户触发的操作

```typescript
<script setup lang="ts">
// ✅ 正确方式
const { $trpc } = useNuxtApp()

// 响应式状态管理
const searchData = ref<NeteaseSearchListSchemaType | null>(null)
const searchStatus = ref<'idle' | 'pending' | 'success' | 'error'>('idle')
const searchError = ref<Error | null>(null)

async function searchNeteaseSongs() {
  if (!neteaseQuery.value.trim()) return
  
  // 确保只在客户端调用
  if (!import.meta.client) return
  
  searchStatus.value = 'pending'
  searchError.value = null
  searchData.value = null
  
  try {
    const result = await $trpc.netease.search.query({
      q: neteaseQuery.value,
      page: 1,
      limit: 10,
    })
    
    searchData.value = result
    searchStatus.value = 'success'
  } catch (error) {
    console.error('搜索歌曲失败:', error)
    searchError.value = error instanceof Error ? error : new Error(String(error))
    searchStatus.value = 'error'
  }
}
</script>
```

### 2. useAsyncData 响应式模式 (推荐用于响应式数据获取)

**适用场景：** 基于某个值变化自动触发的数据获取

```typescript
<script setup lang="ts">
// ✅ 正确的 useAsyncData 配置
const selectedSongId = ref<string>('')
const { $trpc } = useNuxtApp()

const {
  data: lyricsData,
  status: lyricsStatus,
  error: lyricsError,
} = useAsyncData(
  'netease-lyrics',
  async () => {
    if (!selectedSongId.value || import.meta.server) return null
    
    const result = await $trpc.netease.lyric.query({
      id: selectedSongId.value,
    })
    return result
  },
  {
    watch: [selectedSongId],  // 响应式触发
    immediate: false,         // 不立即执行
    server: false,           // 不在服务器端执行
  },
)
</script>
```

## 什么时候使用什么方案

### 使用直接调用模式的场景

✅ **适合：**
- 用户点击按钮触发的搜索
- 表单提交
- 手动刷新数据
- 一次性操作
- 简单的状态管理需求

```typescript
// 示例：搜索功能
async function handleSearch() {
  if (!import.meta.client) return
  
  isLoading.value = true
  try {
    const result = await $trpc.search.query(params)
    data.value = result
  } finally {
    isLoading.value = false
  }
}
```

### 使用 useAsyncData 的场景

✅ **适合：**
- 需要响应式触发的数据获取
- 需要缓存机制
- 复杂的状态管理
- 基于路由参数自动加载数据
- 需要 SSR 支持的场景（配合 RESTful API）

```typescript
// 示例：响应式歌词获取
const { data, status, error } = useAsyncData(
  'song-lyrics',
  () => $trpc.lyrics.query({ id: songId.value }),
  {
    watch: [songId],
    server: false,
  }
)
```

### 不推荐的用法

❌ **错误示例：**
```typescript
// 🚫 错误：在 useLazyAsyncData 中直接调用 tRPC（会在服务器端执行）
const { data } = await useLazyAsyncData(
  'search-data',
  async () => {
    const neteaseMusic = useNeteaseMusic()  // 💥 服务器端会报错
    return await neteaseMusic.search(params)
  }
)
```

## 实际案例对比

### 案例 1：搜索功能重构

**问题代码：**
```typescript
// ❌ 错误的实现
const neteaseMusic = useNeteaseMusic()
const { data: searchData, execute } = await useLazyAsyncData(
  'netease-search',
  async () => {
    const result = await neteaseMusic?.search(params)
    return result?.data?.value
  },
  { server: false }
)
```

**解决方案：**
```typescript
// ✅ 正确的实现
const { $trpc } = useNuxtApp()
const searchData = ref(null)
const searchStatus = ref('idle')

async function searchSongs() {
  if (!import.meta.client) return
  
  searchStatus.value = 'pending'
  try {
    const result = await $trpc.netease.search.query(params)
    searchData.value = result
    searchStatus.value = 'success'
  } catch (error) {
    searchStatus.value = 'error'
  }
}
```

### 案例 2：响应式歌词获取

**正确实现：**
```typescript
// ✅ 使用 useAsyncData 进行响应式数据获取
const selectedSongId = ref('')
const { data: lyricsData } = useAsyncData(
  'lyrics',
  () => {
    if (!selectedSongId.value) return null
    return $trpc.lyrics.query({ id: selectedSongId.value })
  },
  {
    watch: [selectedSongId],
    immediate: false,
    server: false,
  }
)
```

## 常见错误和解决方法

### 1. tRPC 客户端未初始化错误

**问题：** 在服务器端调用 tRPC 客户端

**解决方法：**
```typescript
// ✅ 添加客户端检查
if (!import.meta.client) return

// ✅ 配置 useAsyncData
const { data } = useAsyncData('key', fetcher, {
  server: false  // 关键配置
})
```

### 2. TypeScript 类型错误

**问题：** tRPC 返回类型不匹配

**解决方法：**
```typescript
// ✅ 正确的类型定义
import type { NeteaseSearchListSchemaType } from '~/server/trpc/schemas/api/song'

const searchData = ref<NeteaseSearchListSchemaType | null>(null)
const searchError = ref<Error | null>(null)

// ✅ 正确的错误处理
catch (error) {
  searchError.value = error instanceof Error ? error : new Error(String(error))
}
```

### 3. Composable 设计错误

**问题代码：**
```typescript
// ❌ 错误：在 composable 中使用 useQuery
export const useNeteaseMusic = () => {
  const trpc = useTRPC()
  return {
    search: (input) => trpc?.netease.search.useQuery(input),  // 💥 错误
  }
}
```

**正确方法：**
```typescript
// ✅ 正确：返回直接调用函数
export const useNeteaseMusic = () => {
  const { $trpc } = useNuxtApp()
  return {
    search: (input) => $trpc.netease.search.query(input),  // ✅ 正确
  }
}
```

## 项目配置要点

### 1. tRPC 客户端插件配置

```typescript
// plugins/trpc.client.ts
export default defineNuxtPlugin(() => {
  if (import.meta.client) {  // 🔍 关键：只在客户端初始化
    const trpc = createTRPCNuxtClient<AppRouter>({
      links: [
        splitLink({
          condition: (op) => isNonJsonSerializable(op.input),
          true: httpLink({ url: '/api/trpc' }),
          false: httpBatchLink({ url: '/api/trpc' }),
        }),
      ],
    })

    return { provide: { trpc } }
  }
})
```

### 2. Nuxt 配置

```typescript
// nuxt.config.ts
export default defineNuxtConfig({
  build: {
    transpile: ['trpc-nuxt'],  // 确保 tRPC 正确构建
  },
})
```

### 3. 服务器端路由配置

```typescript
// server/api/trpc/[trpc].ts
import { createTRPCNuxtHandler } from 'trpc-nuxt/server'
import { appRouter } from '~/server/trpc/router'
import { createContext } from '~/server/trpc/context'

export default createTRPCNuxtHandler({
  router: appRouter,
  createContext,
})
```

## 最佳实践总结

### DO ✅

1. **客户端检查**：始终在 tRPC 调用前检查 `import.meta.client`
2. **正确的状态管理**：根据场景选择手动状态管理或 `useAsyncData`
3. **类型安全**：使用正确的 TypeScript 类型定义
4. **错误处理**：实现完整的错误处理和用户反馈
5. **性能优化**：合理使用缓存机制

### DON'T ❌

1. **避免服务器端调用**：不要在服务器端调用仅客户端可用的 tRPC
2. **避免过度设计**：简单场景不要强行使用 `useAsyncData`
3. **避免混合模式**：不要同时使用多种数据获取方式
4. **避免类型错误**：不要忽略 TypeScript 类型检查

## 相关资源

- [tRPC 官方文档](https://trpc.io/)
- [trpc-nuxt 文档](https://trpc-nuxt.vercel.app/)
- [Nuxt 3 数据获取文档](https://nuxt.com/docs/getting-started/data-fetching)

---

**文档版本：** 1.0  
**最后更新：** 2025-01-30  
**适用项目：** Nuxt 3 + tRPC 
## WaveSurfer 波形图空白问题修复总结

**问题描述：**

在 `components/AudioWaveform.vue` 组件中，加载音频文件后，WaveSurfer 波形图区域显示为空白。

**根本原因：**

组件使用了 `v-if="isLoading"` 来控制加载状态的显示。当 `isLoading` 为 `true` 时（即音频加载期间），包含 WaveSurfer 容器元素 (`waveformRef`) 的模板部分会从 DOM 中完全移除。当音频加载完成 (`ready` 事件触发)，`isLoading` 变为 `false`，容器元素被重新插入 DOM。但在此时，WaveSurfer 实例尝试获取容器尺寸时，由于元素刚被渲染，浏览器可能尚未完成布局计算，导致获取到的 `clientWidth` 和 `clientHeight` 为 `undefined`。没有有效的尺寸信息，WaveSurfer 无法绘制波形。

**解决方案：**

1.  **保持 DOM 元素存在**：将控制加载指示器显隐的指令从 `v-if="isLoading"` 改为 `v-show="isLoading"`。同时，将包裹波形图和时间轴容器 (`waveformRef`, `timelineRef`) 的 `<template v-else>` 替换为一个普通的 `div`，并为其添加 `v-show="!isLoading"` 指令。这样可以确保即使在加载状态下，容器元素也始终存在于 DOM 树中，只是视觉上被隐藏。

2.  **移除冗余代码**：删除了 `wavesurfer.value.on('ready', ...)` 事件回调中尝试通过 `setOptions` 强制重新渲染波形的代码块。因为容器尺寸问题解决后，这部分代码不再需要，且可能引入其他问题。

通过这些修改，确保了 WaveSurfer 在 `ready` 事件触发时能够访问到其容器元素的有效尺寸，从而成功绘制波形图。

**修改前后的代码对比：**

修改前：
```vue
<template>
  <div class="w-full h-full flex flex-col items-center justify-center bg-gray-50 rounded-lg">
    <div v-if="isLoading" class="flex flex-col items-center justify-center text-gray-500">
      <UIcon name="i-heroicons-arrow-path" class="animate-spin h-8 w-8 mb-2" />
      <span>加载中...</span>
    </div>
    <template v-else>
      <div ref="waveformRef" class="w-full flex-grow" />
      <div ref="timelineRef" class="w-full h-8" />
    </template>
  </div>
</template>
```

修改后：
```vue
<template>
  <div class="w-full h-full flex flex-col items-center justify-center bg-gray-50 rounded-lg">
    <div v-show="isLoading" class="flex flex-col items-center justify-center text-gray-500">
      <UIcon name="i-heroicons-arrow-path" class="animate-spin h-8 w-8 mb-2" />
      <span>加载中...</span>
    </div>
    <div v-show="!isLoading" class="w-full h-full flex flex-col">
      <div ref="waveformRef" class="w-full flex-grow" />
      <div ref="timelineRef" class="w-full h-8" />
    </div>
  </div>
</template>
```

**技术要点：**

1. `v-if` vs `v-show` 的区别：
   - `v-if` 会完全移除/添加元素到 DOM
   - `v-show` 只是通过 CSS 的 `display` 属性控制元素的显示/隐藏
   
2. DOM 元素尺寸计算时机：
   - 浏览器需要一定时间完成新插入元素的布局计算
   - 立即获取新插入元素的尺寸可能会得到 `undefined`

3. Vue 模板结构优化：
   - 使用合适的容器元素包裹相关内容
   - 避免不必要的 `<template>` 标签
   - 保持 DOM 结构的稳定性 
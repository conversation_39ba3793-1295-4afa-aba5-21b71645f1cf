# Next.js 到 Nuxt 3 迁移任务清单

**本文档列出了从 Next.js 到 Nuxt 3 迁移过程中的具体任务和实施步骤。**

**技术实现细节和指南，请参考：[实现指南](./2-实现指南.md)**

## 1 基础设施任务

1. [x] 项目初始化

   - [x] 创建 Nuxt 3 项目
   - [x] 配置 TypeScript
   - [x] 配置 ESLint 和 Prettier
   - [x] 设置开发环境

2. [x] 依赖迁移
   - [x] 核心依赖更新
   - [x] UI 组件库迁移
   - [x] 开发工具配置

## 2 目录结构迁移

1. [x] 路由迁移

   - [x] App Router (`app/`) 转换为页面路由 (`pages/`)
   - [x] 布局文件迁移 (`app/layout.tsx` -> `layouts/default.vue`)
   - [x] 全局配置迁移 (`app.vue`)

2. [ ] 组件迁移

   - [ ] 核心功能组件迁移
     - [~] LRC 显示组件 (基础展示)
     - [ ] 视频播放器组件
     - [ ] 波形可视化组件
     - [ ] 字幕编辑组件
   - [ ] UI 基础组件迁移
     - [ ] 表单组件（Input、Textarea、Checkbox等）
     - [ ] 对话框组件（Dialog、AlertDialog）
     - [ ] 交互组件（Dropdown、Tooltip等）
     - [ ] Toast 通知组件

## 3 功能迁移

1. [ ] 状态管理迁移

   - [ ] Context API 转换为 Pinia stores
     - [ ] 字幕上下文迁移 (`context/subtitle-context.tsx`)
   - [ ] 可撤销状态迁移

2. [ ] 业务逻辑迁移

   - [ ] 工具函数迁移 (`lib/` -> `utils/`)
     - [ ] LRC 操作
     - [ ] 字幕操作
     - [ ] 通用工具
   - [ ] 服务层创建 (`services/`)
     - [ ] LRC 服务
     - [ ] 字幕服务

3. [ ] Hooks 迁移
   - [ ] Toast Hook 迁移
   - [ ] 可撤销状态 Hook 迁移

## 4 资源迁移

1. [ ] 静态资源迁移
   - [ ] 公共资源迁移 (`public/`)
   - [ ] 样式迁移 (`app/globals.css` -> `assets/css/`)

2. [ ] 类型定义迁移
   - [ ] 基础类型定义
   - [ ] 组件类型定义
   - [ ] API 类型定义

## 5 优化任务

1. [ ] 性能优化

   - [ ] 组件懒加载
   - [ ] 资源加载优化
   - [ ] 状态管理优化

2. [ ] 测试迁移

   - [ ] 单元测试框架设置
   - [ ] 组件测试迁移
   - [ ] 集成测试迁移
   - [ ] E2E 测试迁移

3. [ ] 文档更新
   - [ ] API 文档更新
   - [ ] 组件文档编写
   - [ ] 开发指南完善

## 6 进度追踪

- 开始日期：[待定]
- 预计完成：[待定]
- 当前阶段：[准备阶段]
- 完成度：0%

## 7 注意事项

1. 每个任务完成后进行充分测试
2. 保持与原功能的一致性
3. 记录所有重要决策和变更
4. 定期同步进度和问题
5. 注意 Vue 3 和 React 的概念差异
6. 确保类型安全

## 8 相关资源

- [Nuxt 3 文档](https://nuxt.com/docs)
- [Vue 3 文档](https://vuejs.org/guide/introduction.html)
- [Pinia 文档](https://pinia.vuejs.org/)
- [Vue Test Utils](https://test-utils.vuejs.org/)
- [项目 Git 仓库]()

## 9 更新记录

- [日期] 创建文档
- [日期] 更新任务列表
- [日期] 重组任务结构
- [日期] 细化迁移步骤

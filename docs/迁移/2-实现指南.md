# 迁移实现指南

**本文档提供从 Next.js 到 Nuxt 3 迁移过程中的技术实现细节和最佳实践指导。**

**源项目目录 (待迁移功能来源):** `/Users/<USER>/code/julebu/julebu-c`

**任务清单请参考：[任务清单](./1-任务清单.md)**

## 1 基础设施迁移

### 1.1 项目初始化

1. 创建 Nuxt 3 项目
```bash
npx nuxi init julebu-srt-editor
```

2. 项目结构对应关系
```
Next.js (App Router)  Nuxt 3
app/                 pages/
app/layout.tsx       layouts/default.vue
components/          components/
components/ui/       components/ui/
lib/                 utils/
hooks/               composables/
context/             stores/
public/              public/
types/               types/
-                    services/
```

3. 开发环境配置
```ts
// nuxt.config.ts
export default defineNuxtConfig({
  devtools: { enabled: true },
  typescript: {
    strict: true,
    typeCheck: true
  },
  modules: [
    '@pinia/nuxt',
    '@nuxtjs/tailwindcss'
  ]
})
```

### 1.2 依赖迁移

1. 核心依赖对应
```json
{
  "dependencies": {
    "next": "13.x" -> "nuxt": "^3.9",
    "react": "18.x" -> "vue": "^3.4",
    "react-dom": "18.x" -> 不需要（Vue 已包含）,
    "zustand": "4.x" -> "@pinia/nuxt": "^0.5",
    "@trpc/client": "10.x" -> "trpc-nuxt": "^0.10",
    "wavesurfer.js": "7.x" -> "wavesurfer.js": "^7.0",
    "animejs": "3.x" -> "animejs": "^3.2"
  },
  "devDependencies": {
    "@types/react": "18.x" -> 不需要,
    "@types/react-dom": "18.x" -> 不需要,
    "eslint": "8.x" -> "eslint": "^8.x",
    "typescript": "5.x" -> "typescript": "^5.x"
  }
}
```

2. UI 框架迁移
- Tailwind CSS 配置保持不变
- 基础 UI 组件迁移（从 `components/ui/` 目录）
- 考虑使用 Nuxt UI 补充缺失组件

### 1.3 配置迁移

1. TypeScript 配置
```ts
// tsconfig.json
{
  // Nuxt 3 会自动生成基础配置
  "extends": "./.nuxt/tsconfig.json",
  "compilerOptions": {
    "strict": true,
    "skipLibCheck": true,
    "types": [
      "vue",
      "@pinia/nuxt",
      "@nuxt/types"
    ]
  }
}
```

2. ESLint 配置
```js
// eslint.config.mjs
export default defineNuxtConfig({
  extends: [
    '@nuxt/eslint-config'
  ],
  rules: {
    // 自定义规则
  }
})
```

3. 样式配置
```ts
// nuxt.config.ts
export default defineNuxtConfig({
  css: [
    '~/assets/css/main.css'  // 原 app/globals.css
  ],
  postcss: {
    plugins: {
      tailwindcss: {},
      autoprefixer: {}
    }
  }
})
```

## 2 核心功能迁移

### 2.1 路由系统

1. App Router 到 Pages 的转换
```
源目录结构                    目标结构
app/
├── layout.tsx              layouts/default.vue
├── page.tsx               pages/index.vue
└── faq/
    └── page.tsx           pages/faq.vue
```

2. 布局文件迁移
```vue
<!-- layouts/default.vue -->
<script setup lang="ts">
// 原 app/layout.tsx 的逻辑
</script>

<template>
  <div>
    <!-- 通用布局 -->
    <slot />
  </div>
</template>
```

3. 页面组件迁移
```vue
<!-- pages/index.vue -->
<script setup lang="ts">
// 原 app/page.tsx 的逻辑转换为 Vue 组合式 API
</script>

<template>
  <!-- 页面内容 -->
</template>
```

### 2.2 组件迁移

1. 核心功能组件
   - LRC 显示组件
     - 源: `components/lrc-display.tsx`
     - 目标: `components/LrcDisplay.vue` (示例见下文)
   - 音频播放器组件
     - 源: `components/video-player.tsx`
     - 目标: `components/VideoPlayer.vue`
   - 波形可视化组件
     - 源: `components/waveform-visualizer.tsx`
     - 目标: `components/WaveformVisualizer.vue` (示例见 3.1 节)
   - 字幕编辑组件
     - 源: `components/subtitle-list.tsx`
     - 目标: `components/SubtitleList.vue`

2. UI 组件迁移 (示例)
   - 表单组件
     - 源: `components/ui/input.tsx`, `components/ui/textarea.tsx`, `components/ui/checkbox.tsx`, `components/ui/label.tsx`
     - 目标: `components/ui/Input.vue`, `components/ui/Textarea.vue`, `components/ui/Checkbox.vue`, `components/ui/Label.vue`
   - 对话框组件
     - 源: `components/ui/dialog.tsx`, `components/ui/alert-dialog.tsx`
     - 目标: `components/ui/Dialog.vue`, `components/ui/AlertDialog.vue`
   - 交互组件
     - 源: `components/ui/dropdown-menu.tsx`, `components/ui/tooltip.tsx`, `components/ui/button.tsx`
     - 目标: `components/ui/DropdownMenu.vue`, `components/ui/Tooltip.vue`, `components/ui/Button.vue`
   - Toast 通知组件
     - 源: `components/ui/toast.tsx` (与 `hooks/use-toast.ts` 配合)
     - 目标: `components/ui/Toast.vue` (与 `composables/useToast.ts` 配合)

```vue
<!-- components/LrcDisplay.vue -->
<script setup lang="ts">
// 从 components/lrc-display.tsx 迁移
import { ref, onMounted } from 'vue'
import type { LrcLine } from '~/types'

const props = defineProps<{
  lrcContent: string
}>()

// React useState -> Vue ref
const currentLine = ref<LrcLine | null>(null)

// React useEffect -> Vue onMounted
onMounted(() => {
  // 初始化逻辑
})
</script>

<template>
  <!-- 组件模板 -->
</template>
```

```vue
<!-- components/ui/Button.vue -->
<script setup lang="ts">
// 从 components/ui/button.tsx 迁移
const props = defineProps<{
  variant?: 'default' | 'primary' | 'secondary'
  size?: 'sm' | 'md' | 'lg'
}>()

const emit = defineEmits<{
  click: [event: MouseEvent]
}>()
</script>

<template>
  <button
    :class="[
      'button',
      `button--${variant || 'default'}`,
      `button--${size || 'md'}`
    ]"
    @click="emit('click', $event)"
  >
    <slot />
  </button>
</template>
```

### 2.3 状态管理

1. Context API 到 Pinia 的转换
```ts
// stores/subtitle.ts
import { defineStore } from 'pinia'
import type { Subtitle } from '~/types'

// 原 context/subtitle-context.tsx 转换为 Pinia store
export const useSubtitleStore = defineStore('subtitle', {
  state: () => ({
    subtitles: [] as Subtitle[],
    currentIndex: -1,
    isPlaying: false
  }),
  
  getters: {
    currentSubtitle: (state) => 
      state.currentIndex >= 0 ? state.subtitles[state.currentIndex] : null
  },
  
  actions: {
    setSubtitles(subtitles: Subtitle[]) {
      this.subtitles = subtitles
    },
    
    setCurrentIndex(index: number) {
      this.currentIndex = index
    },
    
    togglePlaying() {
      this.isPlaying = !this.isPlaying
    }
  }
})
```

2. Hooks 到 Composables 的转换
```ts
// composables/useToast.ts
// 从 hooks/use-toast.ts 迁移
export const useToast = () => {
  const toast = ref<string | null>(null)
  const toastType = ref<'info' | 'success' | 'error'>('info')
  
  const showToast = (message: string, type: 'info' | 'success' | 'error' = 'info') => {
    toast.value = message
    toastType.value = type
    
    setTimeout(() => {
      toast.value = null
    }, 3000)
  }
  
  return {
    toast,
    toastType,
    showToast
  }
}
```

### 2.4 服务层实现

1. LRC 服务
```ts
// services/lrc.ts
// 从 lib/lrcOperations.ts 迁移
export class LrcService {
  static parseLrc(content: string) {
    // LRC 解析逻辑
  }
  
  static formatTime(seconds: number) {
    // 时间格式化逻辑
  }
}
```

2. 字幕服务
```ts
// services/subtitle.ts
// 从 lib/subtitleOperations.ts 迁移
export class SubtitleService {
  static parse(content: string) {
    // 字幕解析逻辑
  }
  
  static format(subtitles: Subtitle[]) {
    // 字幕格式化逻辑
  }
}
```

## 3 特性功能迁移

### 3.1 音频处理

1. Wavesurfer.js Vue 组件
```vue
<!-- components/WaveformPlayer.vue -->
<script setup lang="ts">
import WaveSurfer from 'wavesurfer.js'
import { ref, onMounted, onBeforeUnmount } from 'vue'

const props = defineProps<{
  audioUrl: string
}>()

const waveformRef = ref<HTMLDivElement>()
const wavesurfer = ref<WaveSurfer>()

onMounted(() => {
  if (!waveformRef.value) return
  
  wavesurfer.value = WaveSurfer.create({
    container: waveformRef.value,
    waveColor: '#4a9eff',
    progressColor: '#1453ff',
    height: 100
  })
  
  wavesurfer.value.load(props.audioUrl)
})

onBeforeUnmount(() => {
  wavesurfer.value?.destroy()
})
</script>

<template>
  <div ref="waveformRef"></div>
</template>
```

### 3.2 可撤销状态实现

1. 通用可撤销状态 Composable
```ts
// composables/useUndoable.ts
// 从 hooks/use-undoable-state.ts 迁移
export function useUndoable<T>(initialState: T) {
  const currentState = ref<T>(initialState)
  const history = ref<T[]>([initialState])
  const pointer = ref(0)
  
  const setState = (newState: T) => {
    pointer.value++
    history.value = history.value.slice(0, pointer.value)
    history.value.push(newState)
    currentState.value = newState
  }
  
  const undo = () => {
    if (pointer.value > 0) {
      pointer.value--
      currentState.value = history.value[pointer.value]
    }
  }
  
  const redo = () => {
    if (pointer.value < history.value.length - 1) {
      pointer.value++
      currentState.value = history.value[pointer.value]
    }
  }
  
  return {
    state: currentState,
    setState,
    undo,
    redo,
    canUndo: computed(() => pointer.value > 0),
    canRedo: computed(() => pointer.value < history.value.length - 1)
  }
}
```

## 4 测试迁移

### 4.1 单元测试

1. 组件测试
```ts
// tests/components/LrcDisplay.test.ts
import { mount } from '@vue/test-utils'
import LrcDisplay from '~/components/LrcDisplay.vue'

describe('LrcDisplay', () => {
  test('renders lrc content', () => {
    const wrapper = mount(LrcDisplay, {
      props: {
        lrcContent: '[00:00.00]测试歌词'
      }
    })
    
    expect(wrapper.text()).toContain('测试歌词')
  })
})
```

2. Store 测试
```ts
// tests/stores/subtitle.test.ts
import { setActivePinia, createPinia } from 'pinia'
import { useSubtitleStore } from '~/stores/subtitle'

describe('Subtitle Store', () => {
  beforeEach(() => {
    setActivePinia(createPinia())
  })
  
  test('sets subtitles', () => {
    const store = useSubtitleStore()
    const subtitles = [
      { id: 1, text: '测试字幕', start: 0, end: 1000 }
    ]
    
    store.setSubtitles(subtitles)
    expect(store.subtitles).toEqual(subtitles)
  })
})
```

### 4.2 E2E 测试

```ts
// tests/e2e/editor.spec.ts
import { test, expect } from '@playwright/test'

test('editor workflow', async ({ page }) => {
  await page.goto('/editor')
  
  // 测试文件上传
  await page.setInputFiles('input[type="file"]', 'test.srt')
  
  // 测试字幕编辑
  await page.click('.subtitle-item')
  await page.fill('.subtitle-text', '新的字幕文本')
  
  // 测试保存
  await page.click('button:has-text("保存")')
  
  // 验证结果
  expect(await page.textContent('.subtitle-item')).toContain('新的字幕文本')
})
```

## 5 注意事项

1. React 到 Vue 的概念映射
   - `useState` -> `ref` / `reactive`
   - `useEffect` -> `onMounted` / `watch`
   - `useMemo` / `useCallback` -> `computed`
   - Context API -> Pinia
   - JSX -> Template

2. 生命周期对应
   - `componentDidMount` -> `onMounted`
   - `componentWillUnmount` -> `onBeforeUnmount`
   - `componentDidUpdate` -> `watch`

3. 事件处理
   - React: `onClick={handleClick}`
   - Vue: `@click="handleClick"`

4. Props 类型定义
   - React: `interface Props { ... }`
   - Vue: `defineProps<{ ... }>()`

## 6 参考资源

- [Nuxt 3 文档](https://nuxt.com/docs)
- [Vue 3 组合式 API](https://vuejs.org/guide/introduction.html)
- [Pinia 状态管理](https://pinia.vuejs.org/)
- [Vue Test Utils](https://test-utils.vuejs.org/)
- [Playwright](https://playwright.dev/)

## 7 更新记录

- [日期] 创建文档
- [日期] 添加详细实现示例
- [日期] 更新最佳实践
- [日期] 完善迁移指南 
# Vue 版本音频波形可视化组件迁移计划

本文档详细说明了将音频波形可视化组件从 React/Next.js 迁移到 Vue/Nuxt 的渐进式实施计划。该计划分为 7 个主要阶段，每个阶段都有明确的目标和可验证的成果。

## 阶段 1：基础设置与波形显示

### 目标
实现最基础的音频波形显示功能，确保 wavesurfer.js 能在 Vue 环境中正常工作。

### 功能点
- [ ] 创建基础的 Vue 组件结构 (`WaveformVisualizer.vue`)
- [ ] 集成 wavesurfer.js 核心库
- [ ] 实现音频文件加载和波形显示
- [ ] 添加加载状态指示器

### 代码结构示例
```vue
<template>
  <div class="waveform-container relative">
    <div ref="waveformRef" class="w-full h-full"></div>
    <div v-if="isLoading" class="absolute inset-0 flex items-center justify-center">
      <!-- 加载指示器 -->
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, onMounted, watch } from 'vue'
import WaveSurfer from 'wavesurfer.js'

const props = defineProps<{
  mediaFile: File | null
}>()

const waveformRef = ref<HTMLElement | null>(null)
const wavesurfer = ref<WaveSurfer | null>(null)
const isLoading = ref(false)

// 初始化 wavesurfer
onMounted(() => {
  if (!waveformRef.value) return
  
  wavesurfer.value = WaveSurfer.create({
    container: waveformRef.value,
    height: 'auto',
    waveColor: '#A7F3D0',
    progressColor: '#00d4ff',
    cursorColor: '#b91c1c',
    // ... 其他配置
  })
})

// 监听媒体文件变化
watch(() => props.mediaFile, async (newFile) => {
  if (!wavesurfer.value || !newFile) return
  
  isLoading.value = true
  try {
    const mediaUrl = URL.createObjectURL(newFile)
    await wavesurfer.value.load(mediaUrl)
  } finally {
    isLoading.value = false
  }
})
</script>
```

### 技术关注点
- Vue 3 组件基础结构 (`<script setup>`, `ref`, `onMounted`, `watch`)
- wavesurfer.js 的基本配置和初始化
- 文件加载和错误处理
- 基本的响应式状态管理

## 阶段 2：播放控制与时间同步

### 目标
实现波形图的播放控制和时间同步功能，确保与外部播放器状态保持一致。

### 功能点
- [ ] 实现播放/暂停控制
- [ ] 同步当前播放时间
- [ ] 添加时间轴显示
- [ ] 实现点击定位功能
- [ ] 添加播放进度指示

### 代码结构示例
```vue
<script setup lang="ts">
// ... 前面的代码 ...

const props = defineProps<{
  mediaFile: File | null,
  isPlaying: boolean,
  currentTime: number
}>()

const emit = defineEmits<{
  'seek': [time: number],
  'playPause': [playing: boolean]
}>()

// 使用 RAF 进行时间同步
const rafId = ref<number | null>(null)
const updateTime = () => {
  if (!wavesurfer.value) return
  
  if (Math.abs(wavesurfer.value.getCurrentTime() - props.currentTime) > 0.05) {
    wavesurfer.value.setTime(props.currentTime)
  }
  rafId.value = requestAnimationFrame(updateTime)
}

// 监听播放状态
watch(() => props.isPlaying, (playing) => {
  if (!wavesurfer.value) return
  
  if (playing) {
    wavesurfer.value.play()
    updateTime()
  } else {
    wavesurfer.value.pause()
    if (rafId.value) {
      cancelAnimationFrame(rafId.value)
    }
  }
})

// 清理
onUnmounted(() => {
  if (rafId.value) {
    cancelAnimationFrame(rafId.value)
  }
})
</script>
```

### 技术关注点
- 使用 `requestAnimationFrame` 实现平滑的时间同步
- Props 和事件处理
- Timeline 插件的集成
- 性能优化和内存管理

## 阶段 3：只读字幕区域显示

### 目标
在波形图上显示字幕区域，但暂不实现交互功能。

### 功能点
- [ ] 集成 RegionsPlugin
- [ ] 显示字幕区域
- [ ] 实现区域内容显示
- [ ] 建立字幕到区域的映射关系

### 代码结构示例
```vue
<script setup lang="ts">
import RegionsPlugin from 'wavesurfer.js/dist/plugins/regions.esm.js'

interface Subtitle {
  uuid: string
  startTime: string
  endTime: string
  text: string
}

const props = defineProps<{
  // ... 其他 props
  subtitles: Subtitle[]
}>()

const subtitleToRegionMap = ref(new Map())

const initRegions = () => {
  if (!wavesurfer.value) return
  
  // 清除现有区域
  subtitleToRegionMap.value.clear()
  
  // 创建新区域
  props.subtitles.forEach(subtitle => {
    const region = wavesurfer.value!.addRegion({
      id: subtitle.uuid,
      start: timeToSeconds(subtitle.startTime),
      end: timeToSeconds(subtitle.endTime),
      content: createRegionContent(subtitle),
      color: '#fcd34d40'
    })
    
    subtitleToRegionMap.value.set(subtitle.uuid, region)
  })
}

// 监听字幕变化
watch(() => props.subtitles, initRegions, { deep: true })
</script>
```

### 技术关注点
- RegionsPlugin 的配置和使用
- 响应式 Map 的使用
- 字幕时间格式转换
- 区域内容的动态生成

## 阶段 4：区域交互 - 时间调整

### 目标
实现字幕区域的交互功能，允许用户调整字幕时间。

### 功能点
- [ ] 实现区域拖拽
- [ ] 实现区域边缘调整
- [ ] 更新字幕时间
- [ ] 自定义区域样式

### 技术关注点
- 区域事件处理
- 时间格式转换
- DOM 操作
- 状态同步

## 阶段 5：高级区域逻辑与滚动

### 目标
实现区域防重叠和滚动定位功能。

### 功能点
- [ ] 实现区域防重叠逻辑
- [ ] 实现滚动到指定区域
- [ ] 优化拖拽体验

### 技术关注点
- 复杂逻辑处理
- 滚动控制
- 组件方法暴露

## 阶段 6：视图控制

### 目标
实现波形图的缩放和滚动控制。

### 功能点
- [ ] 实现自动缩放
- [ ] 实现手动水平滚动
- [ ] 优化视图体验

### 技术关注点
- 缩放算法
- 滚动事件处理
- 性能优化

## 阶段 7：收尾与集成

### 目标
完善功能，优化性能，确保与 Nuxt 良好集成。

### 功能点
- [ ] 添加悬停时间显示
- [ ] 实现键盘控制
- [ ] 优化错误处理
- [ ] 完善文档

### 技术关注点
- Nuxt 集成
- 性能优化
- 错误处理
- 文档完善

## 注意事项

### 依赖管理
- 核心依赖：
  ```json
  {
    "dependencies": {
      "wavesurfer.js": "^7.0.0",
      "vue": "^3.0.0",
      "nuxt": "^3.0.0"
    }
  }
  ```

### 性能考虑
- 使用 `requestAnimationFrame` 进行时间同步
- 适当使用 `watch` 的 `deep` 选项
- 及时清理事件监听器和 RAF

### 测试策略
- 单元测试：组件方法和工具函数
- 集成测试：组件与 wavesurfer.js 的交互
- E2E 测试：用户交互流程

### 文档要求
- 组件 API 文档
- 事件文档
- 使用示例
- 故障排除指南

## 进度追踪

使用以下命令格式标记已完成的任务：
```markdown
- [x] 已完成的任务
- [ ] 待完成的任务
```

## 验收标准

每个阶段的验收标准：
1. 功能完整性：所有列出的功能点都已实现
2. 代码质量：通过代码审查
3. 测试覆盖：单元测试和集成测试通过
4. 性能要求：无明显性能问题
5. 文档完善：API 文档和使用示例已更新 
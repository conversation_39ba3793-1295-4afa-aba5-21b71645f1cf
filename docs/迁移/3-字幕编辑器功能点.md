# 字幕编辑器功能点总结

## 1. 数据处理与管理
- **SRT 解析**：支持解析 .srt 文件，提取字幕的编号、时间和文本。✅
- **UUID 分配**：为每条字幕分配唯一 uuid，便于前端追踪和 React 渲染。✅
- **ID 重新排序**：添加、删除、合并等操作后自动重新编号，保证字幕顺序。✅
- **状态管理**：通过 React Context 集中管理字幕数据和所有操作✅。
- **撤销/重做**：支持所有字幕操作的撤销与重做。

## 2. 字幕编辑功能
- **文本编辑**：
  - 可直接修改字幕文本，支持回车确认、ESC 取消。✅
- **时间编辑**：
  - 可直接编辑字幕的开始和结束时间，自动聚焦。✅
  - 时间格式校验，逻辑校验（开始时间不能晚于结束时间）。✅
- **添加字幕**：可在任意两条字幕之间或末尾插入新字幕，自动分配时间。
- **删除字幕**：可删除任意一条字幕。✅
- **合并字幕**：可将相邻两条字幕合并为一条，文本拼接，时间合并。✅
- **按时间拆分**：支持按时间点拆分字幕（如由波形图等组件触发）。
- **查找替换**：支持批量替换所有字幕内容。

## 3. UI 与交互
- **列表显示**：字幕以列表形式展示，支持滚动。✅
- **当前行高亮**：根据播放进度高亮当前字幕。
- **自动滚动**：播放时自动滚动到当前字幕。
- **动画效果**：添加、删除字幕有过渡动画。
- **工具提示**：按钮操作有 Tooltip 说明。
- **空文本提示**：字幕内容为空时有占位提示。
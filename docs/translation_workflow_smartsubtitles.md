# smartsubtitles.vue 翻译流程

以下 Mermaid 图表描述了 `pages/smartsubtitles.vue` 文件中涉及的翻译相关流程。

```mermaid
graph TD
    A[用户上传音视频文件] --> B{文件上传成功?}
    B -->|是| C[执行语音识别 startRecognition]
    C --> D{语音识别成功?}
    D -->|是| E[生成 recognitionResult]
    E --> F{用户选择触发 智能分块?}
    F -->|是| G[执行智能分块 performSmartChunking]
    G --> H{智能分块成功?}
    H -->|是| I[生成 chunks]
    I --> J[用户配置翻译参数 targetLanguage, maxWorkers]
    J --> K{用户触发并发翻译?}
    K -->|chunks存在| L[执行并发翻译 performConcurrentTranslation]
    L --> M[更新状态: isTranslating = true]
    L --> N{翻译成功?}
    N -->|是| O[生成 translationResults]
    O --> P[ChunkDisplay 显示 分块与翻译结果]
    N -->|否| Q[记录 translationError]
    Q --> P
    H -->|否| R[记录 chunkingError]
    R --> S[显示错误信息]
    D -->|否| T[记录 speechErrorMessage]
    T --> S
    B -->|否| U[记录 fileErrorMessage]
    U --> S
    K -->|chunks不存在| S

    subgraph "主要阶段"
        A
        C
        G
        L
    end

    subgraph "用户交互/触发点"
        F
        J
        K
    end

    subgraph "数据与状态"
        E[recognitionResult]
        I[chunks]
        M[isTranslating]
        O[translationResults]
        Q[translationError]
        R[chunkingError]
        T[speechErrorMessage]
        U[fileErrorMessage]
    end

    subgraph "结果展示/错误处理"
        P
        S
    end
```

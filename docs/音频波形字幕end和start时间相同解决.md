wavesurfer.js 源码中，当音频的 end 和 start 时间戳相同时，做了一个负面优化，会导致自动增加一个 margin-top 的 style

```ts
	private avoidOverlapping(region: Region) {
  if (!region.content) return

  setTimeout(() => {
    // Check that the label doesn't overlap with other labels
    // If it does, push it down until it doesn't
    const div = region.content as HTMLElement
    const box = div.getBoundingClientRect()

    const overlap = this.regions
      .map((reg) => {
        if (reg === region || !reg.content) return 0

        const otherBox = reg.content.getBoundingClientRect()
        if (box.left < otherBox.left + otherBox.width && otherBox.left < box.left + box.width) {
          return otherBox.height
        }
        return 0
      })
      .reduce((sum, val) => sum + val, 0)

    div.style.marginTop = `${overlap}px`
  }, 10)
}
```

## 解决方案

覆盖这个方法

```ts
// 修补了 RegionPlug 以避免重叠区域的错误
const regionsPluginInstance = RegionsPlugin.create()

// @ts-expect-error 覆盖 avoidOverlapping 方法，此方法为 RegionPlugin 中的私有方法
regionsPluginInstance.avoidOverlapping = (_region: Region) => {
  // 什么都不做
}
```

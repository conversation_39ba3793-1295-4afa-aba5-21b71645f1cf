# 项目技术分析文档

## 1 技术栈

### 1.1 核心框架
- **Next.js**: v15.2.4
  - 使用 App Router 架构
  - 启用 Turbopack 构建工具
- **React**: v19.0.0
- **TypeScript**: v5.8.2

### 1.2 样式解决方案
- **Tailwind CSS**: v4.0.17
- **样式工具**:
  - class-variance-authority: 用于组件样式变体管理
  - tailwind-merge: 用于合并 Tailwind 类名
  - tailwindcss-animate: 动画支持

### 1.3 UI组件库
- **Radix UI 组件**:
  - @radix-ui/react-accordion
  - @radix-ui/react-alert-dialog
  - @radix-ui/react-checkbox
  - @radix-ui/react-dialog
  - @radix-ui/react-dropdown-menu
  - @radix-ui/react-label
  - @radix-ui/react-slider
  - @radix-ui/react-toast
  - @radix-ui/react-tooltip
- **图标库**:
  - @radix-ui/react-icons
  - @tabler/icons-react
  - lucide-react

### 1.4 多媒体处理
- wavesurfer.js: v7.9.4
- @wavesurfer/react: v1.0.9
- react-player: v2.16.0

### 1.5 开发工具
- @biomejs/biome: 代码格式化
- ESLint: 代码检查
- PostCSS: CSS 处理器

## 2 项目结构

```
├── app/                 # Next.js App Router 主目录
│   ├── globals.css     # 全局样式
│   ├── layout.tsx      # 全局布局
│   ├── page.tsx        # 主页面
│   └── faq/            # FAQ 页面
├── components/         # React 组件
├── hooks/             # 自定义 Hooks
├── lib/               # 工具函数和库
├── context/          # React Context 状态管理
├── types/            # TypeScript 类型定义
└── public/           # 静态资源
```

## 3 功能特点

### 3.1 核心功能
- **字幕编辑器**
  - SRT 文件导入/导出
  - 字幕文本编辑
  - 时间轴调整
  - 查找替换功能
  - 撤销/重做支持
- **音频处理**
  - 音频播放器集成
  - 波形可视化
  - 自定义播放控制
  - 播放速度调节
- **用户界面**
  - 响应式布局
  - 键盘快捷键支持
  - 实时预览
  - 交互式时间轴

### 3.2 技术特性
- **状态管理**
  - 使用 React Context 进行全局状态管理
  - 自定义 hooks 实现撤销/重做功能
  - 状态持久化和恢复
- **组件架构**
  - 模块化组件设计
  - 动态组件加载（使用 Next.js dynamic imports）
  - 组件间通信优化
- **性能优化**
  - 客户端渲染优化
  - 媒体文件处理优化
  - 状态更新性能优化

### 3.3 开发体验
- **开发工具链**
  - Turbopack 快速开发环境
  - ESLint + Biome 代码质量保证
  - TypeScript 类型安全
- **调试功能**
  - 开发时热重载
  - 错误边界处理
  - 开发者工具集成

## 4 核心模块说明

### 4.1 组件模块
- **video-player.tsx**: 音频播放器组件，支持基本播放控制
- **waveform-visualizer.tsx**: 音频波形可视化组件，支持时间轴操作
- **subtitle-list.tsx**: 字幕列表管理组件
- **subtitle-item.tsx**: 单个字幕项编辑组件
- **find-replace.tsx**: 查找替换功能组件
- **custom-controls.tsx**: 自定义播放控制组件

### 4.2 状态管理
- **subtitle-context.tsx**: 字幕相关状态管理
  - 字幕数据结构维护
  - 字幕操作方法封装
  - 状态同步控制

### 4.3 自定义 Hooks
- **use-undoable-state.ts**: 实现状态历史记录和撤销/重做功能
- **use-toast.ts**: 统一的消息提示管理

### 4.4 工具函数库
- **subtitleOperations.ts**: 字幕解析和处理相关函数
- **utils.ts**: 通用工具函数

## 5 业务流程

### 5.1 字幕编辑流程
1. 导入 SRT 文件
2. 解析字幕内容
3. 编辑字幕文本和时间
4. 实时预览
5. 导出更新后的 SRT 文件

### 5.2 音频同步流程
1. 加载媒体文件
2. 生成波形可视化
3. 同步字幕时间轴
4. 实时播放预览
5. 调整字幕时间点

## 6 最佳实践

### 6.1 代码组织
- 清晰的目录结构
- 模块化设计
- 类型定义分离

### 6.2 性能优化
- 使用 Next.js 的服务器组件
- 静态资源优化
- 构建优化

### 6.3 用户体验
- 专业的 UI 组件库
- 动画效果支持
- 响应式设计

## 7 开发指南

### 7.1 环境要求
- Node.js
- npm/yarn/pnpm

### 7.2 开发命令
```bash
# 开发环境启动
npm run dev

# 构建项目
npm run build

# 启动生产环境
npm run start

# 代码检查
npm run lint
``` 
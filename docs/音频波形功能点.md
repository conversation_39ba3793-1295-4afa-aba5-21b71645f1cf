# 音频波形可视化功能需求与技术实现

## 功能需求点 (用户可见的功能)

### 1. 基础波形显示
- 加载音频/视频文件后，能够将其音频数据可视化为波形图
- 在处理和加载媒体文件时，显示加载指示器

### 2. 播放控制与同步
- 波形图的播放/暂停状态与主媒体播放器（`VideoPlayer`）的状态保持一致
- 时间轴显示：在波形图上方显示时间刻度
- 精确时间指示：
  - 鼠标悬停时显示精确到毫秒的时间信息
  - 显示当前播放位置的垂直光标线
- 点击定位 (Seek)：点击波形图任意位置，主媒体播放器和波形图光标跳转到对应时间点

### 3. 字幕区域功能
- 字幕可视化：
  - 每条字幕在波形图上显示为可交互区域 (Region)
  - 区域内显示字幕的开始时间、结束时间和文本内容
- 区域交互：
  - 拖拽调整时间：通过拖动区域左右边缘（带箭头的虚线）调整字幕时间
  - 整体拖动：拖动整个区域移动字幕时间段
  - 实时同步：调整区域后，实时更新字幕列表中对应字幕的时间戳
- 防重叠机制：
  - 禁止区域完全覆盖或越过相邻区域
  - 允许区域边缘紧密相邻，但不允许时间重叠
  - 拖动时自动吸附到相邻区域边缘
- 区域样式：特定背景色，左右拖动 handle 有虚线和箭头样式

### 4. 视图控制
- 缩放控制：
  - 短音频（≤30秒）：自动缩放适应容器宽度
  - 长音频：使用默认缩放级别，允许水平滚动
- 滚动控制：
  - 使用鼠标滚轮进行水平滚动
  - 提供 `scrollToRegion` 方法供外部触发滚动

### 5. 交互控制
- 键盘控制：波形图组件获得焦点时，空格键控制播放/暂停
- 上下文联动：与 `SubtitleContext` 交互，读取字幕数据并在区域调整时更新全局字幕状态

## 技术实现点 (代码层面的关键技术)

### 1. 核心技术栈
- 使用 `wavesurfer.js` 库及其 React 封装 `@wavesurfer/react`
- 插件使用：
  - `RegionsPlugin`: 字幕区域管理
  - `Timeline`: 时间轴生成
  - `Hover`: 鼠标悬停时间显示

### 2. React 技术实现
- Hooks 使用：
  - `useEffect`: 处理副作用（初始化、加载媒体、同步状态等）
  - `useRef`: DOM 引用、实例存储、区域映射管理
  - `useState`: 内部状态管理
  - `forwardRef` 和 `useImperativeHandle`: 暴露方法
- 状态同步：
  - Props 接收父组件状态
  - 回调通知父组件
  - `SubtitleContext` 全局状态管理

### 3. 性能优化
- `requestAnimationFrame` 节流 `wavesurfer.setTime` 调用
- 空格键播放/暂停防抖
- 区域管理优化：
  - `Map` 存储字幕 UUID 到 Region 对象的映射
  - 字幕数据变化时重新初始化区域

### 4. 区域管理与防重叠
- Monkey Patch `RegionsPlugin` 的 `avoidOverlapping` 方法
- 自定义防重叠逻辑：
  - 获取时间排序的前后区域
  - 检查完全越过和部分重叠
  - 自动调整边缘位置

### 5. 样式与 DOM 操作
- 动态创建 Region content HTML
- 直接操作 Region DOM 元素添加自定义样式
- 使用 `next/dynamic` 动态导入，禁用 SSR

### 6. 事件处理
- 监听 wavesurfer 实例事件：
  - `ready`, `interaction`, `play`, `pause`
  - `RegionsPlugin` 的 `region-updated`
- DOM 事件处理：
  - `wheel`: 水平滚动
  - `keydown`: 空格播放/暂停
- 事件清理和组件卸载处理

### 7. 标识符管理
- 使用 UUID 作为字幕和区域的唯一标识符
- 避免依赖 SRT 文件中的顺序 ID 
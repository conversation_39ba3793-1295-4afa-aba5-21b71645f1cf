# 开发指南

## 1 开发环境配置

### 1.1. 分支管理
- main：主分支，用于生产环境
- develop：开发分支，用于开发环境
- feature/*：功能分支
- bugfix/*：bug修复分支
- release/*：发布分支

### 1.2. 提交规范
提交信息格式：
```
<type>(<scope>): <subject>

<body>

<footer>
```

类型（type）：
- feat: 新功能
- fix: 修复bug
- docs: 文档更新
- style: 代码格式（不影响代码运行的变动）
- refactor: 重构
- test: 测试相关
- chore: 构建过程或辅助工具的变动

### 1.3. Code Review 规范
- 提交前自查
- 遵循 PR 模板
- 及时回复评论
- 合并前确保CI通过

## 2 项目结构说明

### 2.1. 目录组织
- 按功能模块划分组件
- 共用组件放在 components/common
- 工具函数放在 lib 目录
- 类型定义放在 types 目录

### 2.2. 文档维护
- README 保持更新
- 及时更新开发文档
- 记录重要决策
- 添加使用示例

## 3 开发流程

### 3.1 代码开发
1. 从 develop 分支创建功能分支
2. 开发完成后提交 PR
3. 通过 Code Review
4. 合并到 develop 分支

### 3.2 测试
1. 从 develop 分支创建 bugfix 分支
2. 修复并测试
3. 提交 PR
4. 合并到 develop 分支

### 3.3 构建和部署
1. 从 develop 分支创建 release 分支
2. 进行最终测试
3. 合并到 main 分支
4. 打标签发布

## 4 开发规范

### 4.1 代码规范
- Node.js 版本要求
- 包管理器选择
- 编辑器配置
- 开发工具推荐

### 4.2 Git 规范
- Chrome DevTools
- React Developer Tools
- VS Code 调试配置
- 性能分析工具

_最后更新时间：2024-03-19_

## 5 常见问题

## 6 参考资源 
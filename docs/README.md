# 项目文档目录

本目录包含项目的所有技术文档，用于指导开发和维护工作。

## 1 文档结构

### 1.1 核心文档
- [系统架构.md](./系统架构.md) - 系统架构文档
- [开发指南.md](./开发指南.md) - 开发指南

### 1.2 技术规范 `/技术规范`
- [技术栈.md](./技术规范/技术栈.md) - 技术栈说明
- [编码规范.md](./技术规范/编码规范.md) - 编码规范
- [测试指南.md](./技术规范/测试指南.md) - 测试指南

### 1.3 迁移相关 `/迁移`
- [迁移计划.md](./迁移/迁移计划.md) - 迁移计划
- [技术细节.md](./迁移/技术细节.md) - 迁移技术细节
- [模块迁移.md](./迁移/模块迁移.md) - 模块迁移指南

### 1.4 参考文档 `/参考文档`
- [Nuxt参考.md](./参考文档/Nuxt参考.md) - Nuxt相关参考
- [待办事项.md](./参考文档/待办事项.md) - 待办事项

## 2 文档更新规则

1. 所有文档变更需要通过 PR 提交
2. 文档更新需要在 commit message 中注明原因
3. 重要的技术决策需要及时反映在文档中
4. 定期检查文档的时效性，确保内容准确

## 3 文档使用指南

1. 新成员入职请先阅读 系统架构.md
2. 开发前请仔细阅读 开发指南.md
3. 如发现文档问题，请及时提出 issue
4. 欢迎提交 PR 完善文档内容

## 4 注意事项

1. 文档采用 Markdown 格式编写
2. 保持文档结构清晰，便于阅读
3. 重要内容需要加粗或高亮标注
4. 代码示例需要使用代码块格式

_最后更新时间：2024-03-19_ 
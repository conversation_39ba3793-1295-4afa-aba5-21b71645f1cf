# 视频摘要和术语提取功能使用指南

本功能基于 DeepSeek V3 AI 模型，能够自动分析视频文本内容，提取主题摘要和专业术语，非常适合用于视频翻译、字幕制作和内容分析场景。

## 功能特点

- 🤖 **AI智能分析**: 基于DeepSeek V3模型的高质量内容理解
- 📝 **主题摘要**: 自动生成两句话的视频主题摘要
- 🔤 **术语提取**: 智能识别和翻译专业术语
- 🌐 **多语言支持**: 支持中英日韩等多种语言
- ⚙️ **排除机制**: 可设置已存在术语，避免重复提取
- 💾 **导出功能**: 支持JSON格式导出和剪贴板复制

## 快速开始

### 1. 访问功能页面

导航到 `/summary-demo` 页面即可开始使用。

### 2. 配置语言设置

```typescript
// 在代码中使用
import { setLanguageConfig } from '~/common/prompts'

// 设置源语言和目标语言
setLanguageConfig('English', 'Chinese')
```

### 3. 基本使用

```typescript
import { useSummaryExtraction } from '~/composables/useSummaryExtraction'

// 在Vue组件中使用
const { extractSummary, extractionResult, isExtracting } = useSummaryExtraction()

// 提取摘要
await extractSummary(videoTextContent)

// 获取结果
console.log(extractionResult.value)
```

## API 参考

### 核心函数

#### `getSummaryPrompt(sourceContent, customTermsJson?)`

生成用于AI分析的提示词。

**参数:**
- `sourceContent: string` - 视频文本内容
- `customTermsJson?: CustomTermsJson` - 可选的已存在术语

**返回:** `string` - 完整的提示词

#### `extractVideoSummary(sourceContent, customTermsJson?)`

调用AI API进行摘要和术语提取。

**参数:**
- `sourceContent: string` - 视频文本内容  
- `customTermsJson?: CustomTermsJson` - 可选的已存在术语

**返回:** `Promise<SummaryResult>` - 提取结果

### Composable 使用

#### `useSummaryExtraction()`

提供响应式的摘要提取功能。

**返回对象:**

```typescript
{
  // 状态
  isExtracting: Readonly<Ref<boolean>>,
  extractionResult: Readonly<Ref<SummaryResult | null>>,
  errorMessage: Readonly<Ref<string>>,
  
  // 方法
  extractSummary: (content: string, terms?: CustomTermsJson) => Promise<void>,
  getPromptPreview: (content: string, terms?: CustomTermsJson) => string,
  configureLanguages: (srcLang: string, tgtLang: string) => void,
  reset: () => void
}
```

### 数据类型

```typescript
interface CustomTerm {
  src: string    // 源术语
  tgt: string    // 目标翻译
  note: string   // 说明
}

interface CustomTermsJson {
  terms: CustomTerm[]
}

interface SummaryResult {
  theme: string        // 主题摘要
  terms: CustomTerm[]  // 提取的术语列表
}
```

## 使用示例

### 基础使用

```vue
<template>
  <div>
    <UTextarea v-model="content" placeholder="输入视频文本..." />
    <UButton @click="extract" :loading="isExtracting">
      提取摘要
    </UButton>
    
    <div v-if="result">
      <h3>主题摘要</h3>
      <p>{{ result.theme }}</p>
      
      <h3>专业术语</h3>
      <div v-for="term in result.terms" :key="term.src">
        {{ term.src }} → {{ term.tgt }} ({{ term.note }})
      </div>
    </div>
  </div>
</template>

<script setup>
import { useSummaryExtraction } from '~/composables/useSummaryExtraction'

const { extractSummary, extractionResult: result, isExtracting } = useSummaryExtraction()

const content = ref('')

const extract = () => {
  extractSummary(content.value)
}
</script>
```

### 高级使用（带已存在术语）

```vue
<script setup>
const existingTerms = {
  terms: [
    {
      src: "Machine Learning",
      tgt: "机器学习", 
      note: "已知术语，AI会排除不再提取"
    }
  ]
}

// 提取时传入已存在术语
await extractSummary(content.value, existingTerms)
</script>
```

### 直接调用API

```typescript
import { extractVideoSummary } from '~/common/prompts'

try {
  const result = await extractVideoSummary(`
    This video explains the fundamentals of quantum computing, 
    including qubits, superposition, and quantum entanglement.
    These concepts are crucial for understanding quantum algorithms
    and their potential applications in cryptography and optimization.
  `)
  
  console.log('主题:', result.theme)
  console.log('术语数量:', result.terms.length)
} catch (error) {
  console.error('提取失败:', error)
}
```

## 最佳实践

### 1. 文本预处理

- 确保文本内容完整且格式良好
- 移除不必要的时间戳和格式符号
- 合并过短的句子片段

### 2. 术语管理

- 维护一个项目级的术语库
- 定期更新已存在术语列表
- 为新提取的术语添加准确的说明

### 3. 性能优化

- 对于大量文本，考虑分段处理
- 缓存常用术语和摘要结果
- 使用适当的温度参数控制AI创造性

### 4. 错误处理

```typescript
const { extractSummary, errorMessage } = useSummaryExtraction()

try {
  await extractSummary(content)
} catch (error) {
  // 检查 errorMessage.value 获取详细错误信息
  console.error('提取失败:', errorMessage.value)
}
```

## 配置选项

### 环境变量

确保在 `.env` 文件中配置了必要的API密钥：

```bash
# DeepSeek API 配置
ARK_API_KEY=your_ark_api_key_here
DEEPSEEK_MODEL_ID=your_deepseek_model_id_here
```

### 自定义提示词

如需自定义AI提示词，可以修改 `common/prompts.ts` 中的 `getSummaryPrompt` 函数。

### 语言配置

支持的语言配置：
- English (英语)
- Chinese (中文)
- Japanese (日语)  
- Korean (韩语)

可根据需要扩展更多语言支持。

## 故障排除

### 常见问题

1. **API调用失败**
   - 检查网络连接
   - 验证API密钥配置
   - 确认DeepSeek服务可用性

2. **提取结果质量不佳**
   - 优化输入文本质量
   - 调整语言配置
   - 完善已存在术语列表

3. **JSON解析错误**
   - 检查AI返回内容格式
   - 重试请求
   - 考虑调整提示词

### 调试工具

页面提供了"显示提示词预览"功能，可用于：
- 查看发送给AI的完整提示词
- 调试术语排除逻辑
- 优化提示词效果

## 更新日志

### v1.0.0
- 初始版本发布
- 支持基础摘要和术语提取
- 集成DeepSeek V3 API
- 提供演示页面和文档

---

如有问题或建议，请查看项目文档或提交Issue。 
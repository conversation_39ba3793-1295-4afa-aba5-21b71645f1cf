## SRT 与 LRC 歌词高亮/定位不同步分析

### 现象描述
- 选择 SRT 歌词时，LRC 能高亮对应行。
- 选择 LRC 歌词时，SRT 可能无法高亮对应行（尤其是 SRT 时间戳被手动修改后）。

### 技术原因
1. **SRT 高亮逻辑**：依赖"当前时间点是否落在某个字幕的 startTime~endTime 区间内"。只有在区间内，才会高亮对应 SRT 行。
2. **LRC 高亮逻辑**：只要"当前时间点大于等于该行时间"，就高亮最后一个满足条件的行。
3. **不同步的根本原因**：SRT 设计为"区间字幕"，强调一段时间内持续显示一条字幕；LRC 设计为"点状歌词"，每个时间点只对应一句歌词。若 SRT 的 startTime 晚于 LRC 的时间点，LRC 点击后播放器跳转到该点，SRT 没有任何区间覆盖这个点，所以不会高亮。

### 合理性分析
- 这种现象在实现上是合理的，因为 SRT 和 LRC 的时间模型本质不同。
- SRT 需要区间覆盖，LRC 只需点匹配。
- 如果用户手动修改 SRT 时间戳，导致 SRT 区间与 LRC 时间点错位，这种不同步现象就会出现。

### 用户体验角度
- 用户可能期望"无论点击哪种歌词，另一侧都能高亮最接近的行"，但从数据结构和标准实现角度，当前逻辑符合各自格式设计初衷。

_最后更新时间：2024-03-19_ 
# 编码规范

## 1 TypeScript 规范

### 1.1 基本规范
- 始终使用类型注解
- 避免使用 `any` 类型
- 使用接口（Interface）定义对象类型
- 使用枚举（Enum）定义常量集合

### 1.2 React 组件规范
- 使用函数组件和 Hooks
- 组件文件使用 `.tsx` 扩展名
- 每个文件只包含一个组件
- 组件命名使用大驼峰（PascalCase）

### 1.3 样式规范
- 使用 Tailwind CSS 类名
- 遵循移动优先（Mobile First）原则
- 使用 class-variance-authority 管理样式变体
- 避免内联样式

### 1.4 文件命名规范
- 组件文件：PascalCase（如 `Button.tsx`）
- 工具函数文件：camelCase（如 `utils.ts`）
- 类型定义文件：PascalCase（如 `Types.ts`）
- 样式文件：camelCase（如 `globals.css`）

## 2 导入顺序规范
1. React 相关
2. 第三方库
3. 自定义 hooks
4. 组件
5. 工具函数
6. 类型定义
7. 样式文件

## 3 性能优化规范

### 3.1 代码层面
- 使用 React.memo 优化渲染
- 合理使用 useMemo 和 useCallback
- 避免不必要的重渲染
- 使用适当的依赖数组

### 3.2 资源层面
- 图片使用适当格式和大小
- 使用 Next.js 的图片优化功能
- 合理使用懒加载
- 优化首屏加载

## 4 代码注释规范
- 组件顶部添加功能说明
- 复杂逻辑需要详细注释
- 使用 JSDoc 格式
- 保持注释的及时更新

_最后更新时间：2024-03-19_ 
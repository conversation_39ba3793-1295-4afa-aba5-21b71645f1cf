{"name": "nuxt-app", "type": "module", "private": true, "scripts": {"build": "nuxt build", "dev": "nuxt dev", "generate": "nuxt generate", "postinstall": "nuxt prepare", "lint": "eslint .", "lint:fix": "eslint . --fix", "preview": "nuxt preview", "test": "vitest", "test:ui": "vitest --ui", "no-emit-tsc": "npx tsc --noEmit", "no-emit-vue-tsc": "npx vue-tsc --noEmit"}, "dependencies": {"@langchain/core": "^0.2.6", "@langchain/openai": "^0.1.2", "@logto/vue": "^2.2.13", "@paralleldrive/cuid2": "^2.2.2", "@trpc/client": "^11.1.2", "@trpc/server": "^11.1.2", "@vicons/tabler": "^0.13.0", "@volcengine/openapi": "^1.20.0", "@vueuse/core": "^13.0.0", "animejs": "^3.2.2", "compromise": "^14.13.0", "corenlp-ts": "^0.0.1", "cos-js-sdk-v5": "^1.8.6", "cos-nodejs-sdk-v5": "^2.14.1", "date-fns": "^4.1.0", "decimal.js-light": "^2.5.1", "drizzle-orm": "^0.35.1", "fuse.js": "^7.0.0", "isomorphic-dompurify": "^2.24.0", "jose": "^5.5.0", "lodash-es": "^4.17.21", "lru-cache": "^10.3.0", "mongoose": "^8.4.1", "mysql2": "^3.11.3", "naive-ui": "^2.38.2", "ofetch": "^1.4.1", "path-to-regexp": "^8.2.0", "pinia-plugin-persistedstate": "^4.1.3", "qs": "^6.14.0", "sharp": "^0.33.4", "splitpanes": "^4.0.3", "superjson": "^2.2.1", "trpc-nuxt": "^1.0.5", "uuid": "^11.0.2", "video.js": "^8.22.0", "vue": "^3.4.21", "vue-dompurify-html": "^5.3.0", "vue-draggable-plus": "^0.5.0", "vue-loading-overlay": "^6.0", "vue-sonner": "^1.2.5", "wavesurfer.js": "^7.9.5", "youtube-transcript": "^1.2.1", "zod": "^3.23.8"}, "devDependencies": {"@antfu/eslint-config": "^4.14.1", "@formkit/auto-animate": "^0.8.2", "@iconify-json/heroicons": "^1.2.1", "@iconify-json/ph": "^1.2.1", "@nuxt/content": "^2.12.1", "@nuxt/devtools": "^1.3.3", "@nuxt/eslint": "^0.3.13", "@nuxt/icon": "^1.8.2", "@nuxt/image": "^1.7.0", "@nuxt/ui": "^2.22.0", "@nuxtjs/eslint-config-typescript": "^12.1.0", "@pinia-plugin-persistedstate/nuxt": "^1.2.1", "@pinia/nuxt": "^0.5.1", "@types/animejs": "^3.1.12", "@types/archiver": "^6.0.2", "@types/fs-extra": "^11.0.4", "@types/inquirer": "^9.0.7", "@types/node": "^20.14.2", "@types/qs": "^6.14.0", "@types/splitpanes": "^2.2.6", "@types/video.js": "^7.3.58", "@vitest/ui": "^3.1.2", "@volar/typescript": "^2.2.2", "@vueuse/nuxt": "^10.10.0", "archiver": "^7.0.1", "autoprefixer": "^10.4.19", "code-inspector-plugin": "^0.20.12", "daisyui": "^4.12.2", "drizzle-kit": "0.26.2", "env-cmd": "^10.1.0", "eslint": "^8.57.1", "eslint-plugin-format": "^1.0.1", "eslint-plugin-tailwindcss": "^3.18.0", "eslint-plugin-vue": "^9.21.0", "fs-extra": "^11.2.0", "inquirer": "^10.1.8", "nuxt": "^3.11.2", "postcss": "^8.4.38", "tailwindcss": "^3.4.14", "tsx": "^4.17.0", "typescript": "^5.8.3", "unplugin-auto-import": "^19.1.2", "unplugin-vue-components": "^28.5.0", "vitest": "^1.6.1", "vue-tsc": "^2.2.2"}}
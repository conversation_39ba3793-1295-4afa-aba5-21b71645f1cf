import antfu from '@antfu/eslint-config'
import tailwind from 'eslint-plugin-tailwindcss'

export default antfu(
  {
    // 启用 Vue 支持（项目使用 Nuxt）
    vue: true,

    // 启用 TypeScript 类型感知规则
    typescript: {
      tsconfigPath: 'tsconfig.json',
    },

    // 启用格式化器
    formatters: {
      css: true,
      html: true,
      markdown: 'prettier',
    },

    // 编辑器特定设置
    isInEditor: false,
  },
  {
    ignores: [
      // Drizzle 生成的文件
      'packages/db/drizzle/**',
    ],
  },
  {
    // Vue 单文件组件块顺序规则
    files: ['**/*.vue'],
    rules: {
      'vue/block-order': [
        'error',
        {
          order: ['script', 'template', 'style'],
        },
      ],
    },
  },
  {
    // 全局规则配置
    rules: {
      // 将 console 语句从错误降级为警告
      'no-console': 'warn',
    },
  },
  ...tailwind.configs['flat/recommended'],
)

/**
 * vtt 字幕结束时间微调
 */
export const END_TIME_ADJUSTMENT = 0.01 // 单位：秒

/**
 * 字幕默认起始时间
 */
export const DEFAULT_SUBTITLE_START_TIME = '00:00:00,000' // 字幕默认起始时间

/**
 * 字幕默认结束时间
 */
export const DEFAULT_SUBTITLE_DURATION = 2 // 字幕默认持续时间（秒）

/**
 * 字幕默认结束时间
 */
export const DEFAULT_SUBTITLE_END_TIME = '00:00:02,000' // 字幕默认结束时间

/**
 * 合并字幕时的分隔符
 */
export const SUBTITLE_MERGE_SEPARATOR = ' ' // 合并字幕时的分隔符

/**
 * 支持的音视频文件扩展名
 */
export const SUPPORTED_AUDIO_FORMATS = ['wav', 'mp3', 'mp4', 'm4a', 'ogg']

/**
 * 支持的音视频MIME类型
 */
export const SUPPORTED_MIME_TYPES = [
  'audio/wav',
  'audio/wave',
  'audio/x-wav',
  'audio/mpeg',
  'audio/mp3',
  'audio/mp4',
  'audio/m4a',
  'audio/ogg',
]

/**
 * 文件扩展名到MIME类型的映射
 */
export const EXTENSION_TO_MIME_MAP: Record<string, string> = {
  wav: 'audio/wav',
  mp3: 'audio/mpeg',
  mp4: 'audio/mp4',
  m4a: 'audio/mp4',
  ogg: 'audio/ogg',
}

/**
 * MIME类型标准化映射
 */
export const MIME_TYPE_NORMALIZATION_MAP: Record<string, string> = {
  'audio/mp3': 'audio/mpeg',
  'audio/wave': 'audio/wav',
  'audio/x-wav': 'audio/wav',
  'audio/m4a': 'audio/mp4',
}

/**
 * 文件大小限制（字节）
 */
export const MAX_FILE_SIZE = 150 * 1024 * 1024 // 150MB

/**
 * 摘要长度
 */
export const SUMMARY_LENGTH = 8000

/**
 * 默认温度
 */
export const DEFAULT_TEMPERATURE = 0.3

/**
 * 默认采样参数
 */
export const DEFAULT_TOP_P = 0.9

/**
 * 源语言
 */
export const SRC_LANG = 'en'

/**
 * 目标语言
 */
export const TGT_LANG = '简体中文'

/**
 * DeepSeek 模型ID
 */
export const DEEP_SEEK_MODEL_ID = 'deepseek-v3-250324'

/**
 * 智能分块相关常量
 */
export const DEFAULT_CHUNK_SIZE = 600
export const DEFAULT_MAX_SENTENCES = 10
export const CONTEXT_PREVIOUS_LINES = 3
export const CONTEXT_AFTER_LINES = 2

/**
 * 相似度匹配相关常量
 */
export const SIMILARITY_THRESHOLD = 0.9
export const SIMILARITY_WARNING_THRESHOLD = 0.95

/**
 * 重试机制相关常量
 */
export const MAX_RETRY_ATTEMPTS = 3
export const RETRY_DELAY_BASE = 1000

/**
 * 翻译质量控制常量
 */
export const TRANSLATION_LENGTH_RATIO_MIN = 0.3
export const TRANSLATION_LENGTH_RATIO_MAX = 3.0
export const QUALITY_SCORE_THRESHOLD = 70

export const AUDIO_MIME_PREFIX = 'audio/' // 音频文件类型前缀
export const VIDEO_MIME_PREFIX = 'video/' // 视频文件类型前缀

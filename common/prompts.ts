import { SRC_LANG, TGT_LANG } from './constants'

// 数据校验结果类型定义
export interface ValidationResult {
  status: 'success' | 'error'
  message: string
}

// 自定义术语类型定义
export interface CustomTerm {
  src: string
  tgt: string
  note: string
}

export interface CustomTermsJson {
  terms: CustomTerm[]
}

// 摘要结果类型定义
export interface SummaryResult {
  theme: string
  terms: CustomTerm[]
}

// 语言配置函数
function getLanguageConfig() {
  const srcLang = SRC_LANG

  const tgtLang = TGT_LANG

  return { srcLang, tgtLang }
}

/**
 * 生成内容摘要和术语提取的提示词
 * @param sourceContent 源文本内容
 * @param customTermsJson 可选的已存在术语JSON对象
 * @returns 完整的提示词字符串
 */
export function getSummaryPrompt(sourceContent: string, customTermsJson?: CustomTermsJson): string {
  const { srcLang, tgtLang } = getLanguageConfig()

  // 构建已存在术语的说明
  let termsNote = ''
  if (customTermsJson?.terms?.length) {
    const termsList = customTermsJson.terms.map((term) => `- ${term.src}: ${term.tgt} (${term.note})`)
    termsNote = `\n### Existing Terms\nPlease exclude these terms in your extraction:\n${termsList.join('\n')}`
  }

  const summaryPrompt = `
## Role
You are a translation expert and terminology consultant, specializing in ${srcLang} comprehension and ${tgtLang} expression optimization.

## Task
For the provided ${srcLang} text:
1. Summarize main topic in two sentences
2. Extract professional terms, technical terminology, and person names with ${tgtLang} translations (excluding existing terms)
3. Provide brief explanation for each term

${termsNote}

Steps:
1. Topic Summary:
   - Quick scan for general understanding
   - Write two sentences: first for main topic, second for key point
2. Term Extraction:
   - Mark professional terms, technical terminology, and person names (excluding those listed in Existing Terms)
   - For person names: keep original or provide phonetic translation if needed
   - For technical terms: provide accurate ${tgtLang} translation
   - Add brief explanation (for person names, include role/context)
   - Extract less than 15 terms

## INPUT
<text>
${sourceContent}
</text>

## Output in only JSON format and no other text
{
  "theme": "Two-sentence text summary",
  "terms": [
    {
      "src": "${srcLang} term",
      "tgt": "${tgtLang} translation or original", 
      "note": "Brief explanation"
    },
    ...
  ]
}  

## Example
{
  "theme": "本内容介绍人工智能在医疗领域的应用现状。重点展示了AI在医学影像诊断和药物研发中的突破性进展。",
  "terms": [
    {
      "src": "Machine Learning",
      "tgt": "机器学习",
      "note": "AI的核心技术，通过数据训练实现智能决策"
    },
    {
      "src": "CNN",
      "tgt": "CNN",
      "note": "卷积神经网络，用于医学图像识别的深度学习模型"
    },
    {
      "src": "Geoffrey Hinton",
      "tgt": "杰弗里·辛顿",
      "note": "深度学习之父，图灵奖得主，神经网络领域先驱"
    },
    {
      "src": "Andrew Ng",
      "tgt": "吴恩达",
      "note": "机器学习专家，Coursera联合创始人，前百度首席科学家"
    }
  ]
}

Note: Start you answer with \`\`\`json and end with \`\`\`, do not add any other text.
`.trim()

  return summaryPrompt
}

/**
 * 设置语言配置
 * @param srcLang 源语言
 * @param tgtLang 目标语言
 */
export function setLanguageConfig(srcLang: string, tgtLang: string): void {
  if (import.meta.client) {
    localStorage.setItem('whisper.detected_language', srcLang)
    localStorage.setItem('target_language', tgtLang)
  }
}

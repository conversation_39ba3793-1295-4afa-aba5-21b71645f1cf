# 🎵 Nuxt tRPC 音乐服务

一个基于 Nuxt 3 + tRPC 的完全类型安全的音乐聚合服务，支持网易云音乐、酷狗音乐、酷我音乐等多平台音乐搜索和**歌词服务**功能。

## ✨ 特性

- 🔒 **完全类型安全** - 端到端 TypeScript 类型推导
- 🏗️ **模块化架构** - 平台分离的路由器设计
- ⚡ **高性能** - tRPC 批量请求和 SuperJSON 序列化
- 🎛️ **聚合搜索** - 同时搜索多个音乐平台
- 🎤 **歌词服务** - 基于lrclib.net的同步歌词搜索
- 🛡️ **输入验证** - 使用 Zod 进行 Schema 验证
- 📊 **健康监控** - 实时平台状态检查
- 🔧 **开发工具** - 内置 API 测试器和性能监控

## 🚀 快速开始

### 环境要求

- Node.js 18+
- pnpm (推荐) 或 npm/yarn

### 安装依赖

```bash
pnpm install
```

### 启动开发服务器

```bash
pnpm dev
```

访问 http://localhost:6001 查看应用。

## 📁 项目结构

```
├── server/
│   ├── api/
│   │   ├── trpc.ts              # tRPC API 处理器
│   │   └── lrc.ts               # 原有歌词API (兼容保留)
│   └── trpc/
│       ├── context.ts           # tRPC 上下文
│       ├── router.ts            # 主路由器
│       ├── utils/               # tRPC工具函数
│       │   └── errorHandler.ts  # 错误处理工具
│       ├── schemas/             # Zod 验证 Schema
│       │   ├── common.ts        # 通用类型和歌词Schema
│       │   ├── search.ts        # 搜索相关
│       │   ├── song.ts          # 歌曲相关
│       │   └── lyric.ts         # 歌词相关
│       └── routers/             # 分平台路由器
│           ├── netease.ts       # 网易云音乐 (实际API)
│           ├── kugou.ts         # 酷狗音乐 (模拟实现)
│           ├── kuwo.ts          # 酷我音乐 (模拟实现)  
│           ├── general.ts       # 聚合搜索和健康检查
│           └── lyrics.ts        # 歌词服务 (lrclib.net)
├── composables/
│   └── useTRPC.ts              # 类型安全的客户端hooks
├── pages/
│   ├── music-demo.vue          # 基础音乐服务演示
│   ├── music-service.vue       # 完整服务演示页面
│   └── dev-tools.vue           # 开发工具和API测试器
├── utils/
│   └── lrcNormalizer.ts        # 歌词数据标准化工具
└── plugins/
    └── trpc.client.ts          # tRPC客户端插件
```

## 🎵 音乐平台支持

### 🔴 网易云音乐 (完整实现)
- ✅ 歌曲搜索 - 基于公开API，返回真实数据
- ✅ 歌曲详情 - 获取完整歌曲信息
- ✅ 歌词获取 - 支持多语言歌词

### 🔵 酷狗音乐 (模拟实现)
- 🔄 歌曲搜索 - 模拟数据展示
- 🔄 歌曲详情 - 模拟详情信息
- 🔄 歌词获取 - 模拟歌词内容

### 🟢 酷我音乐 (模拟实现)
- 🔄 歌曲搜索 - 模拟数据展示
- 🔄 歌曲详情 - 模拟详情信息
- 🔄 歌词获取 - 模拟歌词内容

## 🎤 歌词服务特性

### 基于 lrclib.net API
- **免费开源** - 无需API密钥，免费使用
- **同步歌词** - 支持时间轴同步歌词
- **多语言** - 支持多种语言的歌词
- **高质量** - 社区维护的高质量歌词数据

### 功能支持
1. **关键词搜索** - `lyrics.search(query, limit?)`
   - 根据歌曲名或歌手名搜索
   - 自动过滤只返回有同步歌词的结果
   - 支持结果数量限制
   
2. **精确搜索** - `lyrics.searchByTrack(trackName, artistName, ...)`
   - 根据确切的歌曲名和艺术家名搜索
   - 支持专辑名和时长作为附加条件
   - 返回最匹配的单个结果

### 使用示例

```typescript
// 关键词搜索歌词
const { data } = await $trpc.lyrics.search.query({
  q: '周杰伦 稻香',
  limit: 10
})

// 精确搜索歌词
const { data } = await $trpc.lyrics.searchByTrack.query({
  trackName: '稻香',
  artistName: '周杰伦',
  albumName: '魔杰座', // 可选
  duration: 223 // 可选，秒数
})
```

## 🔧 API 接口

### tRPC 路由结构

```typescript
// 平台音乐API
trpc.netease.search.query({ q: '歌曲名', page: 1, limit: 30 })
trpc.netease.detail.query({ id: '歌曲ID' })
trpc.netease.lyric.query({ id: '歌曲ID' })

trpc.kugou.search.query({ q: '歌曲名', page: 1, limit: 30 })
trpc.kugou.detail.query({ id: '歌曲ID' })
trpc.kugou.lyric.query({ id: '歌曲ID' })

trpc.kuwo.search.query({ q: '歌曲名', page: 1, limit: 30 })
trpc.kuwo.detail.query({ id: '歌曲ID' })
trpc.kuwo.lyric.query({ id: '歌曲ID' })

// 聚合服务
trpc.general.aggregateSearch.query({
  q: '歌曲名',
  platforms: ['wy', 'kg', 'kw'],
  page: 1,
  limit: 10
})
trpc.general.healthCheck.query({ includeDetails: true })

// 歌词服务
trpc.lyrics.search.query({ q: '搜索关键词', limit: 10 })
trpc.lyrics.searchByTrack.query({
  trackName: '歌曲名',
  artistName: '艺术家名',
  albumName: '专辑名', // 可选
  duration: 223 // 可选
})
```

## 📄 页面功能

### 🎵 `/music-demo` - 基础演示
- 网易云音乐搜索测试
- 歌词服务搜索测试
- 基础功能演示
- API调用统计

### 🎛️ `/music-service` - 完整服务
- 多平台聚合搜索
- 健康状态监控
- 歌词服务完整功能
- 平台选择和配置
- 详细的统计信息

### 🔧 `/dev-tools` - 开发工具
- API接口测试器
- 歌词API专项测试
- 响应时间监控
- 错误调试工具
- 性能分析

## 🏗️ 架构设计

### 类型安全架构
- **端到端类型推导** - 从服务器到客户端的完整类型安全
- **Zod Schema验证** - 输入输出数据验证
- **tRPC错误处理** - 统一的错误处理机制

### 模块化设计
- **平台分离** - 每个音乐平台独立路由器
- **功能分离** - 搜索、详情、歌词功能独立
- **工具复用** - 通用工具函数和类型定义

### 性能优化
- **批量请求** - tRPC支持请求批处理
- **数据缓存** - Vue/Nuxt内置的响应式缓存
- **错误边界** - 单个平台失败不影响其他平台

## 🛠️ 开发指南

### 添加新的音乐平台

1. 创建新的路由器文件 `server/trpc/routers/newplatform.ts`
2. 实现搜索、详情、歌词三个接口
3. 在主路由器中注册新平台
4. 更新客户端composables
5. 添加平台到聚合搜索中

### 扩展歌词服务

歌词服务基于lrclib.net，如需扩展：

1. 修改 `server/trpc/routers/lyrics.ts`
2. 更新 Schema 定义 `server/trpc/schemas/common.ts`
3. 扩展客户端接口 `composables/useTRPC.ts`
4. 测试新功能 `pages/dev-tools.vue`

## 📊 API 限制说明

### 网易云音乐 API
- 基于公开API，可能有访问频率限制
- 建议添加请求缓存和重试机制

### lrclib.net 歌词API
- 免费使用，无需API key
- 社区维护，数据质量较高
- 建议合理使用，避免过频请求

### 模拟平台 (酷狗/酷我)
- 当前为演示数据
- 生产环境需要对接真实API

## 🚀 部署

### 本地开发
```bash
pnpm dev
```

### 构建生产版本
```bash
pnpm build
```

### 预览生产版本
```bash
pnpm preview
```

## 📝 更新日志

### v2.0.0 - 歌词服务集成
- ✅ 集成 lrclib.net 歌词服务
- ✅ 实现网易云音乐真实API
- ✅ 完善错误处理和类型安全
- ✅ 添加歌词API专项测试工具
- ✅ 更新所有演示页面

### v1.0.0 - 基础架构
- ✅ tRPC基础架构搭建
- ✅ 多平台路由器设计
- ✅ 聚合搜索功能
- ✅ 健康检查机制
- ✅ 开发工具页面

## 🤝 贡献

欢迎提交 Issue 和 Pull Request！

## 📄 许可证

MIT License

## 📞 联系方式

如有问题或建议，请通过以下方式联系：

- 提交 Issue
- 发起 Discussion
- 邮件联系

---

**享受构建类型安全的音乐服务！** 🎵✨ 
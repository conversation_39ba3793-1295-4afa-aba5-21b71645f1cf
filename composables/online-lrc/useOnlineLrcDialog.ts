import { useInfiniteScroll } from '~/composables/core/useInfiniteScroll'
import { useOnlineLrcSearch } from './useOnlineLrcSearch'
import { useOnlineLrcLyrics } from './useOnlineLrcLyrics'
import type { NeteaseSongItemSchemaType } from '~/server/trpc/schemas/api/song'
import type { LrcData } from '~/utils/formats/parsers/lrcParser'

interface UseOnlineLrcDialogReturn {
  // 重新导出搜索相关
  searchQuery: ReturnType<typeof useOnlineLrcSearch>['searchQuery']
  searchText: ReturnType<typeof useOnlineLrcSearch>['searchText']
  allSongs: ReturnType<typeof useOnlineLrcSearch>['allSongs']
  searchStatus: ReturnType<typeof useOnlineLrcSearch>['searchStatus']
  searchError: ReturnType<typeof useOnlineLrcSearch>['searchError']

  // 重新导出歌词相关
  selectedSongId: ReturnType<typeof useOnlineLrcLyrics>['selectedSongId']
  lyricsData: ReturnType<typeof useOnlineLrcLyrics>['lyricsData']
  lyricsStatus: ReturnType<typeof useOnlineLrcLyrics>['lyricsStatus']
  lyricsError: ReturnType<typeof useOnlineLrcLyrics>['lyricsError']

  // 重新导出分页相关
  currentPage: ReturnType<typeof useOnlineLrcSearch>['currentPage']
  hasMore: ReturnType<typeof useOnlineLrcSearch>['hasMore']
  isLoadingMore: ReturnType<typeof useOnlineLrcSearch>['isLoadingMore']

  // 重新导出无限滚动相关
  sentinelRef: ReturnType<typeof useInfiniteScroll>['sentinelRef']

  // 统一方法
  onLoadMoreSongs: () => Promise<void>
  onGetOnlineLrc: () => Promise<void>
  onClearSearch: () => void

  onView: (item: NeteaseSongItemSchemaType) => void
  onCreateLrcData: (songInfo: NeteaseSongItemSchemaType) => LrcData | null

  onCleanupObserver: () => void

  onSetupObserver: () => void
}

/**
 * 在线歌词弹窗 composable
 * 整合搜索、歌词和无限滚动功能，提供统一接口
 */
export const useOnlineLrcDialog = (): UseOnlineLrcDialogReturn => {
  // 使用搜索功能
  const {
    searchQuery,
    searchText,
    allSongs,
    currentPage,
    hasMore,
    isLoadingMore,
    searchStatus,
    searchError,
    onLoadMoreSongs,
    onGetOnlineLrc,
    onClearSearch,
  } = useOnlineLrcSearch()

  // 使用歌词功能
  const { selectedSongId, lyricsData, lyricsStatus, lyricsError, onView, onCreateLrcData } = useOnlineLrcLyrics()

  // 使用无限滚动功能
  const {
    sentinelRef,
    onSetupObserver: onSetupInfiniteScroll,
    onCleanupObserver,
  } = useInfiniteScroll({
    onIntersect: () => {
      if (hasMore.value && !isLoadingMore.value) {
        onLoadMoreSongs()
      }
    },
  })

  // 设置观察器，首次搜索后设置
  const onSetupObserver = () => {
    nextTick(() => {
      onSetupInfiniteScroll()
    })
  }

  const onClearSearchSearch = () => {
    lyricsData.value = null
    onClearSearch()
  }

  return {
    // 重新导出搜索相关
    searchQuery,
    searchText,
    allSongs,
    searchStatus,
    searchError,

    // 重新导出歌词相关
    selectedSongId,
    lyricsData,
    lyricsStatus,
    lyricsError,

    // 重新导出分页相关
    currentPage,
    hasMore,
    isLoadingMore,

    // 重新导出无限滚动相关
    sentinelRef,

    // 统一方法
    onView,
    onCreateLrcData,

    onSetupObserver,

    onCleanupObserver,

    onLoadMoreSongs,
    onGetOnlineLrc,
    onClearSearch: onClearSearchSearch,
  }
}

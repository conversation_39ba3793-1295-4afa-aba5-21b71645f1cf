import type { NeteaseSongItemSchemaType } from '~/server/trpc/schemas/api/song'

interface UseOnlineLrcSearchReturn {
  // 状态
  searchQuery: Ref<string>
  searchText: Ref<string>
  allSongs: Ref<NeteaseSongItemSchemaType[]>
  currentPage: Ref<number>
  hasMore: Ref<boolean>
  isLoadingMore: Ref<boolean>
  searchStatus: Ref<'idle' | 'pending' | 'success' | 'error'>
  searchError: Ref<string | null>

  // 方法
  onPerformSearch: (page?: number) => Promise<void>
  onLoadMoreSongs: () => Promise<void>
  onGetOnlineLrc: () => Promise<void>
  onClearSearch: () => void
}

/**
 * 在线歌词搜索 composable
 * 管理歌曲搜索状态、分页和错误处理
 */
export const useOnlineLrcSearch = (): UseOnlineLrcSearchReturn => {
  const searchQuery = ref('')
  const searchText = ref('')
  const allSongs = ref<NeteaseSongItemSchemaType[]>([])
  const currentPage = ref(1)
  const hasMore = ref(true)
  const isLoadingMore = ref(false)
  const searchStatus = ref<'idle' | 'pending' | 'success' | 'error'>('idle')
  const searchError = ref<string | null>(null)

  // tRPC客户端
  const { $trpc } = useNuxtApp()

  // 执行搜索
  const onPerformSearch = async (page: number = 1) => {
    if (!searchQuery.value || import.meta.server) return

    try {
      if (page === 1) {
        searchStatus.value = 'pending'
      } else {
        isLoadingMore.value = true
      }

      const result = await $trpc.netease.search.query({
        q: searchQuery.value,
        page,
        limit: 10,
      })

      const data = result
      if (data) {
        if (page === 1) {
          allSongs.value = data.list || []
          currentPage.value = 1
        } else {
          allSongs.value = [...allSongs.value, ...(data.list || [])]
          currentPage.value = page
        }

        hasMore.value = (data.allPage || 1) > page
        searchStatus.value = 'success'
        searchError.value = null
      }
    } catch (error) {
      searchStatus.value = 'error'
      searchError.value = error instanceof Error ? error.message : '搜索失败'
    } finally {
      isLoadingMore.value = false
    }
  }

  // 加载更多歌曲
  const onLoadMoreSongs = async () => {
    if (isLoadingMore.value || !hasMore.value || searchStatus.value === 'pending') return

    await onPerformSearch(currentPage.value + 1)
  }

  // 触发响应式搜索
  const onGetOnlineLrc = async () => {
    searchQuery.value = searchText.value
    if (searchQuery.value) {
      await onPerformSearch(1)
    }
  }

  // 清空搜索
  const onClearSearch = () => {
    searchText.value = ''
    searchQuery.value = ''
    allSongs.value = []
    currentPage.value = 1
    hasMore.value = true
    isLoadingMore.value = false
    searchStatus.value = 'idle'
    searchError.value = null
  }

  return {
    // 状态
    searchQuery,
    searchText,
    allSongs,
    currentPage,
    hasMore,
    isLoadingMore,
    searchStatus,
    searchError,

    // 方法
    onPerformSearch,
    onLoadMoreSongs,
    onGetOnlineLrc,
    onClearSearch,
  }
}

import { parseTimeStringToSeconds } from '~/utils/processing/time/timeParser'
import type { LrcData } from '~/utils/formats/parsers/lrcParser'
import type { NeteaseLyricInfoSchemaType, NeteaseSongItemSchemaType } from '~/server/trpc/schemas/api/song'

interface UseOnlineLrcLyricsReturn {
  // 状态
  selectedSongId: Ref<string>
  lyricsData: Ref<NeteaseLyricInfoSchemaType | null | undefined>
  lyricsStatus: Ref<string>
  lyricsError: Ref<unknown>

  // 方法
  onView: (item: NeteaseSongItemSchemaType) => void
  onCreateLrcData: (songInfo: NeteaseSongItemSchemaType) => LrcData | null
}

/**
 * 在线歌词获取 composable
 * 管理歌词获取状态和LrcData转换
 */
export const useOnlineLrcLyrics = (): UseOnlineLrcLyricsReturn => {
  const selectedSongId = ref<string>('')
  const { $trpc } = useNuxtApp()

  // 歌词获取
  const {
    data: lyricsData,
    status: lyricsStatus,
    error: lyricsError,
  } = useAsyncData(
    'netease-lyrics',
    async () => {
      if (!selectedSongId.value || import.meta.server) return null

      const result = await $trpc.netease.lyric.query({
        id: selectedSongId.value,
      })
      return result
    },
    {
      watch: [selectedSongId],
      immediate: false,
      server: false,
    },
  )

  // 展开歌词
  const onView = (item: NeteaseSongItemSchemaType) => {
    // 触发响应式歌词获取
    selectedSongId.value = item.songId
  }

  // 创建LrcData
  const onCreateLrcData = (songInfo: NeteaseSongItemSchemaType): LrcData | null => {
    if (!lyricsData.value?.lyric || !selectedSongId.value) return null

    const lrcData: LrcData = {
      id: songInfo.songId,
      title: songInfo.name,
      artist: songInfo.singer,
      duration: parseTimeStringToSeconds(songInfo.duration), // 转秒
      syncedLyrics: lyricsData.value.lyric || '',
    }

    return lrcData
  }

  return {
    // 状态
    selectedSongId,
    lyricsData,
    lyricsStatus,
    lyricsError,

    // 方法
    onView,
    onCreateLrcData,
  }
}

import type { AdminCourseDetailData } from '~/types/course'
import { useNuxtApp, useRoute } from '#imports'
import { isEmpty } from 'lodash-es'
import { onMounted, ref } from 'vue'
import { useCoursePackStore } from '~/stores/coursePackStore'

export function useCourseDetail() {
  const loading = ref(false)
  const error = ref('')
  const courseDetail = ref<AdminCourseDetailData | undefined>(undefined)

  const route = useRoute()
  const { $trpc } = useNuxtApp()

  const coursePackId = typeof route.query.coursePackId === 'string' ? route.query.coursePackId : undefined
  const courseId = typeof route.query.courseId === 'string' ? route.query.courseId : undefined
  const coursePackStore = useCoursePackStore()

  async function fetchCourseDetail() {
    if (coursePackId === undefined || courseId === undefined) {
      error.value = '未提供课程包ID或课程ID'
      courseDetail.value = undefined
      coursePackStore.setCurrentCourse(undefined)
      return
    }
    loading.value = true
    error.value = ''
    try {
      const res = await $trpc.adminCourse.getCourseDetail.query({
        coursePackId,
        courseId,
      })
      if (!isEmpty(res) && res?.type === 'music' && !!res?.mediaUrl) {
        courseDetail.value = res
        coursePackStore.setCurrentCourse(res)

        // 将歌词更新到 lrcStore
      }
      else {
        console.error('返回数据格式不正确', res)
        error.value = '返回数据格式不正确'
        courseDetail.value = undefined
        coursePackStore.setCurrentCourse(undefined)
      }
    }
    catch {
      error.value = '获取课程详情失败'
      courseDetail.value = undefined
      coursePackStore.setCurrentCourse(undefined)
    }
    finally {
      loading.value = false
    }
  }

  onMounted(() => {
    if (coursePackId !== undefined && courseId !== undefined) {
      void fetchCourseDetail()
    }
  })

  return {
    loading,
    error,
    courseDetail,
    fetchCourseDetail,
  }
}

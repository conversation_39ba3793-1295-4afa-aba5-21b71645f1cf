import type { SentenceUtterance, Translation, AlignedTranslation, AlignmentResult, AlignmentOptions } from '~/utils/processing/time/alignmentTypes'
import { alignSentenceTimestamps, generateSRTSubtitles, validateAlignmentQuality } from '~/utils/processing/time/timestampAlignment'

/**
 * 简化的时间戳对齐composable
 * 使用函数式API，专注于句子级对齐
 */
export function useTimestampAlignment() {
  // 状态管理
  const isProcessing = ref(false)
  const hasResults = computed(() => alignmentResult.value !== null)

  // 数据状态
  const sourceUtterances = ref<SentenceUtterance[]>([])
  const translations = ref<Translation[]>([])
  const alignmentResult = ref<AlignmentResult | null>(null)
  const alignedTranslations = ref<AlignedTranslation[]>([])

  // 预览状态
  const showPreview = ref(false)

  // 配置状态
  const options = ref<AlignmentOptions>({
    similarityThreshold: 0.6,
    normalizeText: true,
    caseSensitive: false,
  })

  // 错误状态
  const error = ref<string | null>(null)
  const warnings = ref<string[]>([])

  /**
   * 导入源数据（utterances）
   */
  function importSourceData(utterances: SentenceUtterance[]) {
    try {
      error.value = null
      warnings.value = []

      if (!utterances || utterances.length === 0) {
        error.value = '源数据不能为空'
        return false
      }

      // 基本验证
      for (let i = 0; i < utterances.length; i++) {
        const utterance = utterances[i]
        if (!utterance.text || utterance.text.trim() === '') {
          warnings.value.push(`第${i + 1}行缺少文本内容`)
        }
        if (utterance.start_time >= utterance.end_time) {
          warnings.value.push(`第${i + 1}行时间戳无效`)
        }
      }

      sourceUtterances.value = utterances

      // 清除之前的结果
      alignmentResult.value = null
      alignedTranslations.value = []
      showPreview.value = false

      return true
    } catch (err) {
      error.value = `导入源数据失败：${err instanceof Error ? err.message : '未知错误'}`
      return false
    }
  }

  /**
   * 导入翻译数据
   */
  function importTranslationData(translationData: Translation[]) {
    try {
      error.value = null

      if (!translationData || translationData.length === 0) {
        error.value = '翻译数据不能为空'
        return false
      }

      // 基本验证
      for (let i = 0; i < translationData.length; i++) {
        const translation = translationData[i]
        if (!translation.source || !translation.translation) {
          warnings.value.push(`第${i + 1}行翻译数据不完整`)
        }
      }

      translations.value = translationData

      // 清除之前的结果
      alignmentResult.value = null
      alignedTranslations.value = []
      showPreview.value = false

      return true
    } catch (err) {
      error.value = `导入翻译数据失败：${err instanceof Error ? err.message : '未知错误'}`
      return false
    }
  }

  /**
   * 执行时间戳对齐
   */
  async function performAlignment() {
    if (sourceUtterances.value.length === 0 || translations.value.length === 0) {
      error.value = '请先导入源数据和翻译数据'
      return false
    }

    try {
      isProcessing.value = true
      error.value = null

      // 执行对齐
      const result = alignSentenceTimestamps(sourceUtterances.value, translations.value, options.value)

      alignmentResult.value = result
      alignedTranslations.value = result.alignedTranslations

      // 验证质量
      const quality = validateAlignmentQuality(result)
      if (!quality.isGood) {
        warnings.value = [...warnings.value, ...quality.warnings]
      }

      console.log('🎯 时间戳对齐完成:', {
        总数量: result.totalCount,
        成功数量: result.successCount,
        平均置信度: (result.averageConfidence * 100).toFixed(1) + '%',
        处理时间: result.processingTime + 'ms',
      })

      return true
    } catch (err) {
      error.value = `对齐处理失败：${err instanceof Error ? err.message : '未知错误'}`
      return false
    } finally {
      isProcessing.value = false
    }
  }

  /**
   * 显示对齐预览
   */
  function showAlignmentPreview() {
    if (hasResults.value) {
      showPreview.value = true
    }
  }

  /**
   * 隐藏对齐预览
   */
  function hideAlignmentPreview() {
    showPreview.value = false
  }

  /**
   * 确认应用对齐结果
   */
  function confirmAlignment(): boolean {
    if (!hasResults.value) {
      error.value = '没有可确认的对齐结果'
      return false
    }

    // 这里可以添加确认逻辑，比如保存到本地存储等
    console.log('✅ 对齐结果已确认应用')
    hideAlignmentPreview()
    return true
  }

  /**
   * 生成SRT字幕文件内容
   */
  function generateSRT(): string {
    if (alignedTranslations.value.length === 0) {
      throw new Error('没有可生成的对齐结果')
    }
    return generateSRTSubtitles(alignedTranslations.value)
  }

  /**
   * 下载SRT文件
   */
  function downloadSRT(filename?: string) {
    try {
      const srtContent = generateSRT()
      const blob = new Blob([srtContent], { type: 'text/plain;charset=utf-8' })
      const url = URL.createObjectURL(blob)
      const a = document.createElement('a')
      a.href = url
      a.download = filename || `字幕_${new Date().toISOString().slice(0, 19).replace(/:/g, '-')}.srt`
      document.body.appendChild(a)
      a.click()
      document.body.removeChild(a)
      URL.revokeObjectURL(url)
    } catch (err) {
      error.value = `下载失败：${err instanceof Error ? err.message : '未知错误'}`
    }
  }

  /**
   * 更新配置
   */
  function updateConfig(newOptions: Partial<AlignmentOptions>) {
    options.value = { ...options.value, ...newOptions }
  }

  /**
   * 重置所有状态
   */
  function reset() {
    sourceUtterances.value = []
    translations.value = []
    alignmentResult.value = null
    alignedTranslations.value = []
    error.value = null
    warnings.value = []
    showPreview.value = false
    isProcessing.value = false
  }

  /**
   * 计算成功统计信息
   */
  const successStatistics = computed(() => {
    if (!alignmentResult.value) return null

    const result = alignmentResult.value
    const successRate = result.totalCount > 0 ? (result.successCount / result.totalCount) * 100 : 0

    return {
      total: result.totalCount,
      success: result.successCount,
      partial: 0, // 简化版本不区分partial
      successRate: Math.round(successRate),
    }
  })

  /**
   * 获取失败项目
   */
  const failedItems = computed(() => {
    if (!alignmentResult.value) return []

    return alignmentResult.value.alignedTranslations
      .map((item, index) => ({ item, index }))
      .filter(({ item }) => item.confidence < 0.5)
      .map(({ item, index }) => ({
        index,
        sourceText: item.source,
        error: { message: `置信度过低: ${(item.confidence * 100).toFixed(1)}%` },
      }))
  })

  /**
   * 预览数据
   */
  const previewData = computed(() => {
    if (!alignmentResult.value) return null

    const result = alignmentResult.value

    return {
      before: translations.value.map((item, index) => ({
        index,
        text: item.translation,
        time: undefined,
      })),
      after: result.alignedTranslations.map((item, index) => ({
        index,
        text: item.translation,
        time: formatTimeDisplay(item.startTime, item.endTime),
        confidence: item.confidence,
        strategy: item.matchStrategy,
      })),
      summary: {
        total: result.totalCount,
        matched: result.successCount,
        failed: result.totalCount - result.successCount,
        averageConfidence: result.averageConfidence,
      },
    }
  })

  /**
   * 格式化时间显示
   */
  function formatTimeDisplay(startTime: number, endTime: number): string {
    const formatTime = (seconds: number) => {
      const hours = Math.floor(seconds / 3600)
      const minutes = Math.floor((seconds % 3600) / 60)
      const secs = Math.floor(seconds % 60)
      const ms = Math.floor((seconds % 1) * 1000)

      return `${hours.toString().padStart(2, '0')}:${minutes.toString().padStart(2, '0')}:${secs.toString().padStart(2, '0')},${ms.toString().padStart(3, '0')}`
    }

    return `${formatTime(startTime)} --> ${formatTime(endTime)}`
  }

  return {
    // 状态
    isProcessing: readonly(isProcessing),
    hasResults: readonly(hasResults),
    error: readonly(error),
    warnings: readonly(warnings),
    showPreview: readonly(showPreview),

    // 数据
    sourceData: readonly(sourceUtterances),
    translationData: readonly(translations),
    alignmentResult: readonly(alignmentResult),
    alignedTranslations: readonly(alignedTranslations),

    // 计算属性
    successStatistics: readonly(successStatistics),
    failedItems: readonly(failedItems),
    previewData: readonly(previewData),

    // 方法
    importSourceData,
    importTranslationData,
    performAlignment,
    showAlignmentPreview,
    hideAlignmentPreview,
    confirmAlignment,
    generateSRT,
    downloadSRT,
    updateConfig,
    reset,
  }
}

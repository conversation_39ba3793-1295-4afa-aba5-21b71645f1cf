import type { Subtitle } from "~/types/subtitle"
import type { useSubtitleStore } from "~/stores/subtitleStore"

export function useSubtitleActions(
  subtitle: Ref<Subtitle>,
  store: ReturnType<typeof useSubtitleStore>,
  isAddDisabled: Ref<boolean>,
  isLast: Ref<boolean>
) {
  const addTooltipContent = computed(() => {
    return isAddDisabled.value ? "没有空间添加" : "添加一行"
  })

  function handleAddSubtitle() {
    if (!isAddDisabled.value) {
      store.addSubtitle(subtitle.value.id)
    }
  }

  function handleDeleteSubtitle() {
    store.deleteSubtitle(subtitle.value.uuid)
  }

  function handleMergeSubtitle() {
    if (!isLast.value) {
      store.mergeWithNextSubtitle(subtitle.value.uuid)
    }
  }

  return {
    addTooltipContent,
    handleAddSubtitle,
    handleDeleteSubtitle,
    handleMergeSubtitle
  }
} 
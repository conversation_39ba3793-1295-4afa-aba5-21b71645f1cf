import { parseSrtTime } from '~/utils/processing/time/timeParser'

export function useSubtitleTimeValidation(
  startTime: Ref<string>,
  endTime: Ref<string>,
  nextSubtitleStartTime?: Ref<string | null>,
  prevSubtitleEndTime?: Ref<string | null>,
) {
  const isTimeOrderValid = computed(() => {
    const startSeconds = parseSrtTime(startTime.value)
    const endSeconds = parseSrtTime(endTime.value)

    if (startSeconds === null || endSeconds === null) return true
    return startSeconds <= endSeconds
  })

  const isStartTimeOrderValid = computed(() => {
    if (!prevSubtitleEndTime?.value) return true
    const startSeconds = parseSrtTime(startTime.value)
    const prevEndSeconds = parseSrtTime(prevSubtitleEndTime.value)
    if (startSeconds === null || prevEndSeconds === null) return true
    return startSeconds >= prevEndSeconds
  })

  const isEndTimeOrderValid = computed(() => {
    if (!nextSubtitleStartTime?.value) return true
    const endSeconds = parseSrtTime(endTime.value)
    const nextStartSeconds = parseSrtTime(nextSubtitleStartTime.value)
    if (endSeconds === null || nextStartSeconds === null) return true
    return endSeconds <= nextStartSeconds
  })

  const isAddDisabled = computed(() => {
    if (!nextSubtitleStartTime?.value) return false

    const currentEndTimeSec = parseSrtTime(endTime.value)
    const nextStartTimeSec = parseSrtTime(nextSubtitleStartTime.value)

    if (currentEndTimeSec === null || nextStartTimeSec === null) return false

    const timeDiff = nextStartTimeSec - currentEndTimeSec
    return timeDiff <= 0.001
  })

  return {
    isTimeOrderValid,
    isAddDisabled,
    isStartTimeOrderValid,
    isEndTimeOrderValid,
  }
}

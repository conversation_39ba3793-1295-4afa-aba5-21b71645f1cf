import type { RecognitionResult, RecognitionUtterance } from '../api/useSpeechRecognition'
import { formatTimeToString } from '~/utils/processing/time/timeFormatter'

export function useSubtitleGeneration() {
  // 下载字幕文件
  const downloadSubtitle = (recognitionResult: RecognitionResult, fileName?: string) => {
    if (!recognitionResult) return

    // 生成SRT格式字幕
    let srtContent = ''
    recognitionResult.utterances.forEach((utterance: RecognitionUtterance, index: number) => {
      srtContent += `${index + 1}\n`
      srtContent += `${formatTimeToString(utterance.start_time)} --> ${formatTimeToString(utterance.end_time)}\n`
      srtContent += `${utterance.text}\n\n`
    })

    // 下载文件
    const blob = new Blob([srtContent], { type: 'text/plain;charset=utf-8' })
    const url = URL.createObjectURL(blob)
    const a = document.createElement('a')
    a.href = url
    a.download = `${fileName || 'subtitle'}.srt`
    document.body.appendChild(a)
    a.click()
    document.body.removeChild(a)
    URL.revokeObjectURL(url)
  }

  // 编辑字幕
  const editSubtitle = (recognitionResult: RecognitionResult) => {
    // 将识别结果转换为编辑器格式并跳转
    if (recognitionResult) {
      // 这里可以将结果存储到 store 中，然后跳转到编辑器
      navigateTo('/')
    }
  }

  return {
    downloadSubtitle,
    editSubtitle,
  }
}

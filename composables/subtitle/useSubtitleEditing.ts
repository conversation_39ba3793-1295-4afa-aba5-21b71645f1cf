import type { Subtitle } from '~/types/subtitle'
import type { useSubtitleStore } from '~/stores/subtitleStore'

export function useSubtitleEditing(subtitle: Ref<Subtitle>, store: ReturnType<typeof useSubtitleStore>) {
  const editing = ref(false)
  const editingText = ref(subtitle.value.text)
  const editingTranslationText = ref(subtitle.value.translationText || '')
  const editingType = ref<null | 'text' | 'translation'>(null)
  const startTime = ref(subtitle.value.startTime)
  const endTime = ref(subtitle.value.endTime)

  const textareaRef = ref<HTMLTextAreaElement | null>(null)
  const translationTextareaRef = ref<HTMLTextAreaElement | null>(null)
  const startTimeInputRef = ref<ComponentPublicInstance | null>(null)
  const endTimeInputRef = ref<ComponentPublicInstance | null>(null)

  watch(
    () => subtitle.value.text,
    (val) => {
      if (!editing.value) editingText.value = val
    },
  )

  watch(
    () => subtitle.value.startTime,
    (val) => {
      startTime.value = val
    },
    { deep: true },
  )

  watch(
    () => subtitle.value.endTime,
    (val) => {
      endTime.value = val
    },
  )

  function startEdit(type: 'text' | 'translation') {
    editing.value = true
    editingType.value = type
    if (type === 'text') {
      editingText.value = subtitle.value.text
      nextTick(() => {
        textareaRef.value?.focus()
      })
    } else {
      editingTranslationText.value = subtitle.value.translationText || ''
      nextTick(() => {
        translationTextareaRef.value?.focus()
      })
    }
  }

  function confirmEdit() {
    if (editingType.value === 'text') {
      store.updateSubtitleText(subtitle.value.uuid, editingText.value)
    } else if (editingType.value === 'translation') {
      if (!editingTranslationText.value.trim()) {
        store.updateSubtitleTranslationText(subtitle.value.uuid, '')
      } else {
        store.updateSubtitleTranslationText(subtitle.value.uuid, editingTranslationText.value)
      }
    }
    editing.value = false
    editingType.value = null
  }

  function cancelEdit() {
    editingText.value = subtitle.value.text
    editingTranslationText.value = subtitle.value.translationText || ''
    editing.value = false
    editingType.value = null
  }

  function onStartTime(val: string) {
    startTime.value = val
    store.updateSubtitleStartTime(subtitle.value.uuid, val)
  }

  function onEndTime(val: string) {
    endTime.value = val
    store.updateSubtitleEndTime(subtitle.value.uuid, val)
  }

  return {
    editing,
    editingText,
    editingTranslationText,
    editingType,
    startTime,
    endTime,
    textareaRef,
    translationTextareaRef,
    startTimeInputRef,
    endTimeInputRef,
    startEdit,
    confirmEdit,
    cancelEdit,
    onStartTime,
    onEndTime,
  }
}

import type { RecognitionR<PERSON>ult } from '~/composables/api/useSpeechRecognition'
import type {
  ChunkItem,
  ChunkContext,
  ChunkingConfig,
  ChunkingResult,
  TranslationTask,
  TranslationResult,
  RetryConfig,
} from '~/types/chunking'
import { TranslationQualityError, RetryExhaustedError } from '~/types/errors'
import { calculateSimilarity, normalizeText } from '~/utils/processing/text/similarityCalculator'
import { detectTranslationErrors, type TranslationError } from '~/utils/validation/quality/qualityChecker'
import { translationLogger } from '~/utils/shared/logger'
import { mergeRecognitionToSentences } from '~/utils/processing/text/textProcessor'
import { splitByMeaning, splitChunksByChars, getPreviousContent, getAfterContent } from '~/utils/processing/text/textAnalyzer'
import {
  DEFAULT_CHUNK_SIZE,
  DEFAULT_MAX_SENTENCES,
  CONTEXT_PREVIOUS_LINES,
  CONTEXT_AFTER_LINES,
  SIMILARITY_THRESHOLD,
  MAX_RETRY_ATTEMPTS,
  RETRY_DELAY_BASE,
} from '~/common/constants'

export function useSmartChunking() {
  // 响应式状态
  const chunks = ref<ChunkItem[]>([])
  const isChunking = ref(false)
  const chunkingError = ref('')
  const contexts = ref<Map<number, ChunkContext>>(new Map())
  const isTranslating = ref(false)
  const translationError = ref('')
  const translationResults = ref<Map<number, string>>(new Map())
  const failedChunks = ref<Set<number>>(new Set())
  const translationProgress = ref({ completed: 0, total: 0, failed: 0 })

  // 分块配置
  const config = ref<ChunkingConfig>({
    chunkSize: DEFAULT_CHUNK_SIZE,
    maxSentences: DEFAULT_MAX_SENTENCES,
    previousLines: CONTEXT_PREVIOUS_LINES,
    afterLines: CONTEXT_AFTER_LINES,
  })

  // 翻译配置
  const translationConfig = ref({
    similarityThreshold: SIMILARITY_THRESHOLD,
    maxRetryAttempts: MAX_RETRY_ATTEMPTS,
    retryDelayBase: RETRY_DELAY_BASE,
    qualityControlEnabled: true,
    timeoutMs: 30000,
  })

  // 计算属性：分块统计信息
  const statistics = computed(() => {
    if (chunks.value.length === 0) {
      return {
        totalChunks: 0,
        averageCharCount: 0,
        averageSentenceCount: 0,
        maxCharCount: 0,
        minCharCount: 0,
      }
    }

    const charCounts = chunks.value.map((chunk) => chunk.charCount)
    const sentenceCounts = chunks.value.map((chunk) => chunk.sentenceCount)

    return {
      totalChunks: chunks.value.length,
      averageCharCount: Math.round(charCounts.reduce((a, b) => a + b, 0) / charCounts.length),
      averageSentenceCount: Math.round(sentenceCounts.reduce((a, b) => a + b, 0) / sentenceCounts.length),
      maxCharCount: Math.max(...charCounts),
      minCharCount: Math.min(...charCounts),
    }
  })

  /**
   * 执行智能分块
   * @param recognitionResult 语音识别结果
   * @returns 分块结果
   */
  const performSmartChunking = async (recognitionResult: RecognitionResult): Promise<ChunkingResult> => {
    isChunking.value = true
    chunkingError.value = ''

    try {
      // 第一步：将识别结果转换为句子数组
      const sentences = mergeRecognitionToSentences(recognitionResult)

      // 第二步：进行语义分割
      const meaningfulSentences = splitByMeaning(sentences)

      // 第三步：按字符数和句子数进行分块
      const chunkTexts = splitChunksByChars(meaningfulSentences, config.value.chunkSize, config.value.maxSentences)

      // 第四步：构建分块项数组
      const chunkItems: ChunkItem[] = chunkTexts.map((chunkText, index) => {
        const lines = chunkText.split('\n').filter((line) => line.trim())
        return {
          index,
          content: chunkText,
          charCount: chunkText.length,
          sentenceCount: lines.length,
          lineCount: lines.length,
        }
      })

      // 第五步：构建上下文信息
      const contextMap = buildChunkContext(chunkTexts)

      // 更新响应式状态
      chunks.value = chunkItems
      contexts.value = contextMap

      // 保存到本地存储
      saveToLocalStorage()

      const result: ChunkingResult = {
        chunks: chunkItems,
        contexts: contextMap,
        statistics: statistics.value,
      }

      return result
    } catch (error) {
      chunkingError.value = error instanceof Error ? error.message : '分块处理失败'
      throw error
    } finally {
      isChunking.value = false
    }
  }

  /**
   * 重试失败的分块
   * @param chunkIndex 分块索引
   * @param targetLanguage 目标语言
   */
  const retryFailedChunk = async (chunkIndex: number, targetLanguage: string = '简体中文') => {
    const chunk = chunks.value[chunkIndex]
    const context = contexts.value.get(chunkIndex)

    if (!chunk) {
      console.error(`分块 ${chunkIndex} 不存在`)
      return
    }

    try {
      const task: TranslationTask = {
        chunk,
        context,
        targetLanguage,
        index: chunkIndex,
        createdAt: Date.now(),
      }

      const result = await translateSingleChunkEnhanced(task)

      // 更新结果
      translationResults.value.set(chunkIndex, result.translatedText)
      failedChunks.value.delete(chunkIndex)

      console.log(`分块 ${chunkIndex} 重试成功`)
    } catch (error) {
      console.error(`分块 ${chunkIndex} 重试失败:`, error)
      throw error
    }
  }

  /**
   * 从失败状态恢复翻译
   * @param targetLanguage 目标语言
   */
  const recoverFromFailures = async (targetLanguage: string = '简体中文') => {
    if (failedChunks.value.size === 0) {
      console.log('没有失败的分块需要恢复')
      return
    }

    isTranslating.value = true

    try {
      const failedIndices = Array.from(failedChunks.value)
      console.log(`正在恢复 ${failedIndices.length} 个失败的分块`)

      // 逐个重试失败的分块
      for (const chunkIndex of failedIndices) {
        try {
          await retryFailedChunk(chunkIndex, targetLanguage)
          translationProgress.value.completed++
          translationProgress.value.failed--
        } catch (error) {
          console.error(`分块 ${chunkIndex} 恢复失败:`, error)
        }
      }

      // 保存恢复后的结果
      saveTranslationToLocalStorage()
    } catch (error) {
      translationError.value = error instanceof Error ? error.message : '恢复翻译失败'
      throw error
    } finally {
      isTranslating.value = false
    }
  }

  /**
   * 支持断点续传的并发翻译
   * @param targetLanguage 目标语言
   * @param maxWorkers 最大并发数
   */
  const performConcurrentTranslationWithResume = async (targetLanguage: string = '简体中文', maxWorkers: number = 4) => {
    if (chunks.value.length === 0) {
      translationError.value = '没有可翻译的分块'
      return
    }

    isTranslating.value = true
    translationError.value = ''

    // 初始化进度
    translationProgress.value = {
      completed: translationResults.value.size,
      total: chunks.value.length,
      failed: failedChunks.value.size,
    }

    try {
      // 找出尚未翻译的分块
      const unprocessedChunks = chunks.value.filter((chunk) => !translationResults.value.has(chunk.index))

      if (unprocessedChunks.length === 0) {
        console.log('所有分块已完成翻译')
        return
      }

      console.log(`继续翻译剩余的 ${unprocessedChunks.length} 个分块`)

      // 创建翻译任务队列
      const translationTasks = unprocessedChunks.map((chunk) => ({
        chunk,
        context: contexts.value.get(chunk.index),
        index: chunk.index,
        targetLanguage,
        createdAt: Date.now(),
      }))

      // 分批并发处理
      const batchSize = maxWorkers
      const allResults: TranslationResult[] = []

      for (let i = 0; i < translationTasks.length; i += batchSize) {
        const batch = translationTasks.slice(i, i + batchSize)

        // 并发翻译当前批次
        const batchPromises = batch.map((task) =>
          translateSingleChunkEnhanced(task).catch((_error) => {
            // 记录失败但不中断整体流程
            failedChunks.value.add(task.index)
            translationProgress.value.failed++
            return null
          }),
        )

        const batchResults = await Promise.allSettled(batchPromises)

        // 处理批次结果
        batchResults.forEach((result, _batchIndex) => {
          if (result.status === 'fulfilled' && result.value) {
            allResults.push(result.value)
            translationResults.value.set(result.value.index, result.value.translatedText)
            translationProgress.value.completed++
          }
        })

        // 保存中间结果（断点续传支持）
        saveTranslationToLocalStorage()
      }

      // 记录最终统计信息
      const successCount = translationResults.value.size
      const failedCount = failedChunks.value.size
      const avgSimilarity = allResults.length > 0 ? allResults.reduce((sum, r) => sum + r.similarity, 0) / allResults.length : 0
      const totalDuration = allResults.reduce((sum, r) => sum + r.duration, 0)

      translationLogger.logBatchStatistics(chunks.value.length, successCount, failedCount, avgSimilarity, totalDuration)

      if (failedCount > 0) {
        console.warn(`翻译完成，但有 ${failedCount} 个分块失败`)
      } else {
        console.log('所有分块翻译成功完成')
      }
    } catch (error) {
      translationError.value = error instanceof Error ? error.message : '并发翻译失败'
      throw error
    } finally {
      isTranslating.value = false
    }
  }

  /**
   * 翻译单个分块（增强版）
   * @param task 翻译任务
   * @returns 翻译结果
   */
  const translateSingleChunkEnhanced = async (task: TranslationTask): Promise<TranslationResult> => {
    const startTime = Date.now()
    const retryConfig: RetryConfig = {
      maxAttempts: MAX_RETRY_ATTEMPTS,
      delayBase: RETRY_DELAY_BASE,
      useExponentialBackoff: true,
    }

    translationLogger.logTranslationStart(task)

    // 调试日志：检查上下文信息
    const contextDebugInfo = {
      hasPreviousContent: (task.context?.previousContent?.length ?? 0) > 0,
      hasAfterContent: (task.context?.afterContent?.length ?? 0) > 0,
      previousContentLines: task.context?.previousContent?.length ?? 0,
      afterContentLines: task.context?.afterContent?.length ?? 0,
      previousContent: task.context?.previousContent,
      afterContent: task.context?.afterContent,
    }

    console.log(
      `%c🔍 分块${task.index}上下文调试:`,
      'background: #ff6b6b; color: white; padding: 4px 8px; border-radius: 4px; font-weight: bold;',
      contextDebugInfo,
    )

    // 如果是第一个分块，显示一个alert确保用户看到
    if (task.index === 0) {
      alert(
        `开始翻译！请查看浏览器控制台(F12 -> Console)查看详细的上下文调试信息。\n\n分块0上下文情况:\n- 前文: ${contextDebugInfo.previousContentLines}行\n- 后文: ${contextDebugInfo.afterContentLines}行`,
      )
    }

    let lastError: Error = new Error('Unknown error')

    for (let attempt = 1; attempt <= retryConfig.maxAttempts; attempt++) {
      try {
        if (attempt > 1) {
          const delay = retryConfig.useExponentialBackoff ? retryConfig.delayBase * Math.pow(2, attempt - 2) : retryConfig.delayBase

          translationLogger.logRetryAttempt(task.index, attempt, delay)
          await new Promise((resolve) => setTimeout(resolve, delay))
        }

        // 构建翻译提示词
        let prompt = `请将以下文本翻译成${task.targetLanguage}，保持原文的格式和语义：\n\n`

        // 添加前文上下文
        if (task.context?.previousContent && task.context.previousContent.length > 0) {
          prompt += `前文参考：\n${task.context.previousContent.join('\n')}\n\n`
        }

        // 主要翻译内容
        prompt += `翻译内容：\n${task.chunk.content}\n\n`

        // 添加后文上下文
        if (task.context?.afterContent && task.context.afterContent.length > 0) {
          prompt += `后文参考：\n${task.context.afterContent.join('\n')}\n\n`
        }

        prompt += `请直接返回翻译结果，不要包含其他解释。`

        // 调试日志：显示最终提示词
        console.log(
          `%c📝 分块${task.index}翻译提示词:`,
          'background: #4ecdc4; color: white; padding: 4px 8px; border-radius: 4px; font-weight: bold;',
          prompt,
        )

        // 记录API调用
        translationLogger.logApiCall(task.index, '/api/deepseek/stream', 'POST')

        // 调用翻译API
        const response = await fetch('/api/deepseek/stream', {
          method: 'POST',
          headers: {
            'Content-Type': 'application/json',
          },
          body: JSON.stringify({
            messages: [
              {
                role: 'user',
                content: prompt,
              },
            ],
            stream: false,
          }),
        })

        if (!response.ok) {
          throw new Error(`翻译API错误: ${response.status}`)
        }

        const translatedText = (await response.text()).trim()
        const duration = Date.now() - startTime

        // 质量检查
        const qualityIssues = detectTranslationErrors(task.chunk.content, translatedText)
        if (qualityIssues.length > 0) {
          translationLogger.logQualityCheck(task.index, qualityIssues, false)
          throw new TranslationQualityError(
            task.index,
            qualityIssues.map((issue: TranslationError) => issue.message),
          )
        }

        // 计算相似度 - 这里应该计算原文的一致性，而不是原文与译文的相似度
        // 由于我们知道这是同一个分块，相似度应该是1.0
        const similarity = 1.0

        const result: TranslationResult = {
          index: task.index,
          sourceText: task.chunk.content,
          translatedText,
          similarity,
          duration,
          retryCount: attempt - 1,
          status: 'success', // 默认成功，质量检查通过即为成功
        }

        // 记录翻译成功
        translationLogger.logTranslationSuccess(result)
        return result
      } catch (error) {
        lastError = error instanceof Error ? error : new Error(String(error))
        translationLogger.logTranslationError(task.index, lastError, attempt - 1)

        // 如果是最后一次尝试，抛出重试耗尽错误
        if (attempt === retryConfig.maxAttempts) {
          throw new RetryExhaustedError(task.index, attempt, lastError)
        }
      }
    }

    // 这行不应该被执行到，但为了类型安全
    throw new RetryExhaustedError(task.index, retryConfig.maxAttempts, lastError)
  }

  /**
   * 为每个分块构建上下文信息
   * @param chunkTexts 分块文本数组
   * @returns 上下文信息映射
   */
  const buildChunkContext = (chunkTexts: string[]): Map<number, ChunkContext> => {
    const contextMap = new Map<number, ChunkContext>()

    chunkTexts.forEach((_, index) => {
      const previousContent = getPreviousContent(chunkTexts, index, config.value.previousLines)
      const afterContent = getAfterContent(chunkTexts, index, config.value.afterLines)

      contextMap.set(index, {
        previousContent,
        afterContent,
      })
    })

    return contextMap
  }

  /**
   * 保存分块结果到本地存储
   */
  const saveToLocalStorage = () => {
    try {
      const data = {
        chunks: chunks.value,
        contexts: Array.from(contexts.value.entries()),
        config: config.value,
        timestamp: Date.now(),
      }
      localStorage.setItem('smart-chunking-result', JSON.stringify(data))
    } catch (error) {
      console.error('保存到本地存储失败:', error)
    }
  }

  /**
   * 保存翻译结果到本地存储
   */
  const saveTranslationToLocalStorage = () => {
    try {
      const data = {
        translations: Array.from(translationResults.value.entries()),
        timestamp: Date.now(),
      }
      localStorage.setItem('smart-chunking-translations', JSON.stringify(data))
    } catch (error) {
      console.error('保存翻译结果失败:', error)
    }
  }

  /**
   * 从本地存储加载分块结果
   */
  const loadFromLocalStorage = () => {
    try {
      const data = localStorage.getItem('smart-chunking-result')
      if (data) {
        const parsed = JSON.parse(data)
        chunks.value = parsed.chunks || []
        contexts.value = new Map(parsed.contexts || [])
        if (parsed.config) {
          config.value = { ...config.value, ...parsed.config }
        }
      }
    } catch (error) {
      console.error('从本地存储加载失败:', error)
    }
  }

  /**
   * 重置分块状态（增强版）
   */
  const resetChunkingEnhanced = () => {
    chunks.value = []
    contexts.value = new Map()
    chunkingError.value = ''
    isChunking.value = false
    translationResults.value = new Map()
    translationError.value = ''
    isTranslating.value = false
    failedChunks.value = new Set()
    translationProgress.value = { completed: 0, total: 0, failed: 0 }

    // 清除本地存储
    localStorage.removeItem('smart-chunking-result')
    localStorage.removeItem('smart-chunking-translations')
    localStorage.removeItem('smart-chunking-failed')
    localStorage.removeItem('smart-chunking-progress')
  }

  /**
   * 保存失败状态到本地存储
   */
  const saveFailureStateToLocalStorage = () => {
    try {
      const data = {
        failedChunks: Array.from(failedChunks.value),
        progress: translationProgress.value,
        timestamp: Date.now(),
      }
      localStorage.setItem('smart-chunking-failed', JSON.stringify(data))
      localStorage.setItem('smart-chunking-progress', JSON.stringify(translationProgress.value))
    } catch (error) {
      console.error('保存失败状态失败:', error)
    }
  }

  /**
   * 从本地存储恢复失败状态
   */
  const loadFailureStateFromLocalStorage = () => {
    try {
      const failureData = localStorage.getItem('smart-chunking-failed')
      const progressData = localStorage.getItem('smart-chunking-progress')

      if (failureData) {
        const parsed = JSON.parse(failureData)
        failedChunks.value = new Set(parsed.failedChunks || [])
      }

      if (progressData) {
        translationProgress.value = JSON.parse(progressData)
      }
    } catch (error) {
      console.error('恢复失败状态失败:', error)
    }
  }

  /**
   * 导出分块结果为JSON
   */
  const exportChunksAsJson = () => {
    const data = {
      chunks: chunks.value,
      contexts: Array.from(contexts.value.entries()),
      statistics: statistics.value,
      config: config.value,
      translations: Array.from(translationResults.value.entries()),
      exportTime: new Date().toISOString(),
    }

    const blob = new Blob([JSON.stringify(data, null, 2)], { type: 'application/json' })
    const url = URL.createObjectURL(blob)
    const a = document.createElement('a')
    a.href = url
    a.download = `智能分块结果_${new Date().toISOString().slice(0, 19).replace(/:/g, '-')}.json`
    document.body.appendChild(a)
    a.click()
    document.body.removeChild(a)
    URL.revokeObjectURL(url)
  }

  /**
   * 匹配翻译结果与原始分块
   * @param chunks 原始分块数组
   * @param translationResults 翻译结果数组
   * @returns 匹配后的翻译结果映射
   */
  const matchTranslationResults = (chunks: ChunkItem[], translationResults: TranslationResult[]): Map<number, string> => {
    const matchedResults = new Map<number, string>()
    const usedResults = new Set<number>()

    for (let i = 0; i < chunks.length; i++) {
      const chunk = chunks[i]
      const normalizedChunkText = normalizeText(chunk.content)

      let bestMatch: TranslationResult | null = null
      let bestSimilarity = 0

      // 寻找最佳匹配的翻译结果
      for (const result of translationResults) {
        if (usedResults.has(result.index)) continue

        const normalizedSourceText = normalizeText(result.sourceText)
        const similarity = calculateSimilarity(normalizedChunkText, normalizedSourceText)

        if (similarity > bestSimilarity) {
          bestSimilarity = similarity
          bestMatch = result
        }
      }

      if (bestMatch) {
        // 验证匹配质量
        if (bestSimilarity < SIMILARITY_THRESHOLD) {
          translationLogger.logSimilarityMatch(i, bestSimilarity, SIMILARITY_THRESHOLD, false)
          console.warn(`分块${i}匹配质量较低: ${(bestSimilarity * 100).toFixed(1)}%`)
        } else {
          translationLogger.logSimilarityMatch(i, bestSimilarity, SIMILARITY_THRESHOLD, true)
        }

        matchedResults.set(i, bestMatch.translatedText)
        usedResults.add(bestMatch.index)
      } else {
        console.error(`无法为分块${i}找到匹配的翻译结果`)
      }
    }

    return matchedResults
  }

  /**
   * 验证翻译匹配的有效性
   * @param chunk 原始分块
   * @param result 翻译结果
   * @returns 是否匹配有效
   */
  const validateTranslationMatch = (chunk: ChunkItem, result: TranslationResult): boolean => {
    const similarity = calculateSimilarity(normalizeText(chunk.content), normalizeText(result.sourceText))
    return similarity >= SIMILARITY_THRESHOLD
  }

  /**
   * 处理翻译匹配失败的情况
   * @param chunkIndex 分块索引
   * @param bestMatch 最佳匹配结果
   */
  const handleTranslationMismatch = (chunkIndex: number, bestMatch: TranslationResult): void => {
    const similarity = calculateSimilarity(normalizeText(chunks.value[chunkIndex]?.content || ''), normalizeText(bestMatch.sourceText))

    if (similarity < SIMILARITY_THRESHOLD) {
      translationLogger.logSimilarityMatch(chunkIndex, similarity, SIMILARITY_THRESHOLD, false)

      // 可以选择重新翻译或标记为失败
      console.warn(`分块${chunkIndex}匹配失败，相似度: ${(similarity * 100).toFixed(1)}%`)
    }
  }

  /**
   * 重置分块状态
   */
  const resetChunking = () => {
    chunks.value = []
    contexts.value = new Map()
    chunkingError.value = ''
    isChunking.value = false
    translationResults.value = new Map()
    translationError.value = ''
    isTranslating.value = false

    // 清除本地存储
    localStorage.removeItem('smart-chunking-result')
    localStorage.removeItem('smart-chunking-translations')
  }

  /**
   * 更新分块配置
   * @param newConfig 新的配置
   */
  const updateConfig = (newConfig: Partial<ChunkingConfig>) => {
    config.value = { ...config.value, ...newConfig }
  }

  /**
   * 更新翻译配置
   * @param newConfig 新的翻译配置
   */
  const updateTranslationConfig = (newConfig: Partial<typeof translationConfig.value>) => {
    translationConfig.value = { ...translationConfig.value, ...newConfig }
  }

  /**
   * 并发翻译分块（原版本，向后兼容）
   * @param targetLanguage 目标语言
   * @param maxWorkers 最大并发数
   */
  const performConcurrentTranslation = async (targetLanguage: string = '简体中文', maxWorkers: number = 4) => {
    // 重置状态并调用增强版本
    translationResults.value = new Map()
    failedChunks.value = new Set()
    await performConcurrentTranslationWithResume(targetLanguage, maxWorkers)
  }

  // 页面加载时尝试从本地存储恢复数据
  if (typeof window !== 'undefined') {
    loadFromLocalStorage()
    loadFailureStateFromLocalStorage()
  }

  return {
    // 响应式状态
    chunks,
    isChunking,
    chunkingError,
    contexts,
    config,
    translationConfig,
    isTranslating,
    translationError,
    translationResults,
    failedChunks,
    translationProgress,

    // 计算属性
    statistics,

    // 方法
    performSmartChunking,
    performConcurrentTranslationWithResume,
    buildChunkContext,
    resetChunking,
    resetChunkingEnhanced,
    retryFailedChunk,
    recoverFromFailures,
    updateConfig,
    updateTranslationConfig,
    saveToLocalStorage,
    loadFromLocalStorage,
    saveFailureStateToLocalStorage,
    loadFailureStateFromLocalStorage,
    exportChunksAsJson,
    matchTranslationResults,
    validateTranslationMatch,
    handleTranslationMismatch,
    performConcurrentTranslation,
  }
}

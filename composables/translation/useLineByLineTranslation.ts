import type { RecognitionResult } from '~/composables/api/useSpeechRecognition'

/**
 * 翻译步骤类型
 */
type TranslationStep = 'faithfulness' | 'expressiveness'

/**
 * 忠实翻译结果
 */
interface FaithfulTranslation {
  origin: string
  direct: string
}

/**
 * 表达优化翻译结果
 */
interface ExpressiveTranslation extends FaithfulTranslation {
  reflect: string
  free: string
}

/**
 * 逐行翻译配置
 */
interface LineTranslationConfig {
  targetLanguage: string
  sourceLanguage: string
  reflectTranslate: boolean
  maxRetryAttempts: number
  timeoutMs: number
}

/**
 * 翻译质量验证结果
 */
interface ValidationResult {
  status: 'success' | 'error'
  message: string
}

/**
 * 逐行翻译上下文
 */
interface TranslationContext {
  previousContent: string[]
  afterContent: string[]
  summary: string
  thingsToNote: string
}

export function useLineByLineTranslation() {
  // 响应式状态
  const isTranslating = ref(false)
  const translationError = ref('')
  const faithfulResults = ref<Map<number, Record<string, FaithfulTranslation>>>(new Map())
  const expressiveResults = ref<Map<number, Record<string, ExpressiveTranslation>>>(new Map())
  const finalResults = ref<Map<number, string>>(new Map())

  // 翻译配置
  const config = ref<LineTranslationConfig>({
    targetLanguage: '简体中文',
    sourceLanguage: '中文',
    reflectTranslate: true,
    maxRetryAttempts: 3,
    timeoutMs: 30000,
  })

  /**
   * 生成共享上下文提示词
   */
  const generateSharedPrompt = (context: TranslationContext): string => {
    return `### 上下文信息
<previous_content>
${context.previousContent.join('\n')}
</previous_content>

<subsequent_content>
${context.afterContent.join('\n')}
</subsequent_content>

### 内容摘要
${context.summary}

### 需要注意的术语
${context.thingsToNote}`
  }

  /**
   * 生成忠实翻译提示词
   */
  const getPromptFaithfulness = (lines: string, sharedPrompt: string): string => {
    const lineSplits = lines.split('\n')

    const jsonDict: Record<string, { origin: string; direct: string }> = {}
    lineSplits.forEach((line, index) => {
      jsonDict[`${index + 1}`] = {
        origin: line,
        direct: `direct ${config.value.targetLanguage} translation ${index + 1}.`,
      }
    })

    const jsonFormat = JSON.stringify(jsonDict, null, 2)

    return `## 角色
你是一名专业的Netflix字幕翻译师，精通${config.value.sourceLanguage}和${config.value.targetLanguage}以及它们各自的文化背景。
你的专长在于准确理解原始${config.value.sourceLanguage}文本的语义和结构，并忠实地将其翻译成${config.value.targetLanguage}，同时保持原文含义。

## 任务
我们有一段原始${config.value.sourceLanguage}字幕需要直接翻译成${config.value.targetLanguage}。这些字幕来自特定的上下文，可能包含特定的主题和术语。

1. 逐行将原始${config.value.sourceLanguage}字幕翻译成${config.value.targetLanguage}
2. 确保翻译忠实于原文，准确传达原始含义
3. 考虑上下文和专业术语

${sharedPrompt}

<translation_principles>
1. 忠实原文：准确传达原文的内容和含义，不得任意改变、添加或省略内容。
2. 术语准确：正确使用专业术语，保持术语的一致性。
3. 理解上下文：充分理解并体现文本的背景和上下文关系。
</translation_principles>

## 输入
<subtitles>
${lines}
</subtitles>

## 仅以JSON格式输出，不要其他文本
\`\`\`json
${jsonFormat}
\`\`\`

注意：以 \`\`\`json 开始你的回答，以 \`\`\` 结束，不要添加任何其他文本。`
  }

  /**
   * 生成表达优化提示词
   */
  const getPromptExpressiveness = (faithfulResult: Record<string, FaithfulTranslation>, lines: string, sharedPrompt: string): string => {
    const jsonFormat = Object.fromEntries(
      Object.entries(faithfulResult).map(([key, value]) => [
        key,
        {
          origin: value.origin,
          direct: value.direct,
          reflect: '你对直接翻译的反思',
          free: '你的自由翻译',
        },
      ]),
    )

    const jsonFormatStr = JSON.stringify(jsonFormat, null, 2)

    return `## 角色
你是一名专业的Netflix字幕翻译师和语言顾问。
你的专长不仅在于准确理解原始${config.value.sourceLanguage}，还在于优化${config.value.targetLanguage}翻译，使其更符合目标语言的表达习惯和文化背景。

## 任务
我们已经有了原始${config.value.sourceLanguage}字幕的直接翻译版本。
你的任务是反思和改进这些直接翻译，创造更自然流畅的${config.value.targetLanguage}字幕。

1. 逐行分析直接翻译结果，指出存在的问题
2. 提供详细的修改建议
3. 基于你的分析进行自由翻译
4. 不要在翻译中添加注释或解释，因为字幕是给观众阅读的
5. 自由翻译中不要留空行，因为字幕是给观众阅读的

${sharedPrompt}

<Translation Analysis Steps>
请使用两步思考过程逐行处理文本：

1. 直接翻译反思：
   - 评估语言流畅性
   - 检查语言风格是否与原文一致
   - 检查字幕的简洁性，指出翻译过于冗长的地方

2. ${config.value.targetLanguage}自由翻译：
   - 以上下文流畅自然为目标，符合${config.value.targetLanguage}表达习惯
   - 确保${config.value.targetLanguage}观众容易理解和接受
   - 调整语言风格以匹配主题（例如，教程使用随意语言，技术内容使用专业术语，纪录片使用正式语言）
</Translation Analysis Steps>
       
## 输入
<subtitles>
${lines}
</subtitles>

## 仅以JSON格式输出，不要其他文本
\`\`\`json
${jsonFormatStr}
\`\`\`

注意：以 \`\`\`json 开始你的回答，以 \`\`\` 结束，不要添加任何其他文本。`
  }

  /**
   * 验证翻译结果格式
   */
  const validateTranslationResult = (
    result: Record<string, Record<string, string>>,
    requiredKeys: string[],
    requiredSubKeys: string[],
  ): ValidationResult => {
    // 检查必需的键
    if (!requiredKeys.every((key) => key in result)) {
      const missingKeys = requiredKeys.filter((key) => !(key in result))
      return {
        status: 'error',
        message: `缺少必需的键: ${missingKeys.join(', ')}`,
      }
    }

    // 检查所有项目的必需子键
    for (const key in result) {
      if (!requiredSubKeys.every((subKey) => subKey in result[key])) {
        const missingSubKeys = requiredSubKeys.filter((subKey) => !(subKey in result[key]))
        return {
          status: 'error',
          message: `项目 ${key} 中缺少必需的子键: ${missingSubKeys.join(', ')}`,
        }
      }
    }

    return { status: 'success', message: '翻译完成' }
  }

  /**
   * 调用翻译API
   */
  const callTranslationAPI = async (prompt: string): Promise<string> => {
    const response = await fetch('/api/deepseek/stream', {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify({
        messages: [
          {
            role: 'user',
            content: prompt,
          },
        ],
        stream: false,
      }),
    })

    if (!response.ok) {
      throw new Error(`翻译API错误: ${response.status}`)
    }

    return await response.text()
  }

  /**
   * 重试翻译
   */
  const retryTranslation = async (
    prompt: string,
    expectedLength: number,
    stepName: TranslationStep,
    chunkIndex: number = 0,
  ): Promise<Record<string, FaithfulTranslation> | Record<string, ExpressiveTranslation>> => {
    const validationFunctions = {
      faithfulness: (data: Record<string, Record<string, string>>) =>
        validateTranslationResult(
          data,
          Array.from({ length: expectedLength }, (_, i) => `${i + 1}`),
          ['direct'],
        ),
      expressiveness: (data: Record<string, Record<string, string>>) =>
        validateTranslationResult(
          data,
          Array.from({ length: expectedLength }, (_, i) => `${i + 1}`),
          ['free'],
        ),
    }

    for (let retry = 0; retry < config.value.maxRetryAttempts; retry++) {
      try {
        const response = await callTranslationAPI(prompt + ' '.repeat(retry))

        // 解析JSON响应
        const cleanedResponse = response.replace(/```json\n?|\n?```/g, '').trim()
        const result = JSON.parse(cleanedResponse) as Record<string, Record<string, string>>

        // 验证格式
        const validation = validationFunctions[stepName](result)
        if (validation.status === 'error') {
          throw new Error(validation.message)
        }

        // 检查行数匹配
        if (Object.keys(result).length === expectedLength) {
          return result as unknown as Record<string, FaithfulTranslation> | Record<string, ExpressiveTranslation>
        }

        if (retry < config.value.maxRetryAttempts - 1) {
          console.warn(`${stepName}翻译分块${chunkIndex}失败，重试中...`)
        }
      } catch (error) {
        if (retry === config.value.maxRetryAttempts - 1) {
          throw new Error(`${stepName}翻译分块${chunkIndex}经过${config.value.maxRetryAttempts}次重试后失败`)
        }
      }
    }

    throw new Error(`重试次数耗尽`)
  }

  /**
   * 核心翻译函数 - 实现两步翻译策略
   */
  const translateLines = async (
    lines: string,
    context: TranslationContext,
    chunkIndex: number = 0,
  ): Promise<{ translatedText: string; originalText: string }> => {
    const sharedPrompt = generateSharedPrompt(context)
    const lineCount = lines.split('\n').length

    // 步骤一：忠实翻译
    const prompt1 = getPromptFaithfulness(lines, sharedPrompt)
    const faithResult = (await retryTranslation(prompt1, lineCount, 'faithfulness', chunkIndex)) as Record<string, FaithfulTranslation>

    // 处理换行符
    for (const key in faithResult) {
      faithResult[key].direct = faithResult[key].direct.replace(/\n/g, ' ')
    }

    // 保存忠实翻译结果
    faithfulResults.value.set(chunkIndex, faithResult)

    // 如果禁用反思翻译，直接返回忠实翻译结果
    if (!config.value.reflectTranslate) {
      const translatedText = Object.values(faithResult)
        .map((item: FaithfulTranslation) => item.direct.trim())
        .join('\n')

      finalResults.value.set(chunkIndex, translatedText)
      return { translatedText, originalText: lines }
    }

    // 步骤二：表达优化
    const prompt2 = getPromptExpressiveness(faithResult, lines, sharedPrompt)
    const expressResult = (await retryTranslation(prompt2, lineCount, 'expressiveness', chunkIndex)) as Record<
      string,
      ExpressiveTranslation
    >

    // 保存表达优化结果
    expressiveResults.value.set(chunkIndex, expressResult)

    const translatedText = Object.values(expressResult)
      .map((item: ExpressiveTranslation) => item.free.replace(/\n/g, ' ').trim())
      .join('\n')

    // 最终验证行数匹配
    if (lines.split('\n').length !== translatedText.split('\n').length) {
      throw new Error(`原文行数与翻译行数不匹配：原文${lines.split('\n').length}行，翻译${translatedText.split('\n').length}行`)
    }

    finalResults.value.set(chunkIndex, translatedText)
    return { translatedText, originalText: lines }
  }

  /**
   * 批量翻译识别结果
   */
  const translateRecognitionResult = async (
    recognitionResult: RecognitionResult,
    context: Partial<TranslationContext> = {},
  ): Promise<string> => {
    if (!recognitionResult.utterances || recognitionResult.utterances.length === 0) {
      throw new Error('没有可翻译的识别结果')
    }

    isTranslating.value = true
    translationError.value = ''

    try {
      // 将识别结果转换为文本行
      const lines = recognitionResult.utterances.map((utterance) => utterance.text).join('\n')

      // 构建默认上下文
      const translationContext: TranslationContext = {
        previousContent: context.previousContent || [],
        afterContent: context.afterContent || [],
        summary: context.summary || '这是一段语音识别的字幕内容，需要进行准确翻译。',
        thingsToNote: context.thingsToNote || '请注意保持专业术语的准确性和语言表达的自然流畅。',
      }

      const result = await translateLines(lines, translationContext, 0)
      return result.translatedText
    } catch (error) {
      translationError.value = error instanceof Error ? error.message : '翻译失败'
      throw error
    } finally {
      isTranslating.value = false
    }
  }

  /**
   * 重置翻译状态
   */
  const resetTranslation = () => {
    isTranslating.value = false
    translationError.value = ''
    faithfulResults.value.clear()
    expressiveResults.value.clear()
    finalResults.value.clear()
  }

  /**
   * 更新翻译配置
   */
  const updateConfig = (newConfig: Partial<LineTranslationConfig>) => {
    config.value = { ...config.value, ...newConfig }
  }

  /**
   * 获取翻译统计信息
   */
  const getTranslationStatistics = computed(() => {
    return {
      totalTranslated: finalResults.value.size,
      faithfulOnly: faithfulResults.value.size - expressiveResults.value.size,
      withExpressive: expressiveResults.value.size,
      reflectTranslateEnabled: config.value.reflectTranslate,
    }
  })

  return {
    // 响应式状态
    isTranslating,
    translationError,
    faithfulResults,
    expressiveResults,
    finalResults,
    config,

    // 计算属性
    getTranslationStatistics,

    // 方法
    translateLines,
    translateRecognitionResult,
    resetTranslation,
    updateConfig,
    generateSharedPrompt,
    getPromptFaithfulness,
    getPromptExpressiveness,
  }
}

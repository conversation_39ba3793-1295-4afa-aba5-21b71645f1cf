import { getSummaryPrompt, setLanguageConfig } from '~/common/prompts'
import type { CustomTermsJson, SummaryResult } from '~/common/prompts'
import { extractSummary } from '~/services/summaryService'

/**
 * 文本摘要和术语提取的Composable
 */
export function useSummaryExtraction() {
  // 响应式状态
  const isExtracting = ref(false)
  const extractionResult = ref<SummaryResult | null>(null)
  const errorMessage = ref('')

  /**
   * 提取文本摘要和术语
   * @param sourceContent 源文本内容
   * @param customTerms 可选的已存在术语
   */
  const extractSummaryFn = async (sourceContent: string, customTerms?: CustomTermsJson) => {
    if (!sourceContent.trim()) {
      errorMessage.value = '请提供有效的文本内容'
      return
    }

    isExtracting.value = true
    errorMessage.value = ''
    extractionResult.value = null

    try {
      const result = await extractSummary(sourceContent, customTerms)
      console.log('%c AT 🥝 result 🥝-31', 'font-size:13px; background:#c76f65; color:#ffb3a9;', result)
      extractionResult.value = result
    } catch (error) {
      errorMessage.value = error instanceof Error ? error.message : '提取失败'
      console.error('摘要提取失败:', error)
    } finally {
      isExtracting.value = false
    }
  }

  /**
   * 获取提示词预览（用于调试或自定义）
   * @param sourceContent 源文本内容
   * @param customTerms 可选的已存在术语
   */
  const getPromptPreview = (sourceContent: string, customTerms?: CustomTermsJson) => {
    return getSummaryPrompt(sourceContent, customTerms)
  }

  /**
   * 配置语言设置
   * @param srcLang 源语言
   * @param tgtLang 目标语言
   */
  const configureLanguages = (srcLang: string, tgtLang: string) => {
    setLanguageConfig(srcLang, tgtLang)
  }

  /**
   * 重置状态
   */
  const reset = () => {
    isExtracting.value = false
    extractionResult.value = null
    errorMessage.value = ''
  }

  /**
   * 设置缓存的摘要结果
   * @param cachedResult 缓存的摘要结果
   */
  const setCachedResult = (cachedResult: SummaryResult) => {
    extractionResult.value = cachedResult
    errorMessage.value = ''
  }

  return {
    // 状态
    isExtracting: readonly(isExtracting),
    extractionResult: readonly(extractionResult),
    errorMessage: readonly(errorMessage),

    // 方法
    extractSummaryFn,
    getPromptPreview,
    configureLanguages,
    reset,
    setCachedResult,
  }
}

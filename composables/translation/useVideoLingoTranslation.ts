import type { RecognitionResult } from '~/composables/api/useSpeechRecognition'

/**
 * VideoLingo翻译架构适配
 * 基于原始Python实现的完整翻译流程
 */

// 翻译步骤类型
type TranslationStep = 'faithfulness' | 'expressiveness'

// 忠实翻译结果
interface FaithfulTranslation {
  origin: string
  direct: string
}

// 表达优化翻译结果
interface ExpressiveTranslation extends FaithfulTranslation {
  reflect: string
  free: string
}

// 翻译块数据
interface TranslationChunk {
  index: number
  content: string
  charCount: number
  lineCount: number
}

// 翻译任务
interface TranslationTask {
  chunk: TranslationChunk
  previousContent: string[]
  afterContent: string[]
  themePrompt: string
  thingsToNote: string
  index: number
}

// 翻译结果
interface TranslationResult {
  index: number
  originalText: string
  translatedText: string
  similarity?: number
}

// 相似度匹配结果
interface SimilarityMatch {
  result: TranslationResult
  similarity: number
}

// VideoLingo翻译配置
interface VideoLingoConfig {
  targetLanguage: string
  sourceLanguage: string
  reflectTranslate: boolean
  maxWorkers: number
  chunkSize: number
  maxSentences: number
  maxRetryAttempts: number
  similarityThreshold: number
}

export function useVideoLingoTranslation() {
  // 响应式状态
  const isTranslating = ref(false)
  const translationError = ref('')
  const chunks = ref<TranslationChunk[]>([])
  const translationResults = ref<Map<number, string>>(new Map())
  const faithfulResults = ref<Map<number, Record<string, FaithfulTranslation>>>(new Map())
  const expressiveResults = ref<Map<number, Record<string, ExpressiveTranslation>>>(new Map())
  const translationProgress = ref({ completed: 0, total: 0, errors: 0 })

  // VideoLingo配置
  const config = ref<VideoLingoConfig>({
    targetLanguage: '简体中文',
    sourceLanguage: 'English',
    reflectTranslate: true,
    maxWorkers: 4,
    chunkSize: 600,        // 按文档推荐：600字符提供丰富上下文
    maxSentences: 10,      // 按文档推荐：最多10句，保持语义完整性
    maxRetryAttempts: 3,
    similarityThreshold: 0.9,
  })

  /**
   * 1. 智能分块算法 - 按文档实现的split_chunks_by_chars
   * 输入：已分割的句子数组
   * 输出：包含多个句子的文本块，用于提供上下文
   */
  const splitChunksByChars = (sentences: string[], chunkSize: number, maxSentences: number): string[] => {
    const chunks: string[] = []
    let chunk = ''
    let sentenceCount = 0

    for (const sentence of sentences) {
      const newLineContent = sentence + '\n'

      // 检查是否超过限制（按文档的双重限制机制）
      if (chunk.length + newLineContent.length > chunkSize || sentenceCount >= maxSentences) {
        if (chunk.trim()) {
          chunks.push(chunk.trim())
        }
        chunk = newLineContent
        sentenceCount = 1
      } else {
        chunk += newLineContent
        sentenceCount++
      }
    }

    // 添加最后一个分块
    if (chunk.trim()) {
      chunks.push(chunk.trim())
    }

    return chunks
  }

  /**
   * 2. 上下文构建机制
   */
  const getPreviousContent = (chunks: string[], chunkIndex: number): string[] => {
    if (chunkIndex === 0) return []
    return chunks[chunkIndex - 1].split('\n').slice(-3) // 获取前一块的最后3行
  }

  const getAfterContent = (chunks: string[], chunkIndex: number): string[] => {
    if (chunkIndex === chunks.length - 1) return []
    return chunks[chunkIndex + 1].split('\n').slice(0, 2) // 获取后一块的前2行
  }

  /**
   * 3. 共享上下文生成
   */
  const generateSharedPrompt = (previousContent: string[], afterContent: string[], summary: string, thingsToNote: string): string => {
    return `### 上下文信息
<previous_content>
${previousContent.join('\n')}
</previous_content>

<subsequent_content>
${afterContent.join('\n')}
</subsequent_content>

### 内容摘要
${summary}

### 需要注意的术语
${thingsToNote}`
  }

  /**
   * 4. 忠实翻译提示词生成
   */
  const getPromptFaithfulness = (lines: string, sharedPrompt: string): string => {
    const lineSplits = lines.split('\n')
    const jsonDict: Record<string, { origin: string; direct: string }> = {}

    lineSplits.forEach((line, index) => {
      jsonDict[`${index + 1}`] = {
        origin: line,
        direct: `direct ${config.value.targetLanguage} translation ${index + 1}.`,
      }
    })

    const jsonFormat = JSON.stringify(jsonDict, null, 2)

    return `## 角色
你是一名专业的Netflix字幕翻译师，精通${config.value.sourceLanguage}和${config.value.targetLanguage}以及它们各自的文化背景。
你的专长在于准确理解原始${config.value.sourceLanguage}文本的语义和结构，并忠实地将其翻译成${config.value.targetLanguage}，同时保持原文含义。

## 任务
我们有一段原始${config.value.sourceLanguage}字幕需要直接翻译成${config.value.targetLanguage}。这些字幕来自特定的上下文，可能包含特定的主题和术语。

1. 逐行将原始${config.value.sourceLanguage}字幕翻译成${config.value.targetLanguage}
2. 确保翻译忠实于原文，准确传达原始含义
3. 考虑上下文和专业术语

${sharedPrompt}

<translation_principles>
1. 忠实原文：准确传达原文的内容和含义，不得任意改变、添加或省略内容。
2. 术语准确：正确使用专业术语，保持术语的一致性。
3. 理解上下文：充分理解并体现文本的背景和上下文关系。
</translation_principles>

## 输入
<subtitles>
${lines}
</subtitles>

## 仅以JSON格式输出，不要其他文本
\`\`\`json
${jsonFormat}
\`\`\`

注意：以 \`\`\`json 开始你的回答，以 \`\`\` 结束，不要添加任何其他文本。`
  }

  /**
   * 5. 表达优化提示词生成
   */
  const getPromptExpressiveness = (faithfulResult: Record<string, FaithfulTranslation>, lines: string, sharedPrompt: string): string => {
    const jsonFormat = Object.fromEntries(
      Object.entries(faithfulResult).map(([key, value]) => [
        key,
        {
          origin: value.origin,
          direct: value.direct,
          reflect: '你对直接翻译的反思',
          free: '你的自由翻译',
        },
      ]),
    )

    const jsonFormatStr = JSON.stringify(jsonFormat, null, 2)

    return `## 角色
你是一名专业的Netflix字幕翻译师和语言顾问。
你的专长不仅在于准确理解原始${config.value.sourceLanguage}，还在于优化${config.value.targetLanguage}翻译，使其更符合目标语言的表达习惯和文化背景。

## 任务
我们已经有了原始${config.value.sourceLanguage}字幕的直接翻译版本。
你的任务是反思和改进这些直接翻译，创造更自然流畅的${config.value.targetLanguage}字幕。

1. 逐行分析直接翻译结果，指出存在的问题
2. 提供详细的修改建议
3. 基于你的分析进行自由翻译
4. 不要在翻译中添加注释或解释，因为字幕是给观众阅读的
5. 自由翻译中不要留空行，因为字幕是给观众阅读的

${sharedPrompt}

<Translation Analysis Steps>
请使用两步思考过程逐行处理文本：

1. 直接翻译反思：
   - 评估语言流畅性
   - 检查语言风格是否与原文一致
   - 检查字幕的简洁性，指出翻译过于冗长的地方

2. ${config.value.targetLanguage}自由翻译：
   - 以上下文流畅自然为目标，符合${config.value.targetLanguage}表达习惯
   - 确保${config.value.targetLanguage}观众容易理解和接受
   - 调整语言风格以匹配主题（例如，教程使用随意语言，技术内容使用专业术语，纪录片使用正式语言）
</Translation Analysis Steps>

## 输入
<subtitles>
${lines}
</subtitles>

## 仅以JSON格式输出，不要其他文本
\`\`\`json
${jsonFormatStr}
\`\`\`

注意：以 \`\`\`json 开始你的回答，以 \`\`\` 结束，不要添加任何其他文本。`
  }

  /**
   * 6. 调用翻译API
   */
  const callTranslationAPI = async (prompt: string): Promise<string> => {
    const response = await fetch('/api/deepseek/stream', {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify({
        messages: [{ role: 'user', content: prompt }],
        stream: false,
      }),
    })

    if (!response.ok) {
      throw new Error(`翻译API错误: ${response.status}`)
    }

    return await response.text()
  }

  /**
   * 7. 重试翻译机制
   */
  const retryTranslation = async (
    prompt: string,
    expectedLength: number,
    stepName: TranslationStep,
    chunkIndex: number,
  ): Promise<Record<string, FaithfulTranslation | ExpressiveTranslation>> => {
    for (let retry = 0; retry < config.value.maxRetryAttempts; retry++) {
      try {
        const response = await callTranslationAPI(prompt + ' '.repeat(retry))
        const cleanedResponse = response.replace(/```json\n?|\n?```/g, '').trim()
        const result = JSON.parse(cleanedResponse)

        // 验证行数匹配
        if (Object.keys(result).length === expectedLength) {
          return result
        }

        if (retry < config.value.maxRetryAttempts - 1) {
          console.warn(`${stepName}翻译分块${chunkIndex}失败，重试中...`)
        }
      } catch (error) {
        if (retry === config.value.maxRetryAttempts - 1) {
          throw new Error(`${stepName}翻译分块${chunkIndex}经过${config.value.maxRetryAttempts}次重试后失败`)
        }
      }
    }
    throw new Error('重试次数耗尽')
  }

  /**
   * 8. translate_lines函数 - 核心翻译逻辑（按文档实现）
   * 输入：包含多个句子的文本块
   * 输出：逐句翻译结果，严格保持句子数量一致
   */
  const translateLines = async (
    lines: string,
    previousContent: string[],
    afterContent: string[],
    thingsToNote: string,
    summaryPrompt: string,
    chunkIndex: number,
  ): Promise<{ translatedText: string; originalText: string }> => {
    const sharedPrompt = generateSharedPrompt(previousContent, afterContent, summaryPrompt, thingsToNote)

    // 第一步：忠实翻译 - 严格按照文档的JSON格式
    const lineSplits = lines.split('\n').filter(line => line.trim() !== '')
    console.log(`分块${chunkIndex}: 开始翻译${lineSplits.length}行文本`)

    // 构建JSON字典，确保一句对一句
    const jsonDict: Record<string, { origin: string; direct: string }> = {}
    lineSplits.forEach((line, index) => {
      jsonDict[`${index + 1}`] = {
        origin: line,
        direct: `direct ${config.value.targetLanguage} translation ${index + 1}.`,
      }
    })

    // 忠实翻译
    const faithPrompt = getPromptFaithfulness(lines, sharedPrompt)
    const faithResult = await retryTranslation(faithPrompt, lineSplits.length, 'faithfulness', chunkIndex)

    // 保存忠实翻译结果
    faithfulResults.value.set(chunkIndex, faithResult)

    let translatedText = ''

    // 第二步：表达优化（可选）
    if (config.value.reflectTranslate) {
      const expressPrompt = getPromptExpressiveness(faithResult, lines, sharedPrompt)
      const expressResult = await retryTranslation(expressPrompt, lineSplits.length, 'expressiveness', chunkIndex) as Record<string, ExpressiveTranslation>

      // 保存表达优化结果
      expressiveResults.value.set(chunkIndex, expressResult)

      // 从表达优化结果提取free翻译
      translatedText = Object.keys(expressResult)
        .sort((a, b) => parseInt(a) - parseInt(b))
        .map(key => expressResult[key].free.replace(/\n/g, ' ').trim())
        .join('\n')
    } else {
      // 从忠实翻译结果提取direct翻译
      translatedText = Object.keys(faithResult)
        .sort((a, b) => parseInt(a) - parseInt(b))
        .map(key => (faithResult[key] as FaithfulTranslation).direct.replace(/\n/g, ' ').trim())
        .join('\n')
    }

    // 关键验证：确保原文行数与译文行数完全一致（按文档要求）
    const originalLines = lines.split('\n').filter(line => line.trim() !== '')
    const translatedLines = translatedText.split('\n').filter(line => line.trim() !== '')
    
    if (originalLines.length !== translatedLines.length) {
      console.error(`分块${chunkIndex}行数不匹配:`, {
        原文行数: originalLines.length,
        译文行数: translatedLines.length,
        原文: originalLines,
        译文: translatedLines
      })
      throw new Error(`分块${chunkIndex}原文行数与翻译行数不匹配: 原文${originalLines.length}行，译文${translatedLines.length}行`)
    }

    console.log(`分块${chunkIndex}: 翻译完成，${originalLines.length}行对${translatedLines.length}行`)

    return { translatedText, originalText: lines }
  }

  /**
   * 9. translate_chunk函数 - 单块翻译处理器
   */
  const translateChunk = async (task: TranslationTask): Promise<TranslationResult> => {
    try {
      const result = await translateLines(
        task.chunk.content,
        task.previousContent,
        task.afterContent,
        task.thingsToNote,
        task.themePrompt,
        task.index,
      )

      return {
        index: task.index,
        originalText: result.originalText,
        translatedText: result.translatedText,
      }
    } catch (error) {
      console.error(`分块${task.index}翻译失败:`, error)
      throw error
    }
  }

  /**
   * 10. 相似度匹配算法
   */
  const calculateSimilarity = (text1: string, text2: string): number => {
    const normalize = (text: string): string => text.replace(/\s+/g, '').toLowerCase()

    const normalized1 = normalize(text1)
    const normalized2 = normalize(text2)

    if (normalized1 === normalized2) return 1.0

    // 简单的相似度计算 - 可以替换为更复杂的算法
    const longer = normalized1.length > normalized2.length ? normalized1 : normalized2
    const shorter = normalized1.length > normalized2.length ? normalized2 : normalized1

    if (longer.length === 0) return 1.0

    const matches = shorter.split('').filter((char, index) => char === longer[index]).length
    return matches / longer.length
  }

  /**
   * 11. 结果重组逻辑（按文档的translate_all实现）
   * 将分块翻译结果重组为句子级别的平行列表
   */
  const matchTranslationResults = (originalChunks: string[], results: TranslationResult[]): Map<number, string> => {
    const srcTextList: string[] = []
    const transTextList: string[] = []

    // 按文档：逐块处理，将每块的句子分别添加到列表中
    originalChunks.forEach((chunkText, chunkIndex) => {
      const chunkLines = chunkText.split('\n').filter(line => line.trim() !== '')
      srcTextList.push(...chunkLines) // 逐句添加原文

      // 计算与所有结果的相似度，找到最佳匹配
      const similarities: SimilarityMatch[] = results.map((result) => ({
        result,
        similarity: calculateSimilarity(
          chunkText.replace(/\s+/g, '').toLowerCase(), 
          result.originalText.replace(/\s+/g, '').toLowerCase()
        ),
      }))

      // 找到最佳匹配
      const bestMatch = similarities.reduce((best, current) => (current.similarity > best.similarity ? current : best))

      // 检查相似度阈值
      if (bestMatch.similarity < config.value.similarityThreshold) {
        console.warn(`分块${chunkIndex}匹配失败，相似度: ${bestMatch.similarity.toFixed(3)}`)
        throw new Error(`分块${chunkIndex}翻译匹配失败`)
      } else if (bestMatch.similarity < 1.0) {
        console.warn(`分块${chunkIndex}相似匹配，相似度: ${bestMatch.similarity.toFixed(3)}`)
      }

      // 将匹配的翻译结果按行分割并添加
      const translatedLines = bestMatch.result.translatedText.split('\n').filter(line => line.trim() !== '')
      transTextList.push(...translatedLines) // 逐句添加译文

      // 验证当前块的行数匹配
      if (chunkLines.length !== translatedLines.length) {
        console.error(`分块${chunkIndex}行数不匹配:`, {
          原文行数: chunkLines.length,
          译文行数: translatedLines.length,
          原文: chunkLines,
          译文: translatedLines
        })
        throw new Error(`分块${chunkIndex}句子数量不匹配`)
      }
    })

    // 构建最终的句子级Map：索引 → 翻译文本
    const finalResults = new Map<number, string>()
    transTextList.forEach((translation, index) => {
      finalResults.set(index, translation)
    })

    // 验证总体行数匹配
    if (srcTextList.length !== transTextList.length) {
      console.error('总体行数不匹配:', {
        原文总行数: srcTextList.length,
        译文总行数: transTextList.length
      })
      throw new Error(`翻译结果总行数不匹配: 原文${srcTextList.length}行，译文${transTextList.length}行`)
    }

    console.log(`✅ 翻译重组完成: ${srcTextList.length}句原文对应${transTextList.length}句译文`)
    
    return finalResults
  }

  /**
   * 12. translate_all函数 - VideoLingo主控制器
   */
  const translateAll = async (
    recognitionResult: RecognitionResult,
    context: { summary: string; thingsToNote: string },
  ): Promise<string> => {
    if (!recognitionResult.utterances || recognitionResult.utterances.length === 0) {
      throw new Error('没有可翻译的识别结果')
    }

    isTranslating.value = true
    translationError.value = ''

    try {
      // 第一步：文本预处理和分块
      const sentences = recognitionResult.utterances.map((utterance) => utterance.text)
      const chunkTexts = splitChunksByChars(sentences, config.value.chunkSize, config.value.maxSentences)

      // 构建分块数据
      const chunkItems: TranslationChunk[] = chunkTexts.map((content, index) => ({
        index,
        content,
        charCount: content.length,
        lineCount: content.split('\n').length,
      }))

      chunks.value = chunkItems

      // 第二步：构建翻译任务
      const tasks: TranslationTask[] = chunkTexts.map((chunkText, index) => ({
        chunk: chunkItems[index],
        previousContent: getPreviousContent(chunkTexts, index),
        afterContent: getAfterContent(chunkTexts, index),
        themePrompt: context.summary,
        thingsToNote: context.thingsToNote,
        index,
      }))

      // 第三步：并发执行翻译任务
      translationProgress.value = { completed: 0, total: tasks.length, errors: 0 }

      const results: TranslationResult[] = []
      const maxWorkers = Math.min(config.value.maxWorkers, tasks.length)

      // 简单的并发控制 - 分批处理
      for (let i = 0; i < tasks.length; i += maxWorkers) {
        const batch = tasks.slice(i, i + maxWorkers)

        const batchPromises = batch.map(async (task) => {
          try {
            const result = await translateChunk(task)
            translationProgress.value.completed++
            return result
          } catch (error) {
            translationProgress.value.errors++
            console.error(`分块${task.index}翻译失败:`, error)
            throw error
          }
        })

        const batchResults = await Promise.all(batchPromises)
        results.push(...batchResults)
      }

      // 第四步：结果匹配和整合
      results.sort((a, b) => a.index - b.index) // 按原始顺序排序
      const matchedResults = matchTranslationResults(chunkTexts, results)

      // 保存结果
      translationResults.value = matchedResults

      // 第五步：构建最终翻译文本
      const finalText = Array.from(matchedResults.values()).join('\n')

      return finalText
    } catch (error) {
      translationError.value = error instanceof Error ? error.message : '翻译失败'
      throw error
    } finally {
      isTranslating.value = false
    }
  }

  /**
   * 重置翻译状态
   */
  const resetTranslation = () => {
    isTranslating.value = false
    translationError.value = ''
    chunks.value = []
    translationResults.value.clear()
    faithfulResults.value.clear()
    expressiveResults.value.clear()
    translationProgress.value = { completed: 0, total: 0, errors: 0 }
  }

  /**
   * 更新配置
   */
  const updateConfig = (newConfig: Partial<VideoLingoConfig>) => {
    config.value = { ...config.value, ...newConfig }
  }

  /**
   * 获取翻译统计信息
   */
  const getTranslationStatistics = computed(() => {
    return {
      totalChunks: chunks.value.length,
      completedChunks: translationResults.value.size,
      faithfulTranslations: faithfulResults.value.size,
      expressiveTranslations: expressiveResults.value.size,
      reflectTranslateEnabled: config.value.reflectTranslate,
      progress: translationProgress.value,
    }
  })

  return {
    // 响应式状态
    isTranslating,
    translationError,
    chunks,
    translationResults,
    faithfulResults,
    expressiveResults,
    config,

    // 计算属性
    getTranslationStatistics,

    // 核心方法
    translateAll,
    resetTranslation,
    updateConfig,

    // 内部方法（用于测试和调试）
    splitChunksByChars,
    translateLines,
    translateChunk,
    calculateSimilarity,
    matchTranslationResults,
  }
}

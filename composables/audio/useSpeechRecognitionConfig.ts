import type { ApiConfig } from '../api/useSpeechRecognition'

export function useSpeechRecognitionConfig() {
  // API 配置
  const apiConfig = reactive<ApiConfig>({
    appid: '9528098044',
    token: '-igcmNSCpyAXDFtrAF_YDcdQp6E0hYac',
  })

  // 响应式数据
  const selectedLanguage = ref('en-US')
  const selectedContentType = ref('auto')

  // 特色功能配置
  const features = reactive({
    usePunc: false, // 增加标点
    useDdc: false, // 水词标注
    withSpeakerInfo: false, // 说话人识别
    useItn: false, // 数字转换
  })

  const maxCharsPerLine = ref(46) // 每行最多显示字数
  const maxLines = ref(1) // 每屏最多显示行数

  // 语言选项（根据火山引擎API文档）
  const languageOptions = [
    { value: 'zh-CN', label: '中文普通话（支持中英混合及方言）' },
    { value: 'en-US', label: '英语（美国）' },
  ]

  // 字幕内容类型选项
  const contentTypeOptions = [
    { value: 'auto', label: '自动判断' },
    { value: 'speech', label: '语音' },
    { value: 'singing', label: '歌词' },
  ]

  // 推荐字符数映射
  const recommendedCharsMap: Record<string, number> = {
    'zh-CN': 15,
    ug: 55,
    'en-US': 55,
  }

  // 方法
  const getRecommendedCharsPerLine = () => {
    return recommendedCharsMap[selectedLanguage.value] || 46
  }

  const getLanguageLabel = (code: string) => {
    return languageOptions.find((opt) => opt.value === code)?.label || code
  }

  const getContentTypeLabel = (type: string) => {
    return contentTypeOptions.find((opt) => opt.value === type)?.label || type
  }

  // 监听语言变化，更新推荐字符数
  watch(selectedLanguage, () => {
    maxCharsPerLine.value = getRecommendedCharsPerLine()
  })

  return {
    // 响应式状态
    apiConfig,
    selectedLanguage,
    selectedContentType,
    features,
    maxCharsPerLine,
    maxLines,

    // 选项
    languageOptions,
    contentTypeOptions,

    // 方法
    getRecommendedCharsPerLine,
    getLanguageLabel,
    getContentTypeLabel,
  }
}

import { usePlayerStore } from '~/stores/playerStore'
import { useSubtitleStore } from '~/stores/subtitleStore'
import type { Subtitle } from '~/types/subtitle'
import { formatTimeToString } from '~/utils/processing/time/timeFormatter'
import { parseSrtTime } from '~/utils/processing/time/timeParser'
import { find, findIndex, sortBy, forEach } from 'lodash-es'

const REGION_COLOR = 'rgba(99, 102, 241, 0.22)' // 蓝紫色，提升透明度
const HANDLE_COLOR = '#a78bfa' // 浅紫色，和波形进度色一致

/**
 * 用于管理 WaveSurfer Regions（字幕区域）的 composable。
 * @param {unknown} regionsPluginInstance - WaveSurfer 的 regions 插件实例。
 * @returns {object} - 包含注册/注销事件和 region 同步的相关方法。
 */
export function useWaveformRegions(regionsPluginInstance: unknown) {
  const playerStore = usePlayerStore()
  const subtitleStore = useSubtitleStore()
  const subtitleRegionMap = ref(new Map())

  /**
   * 生成 Region 显示内容的 DOM 元素，包含起止时间和字幕文本。
   * @param {Subtitle} sub - 字幕对象。
   * @returns {HTMLElement} - 用于显示的 DOM 元素。
   */
  function getRegionContentElement(sub: Subtitle): HTMLElement {
    const { startTime, endTime, text, translationText } = sub

    // 创建内容元素
    const content = document.createElement('div')
    content.style.cssText =
      'display:flex;flex-direction:column;height:80%;justify-content:center;padding:0.22rem 0.48rem;box-sizing:border-radius:0.36rem;border-bottom:2px solid #a78bfa;background:rgba(99,102,241,0.16);box-shadow:0 2px 8px 0 rgba(99,102,241,0.10);min-width:0;pointer-events:auto;transition:border 0.2s;'

    // 时间戳
    const header = document.createElement('div')
    header.style.cssText =
      'display:flex;justify-content:space-between;width:100%;font-family:inherit;font-size:0.72rem;color:rgba(255,255,255,0.82);letter-spacing:0.01em;opacity:0.96;margin-top:0.08rem;margin-bottom:0.14rem;filter:drop-shadow(0 0 1px #000);border-radius:0.18rem;align-items:center;'
    header.innerHTML = `<em style="font-style:normal;">${startTime}</em><span style="flex:1"></span><em style="font-style:normal;">${endTime}</em>`

    // 文本
    const body = document.createElement('div')
    body.style.cssText =
      'font-family:inherit;font-size:1.04rem;font-weight:700;color:#f3f4f6;overflow:hidden;white-space:normal;line-height:1.48;letter-spacing:0.01em;background:transparent;border-radius:0;padding:0;filter:drop-shadow(0 0 2px #000);margin-top:1rem;max-width:100%;flex:1 1 auto;word-break:break-all;display:block;'
    body.textContent = `${text}`

    content.appendChild(header)
    content.appendChild(body)

    // 翻译文本（如果有）
    if (translationText) {
      const translation = document.createElement('div')
      translation.style.cssText =
        'font-family:inherit;font-size:0.68rem;font-weight:400;color:#e0e7ef;opacity:0.85;overflow:hidden;white-space:normal;line-height:1.28;letter-spacing:0.01em;background:transparent;border-radius:0;padding:0;margin-top:0.18rem;max-width:100%;flex:0 0 auto;word-break:break-all;display:block;'
      translation.textContent = `${translationText}`
      body.appendChild(translation)
    }
    return content
  }

  /**
   * 自定义 Region 拖拽手柄样式（虚线、宽度、透明背景）。
   * @param {unknown} region - 需要设置手柄样式的 Region 实例。
   */
  function styleRegionHandles(region: unknown) {
    const r = region as { element: HTMLElement }
    const leftHandle = r.element.querySelector('div[part="region-handle region-handle-left"]')
    if (leftHandle) {
      ;(leftHandle as HTMLElement).style.cssText += `border-left:2px dashed ${HANDLE_COLOR};width:4px;background:transparent;`
    }
    const rightHandle = r.element.querySelector('div[part="region-handle region-handle-right"]')
    if (rightHandle) {
      ;(rightHandle as HTMLElement).style.cssText += `border-right:2px dashed ${HANDLE_COLOR};width:4px;background:transparent;`
    }
  }

  /**
   * region-updated 事件监听器：自动吸附与自动合并（防止错位）逻辑。
   * @param {unknown} region - 被更新的 Region 实例。
   */
  const handleRegionUpdate = (region: unknown) => {
    // 类型断言
    const r = region as {
      id: string
      start: number
      end: number
      setContent: (c: HTMLElement) => void
      setOptions: (opts: { start?: number; end?: number; content?: HTMLElement }) => void
    }
    const subtitleUuid = r.id
    let newStartTime = r.start
    let newEndTime = r.end
    let adjusted = false

    // 找到对应字幕对象
    const currentSub = find(subtitleStore.subtitles, (s) => s.uuid === subtitleUuid)
    if (!currentSub) return

    // 获取所有 region，按 start 时间排序
    const allRegions = Array.from(subtitleRegionMap.value.values()) as Array<{
      id: string
      start: number
      end: number
    }>
    const sortedRegions = sortBy(allRegions, (a) => a.start)
    const currentIndex = findIndex(sortedRegions, (reg) => reg.id === subtitleUuid)
    const prevRegion = currentIndex > 0 ? sortedRegions[currentIndex - 1] : null
    const nextRegion = currentIndex < sortedRegions.length - 1 ? sortedRegions[currentIndex + 1] : null

    // 检查是否完全跨越前/后 region（自动合并/防止错位）
    if ((prevRegion && newEndTime <= prevRegion.start) || (nextRegion && newStartTime >= nextRegion.end)) {
      // 恢复到原始位置
      const originalStart = parseSrtTime(currentSub.startTime)
      const originalEnd = parseSrtTime(currentSub.endTime)
      if (originalStart == null || originalEnd == null) return
      r.setOptions({ start: originalStart, end: originalEnd })
      r.setOptions({
        content: getRegionContentElement(currentSub),
      })
      styleRegionHandles(r)
      return
    }

    // 自动吸附：防止与前/后 region 部分重叠
    if (prevRegion && newStartTime < prevRegion.end) {
      adjusted = true
      newStartTime = prevRegion.end
      if (newStartTime >= newEndTime) {
        newEndTime = newStartTime + 0.1
      }
    }
    if (nextRegion && newEndTime > nextRegion.start) {
      adjusted = true
      newEndTime = nextRegion.start
      if (newEndTime <= newStartTime) {
        newStartTime = newEndTime - 0.1
      }
    }

    if (adjusted) {
      r.setOptions({ start: newStartTime, end: newEndTime })
    }

    // 格式化时间
    const newStartTimeFormatted = formatTimeToString(newStartTime)
    const newEndTimeFormatted = formatTimeToString(newEndTime)

    // 刷新 region 内容
    r.setOptions({
      content: getRegionContentElement({
        ...currentSub,
        startTime: newStartTimeFormatted,
        endTime: newEndTimeFormatted,
      }),
    })
    styleRegionHandles(r)

    // 更新字幕 store
    if (currentSub.startTime !== newStartTimeFormatted || currentSub.endTime !== newEndTimeFormatted) {
      subtitleStore.updateSubtitleStartTime(r.id, newStartTimeFormatted)
      subtitleStore.updateSubtitleEndTime(r.id, newEndTimeFormatted)
    }

    // 如果当前字幕正在循环播放，更新循环时间
    if (playerStore.loopingSubtitleUuid === currentSub.uuid) {
      playerStore.updateLoopTime(newStartTime, newEndTime)
    }
  }

  /**
   * region-clicked 事件监听器：点击 region 时跳转到对应媒体时间。
   * @param {unknown} region - 被点击的 Region 实例。
   * @param {MouseEvent} e - 鼠标事件对象。
   */
  const regionClickedListener = (region: unknown, e: MouseEvent) => {
    const r = region as { start: number }
    e.stopPropagation()
    playerStore.setSeekRequest(r.start)
  }

  /**
   * 注册 region 相关事件（region-updated、region-clicked）。
   */
  function registerRegionEvents() {
    const plugin = regionsPluginInstance as { on: (...args: unknown[]) => unknown } | undefined
    if (plugin) {
      plugin.on('region-updated', handleRegionUpdate)
      plugin.on('region-clicked', regionClickedListener)
    }
  }

  /**
   * 注销 region 相关事件（region-updated、region-clicked）。
   */
  function unregisterRegionEvents() {
    const plugin = regionsPluginInstance as { un: (...args: unknown[]) => unknown } | undefined
    if (plugin) {
      plugin.un('region-updated', handleRegionUpdate)
      plugin.un('region-clicked', regionClickedListener)
    }
  }

  /**
   * 清空所有现有区域
   * @param {unknown} plugin - regions 插件实例
   */
  function clearAllRegions(plugin: { clearRegions: (...args: unknown[]) => unknown; getRegions?: () => Array<{ remove: () => void }> }) {
    // 先逐个移除所有region
    if (typeof plugin.getRegions === 'function') {
      try {
        const regions = plugin.getRegions() as Array<{ remove: () => void }>
        forEach(regions, (region) => {
          if (region && typeof region.remove === 'function') {
            region.remove()
          }
        })
      } catch (e) {
        console.log('%c AT 🥝 e 🥝-243', 'font-size:13px; background:#b58127; color:#f9c56b;', e)
      }
    }
    plugin.clearRegions()
    subtitleRegionMap.value.clear()
  }

  /**
   * 同步字幕数据到 regions，自动清空并重建所有 region。
   * @param {unknown} wavesurfer - WaveSurfer 实例。
   * @param {string} status - WaveSurfer 状态。
   */
  function syncRegions(wavesurfer: unknown, status: string) {
    const ws = wavesurfer as { [key: string]: unknown } | undefined
    if (!ws || status !== 'ready') return
    const plugin = regionsPluginInstance as
      | {
          clearRegions: (...args: unknown[]) => unknown
          addRegion: (...args: unknown[]) => unknown
          getRegions?: () => Array<{ remove: () => void }>
        }
      | undefined
    if (!plugin) return

    // 清空所有区域
    clearAllRegions(plugin)

    forEach(subtitleStore.subtitles, (sub) => {
      const start = parseSrtTime(sub.startTime)
      const end = parseSrtTime(sub.endTime)
      if (start === null || end === null || start >= end) return
      try {
        const region = plugin.addRegion({
          id: sub.uuid,
          start,
          end,
          content: getRegionContentElement(sub),
          color: REGION_COLOR,
          drag: true,
          resize: true,
          minLength: 0.1,
        })
        subtitleRegionMap.value.set(sub.uuid, region as unknown)
        styleRegionHandles(region)
      } catch (e) {
        // 可根据需要添加错误处理
      }
    })
  }

  return {
    subtitleRegionMap,
    registerRegionEvents,
    unregisterRegionEvents,
    syncRegions,
  }
}

import WaveSurfer from 'wavesurfer.js'
import TimelinePlugin from 'wavesurfer.js/dist/plugins/timeline.esm.js'
import HoverPlugin from 'wavesurfer.js/dist/plugins/hover.esm.js'
import RegionsPlugin, { type Region } from 'wavesurfer.js/dist/plugins/regions.esm.js'
import { formatTimeToString } from '~/utils/processing/time/timeFormatter'
import { useWaveformPlayerSync } from '~/composables/audio/useWaveformPlayerSync'
import { usePlayerStore } from '~/stores/playerStore'

interface WaveSurferOptions {
  container: HTMLElement
  timelineContainer?: HTMLElement
  onSeek?: (time: number) => void
  onError?: (error: Error) => void
  onReady?: () => void
  onDurationAvailable?: (duration: number) => void
  playbackRate?: number
}

type WaveSurferStatus = 'idle' | 'initializing' | 'loading' | 'ready' | 'error'

export function useWaveSurfer() {
  const wavesurfer = ref<WaveSurfer | null>(null)
  const status = ref<WaveSurferStatus>('idle')
  const error = ref<Error | null>(null)
  const playerStore = usePlayerStore()
  const loopCheckInterval = ref<number | null>(null)

  // 修补了 RegionPlug 以避免重叠区域的错误
  const regionsPluginInstance = RegionsPlugin.create()

  // @ts-expect-error 覆盖 avoidOverlapping 方法，此方法为 RegionPlugin 中的私有方法
  regionsPluginInstance.avoidOverlapping = (_region: Region) => {
    // 什么都不做
  }

  // 处理循环播放逻辑
  const checkLoopPlayback = (currentTime?: number) => {
    if (!wavesurfer.value || !playerStore.isLooping || playerStore.loopEndTime === null) {
      return
    }
    // 优先使用 audioprocess 事件传入的 currentTime，兼容原有定时器调用
    const time = typeof currentTime === 'number' ? currentTime : wavesurfer.value.getCurrentTime()
    if (time >= playerStore.loopEndTime) {
      // 如果到达循环结束时间，跳回循环开始时间
      if (playerStore.loopStartTime !== null) {
        wavesurfer.value.setTime(playerStore.loopStartTime)
        playerStore.setSeekRequest(playerStore.loopStartTime)
      }
    }
  }

  // 开始循环检查
  const startLoopCheck = () => {
    stopLoopCheck() // 先停止之前的检查
    // 缩短检测间隔为 30ms，提高精度
    loopCheckInterval.value = window.setInterval(() => checkLoopPlayback(), 30)
  }

  // 停止循环检查
  const stopLoopCheck = () => {
    if (loopCheckInterval.value !== null) {
      clearInterval(loopCheckInterval.value)
      loopCheckInterval.value = null
    }
  }

  const initialize = async (options: WaveSurferOptions) => {
    console.log('正在使用容器初始化 WaveSurfer:', options.container)
    status.value = 'initializing'
    console.log('WaveSurfer 状态更改为: 正在初始化 (在 initialize() 中)')
    error.value = null

    try {
      // 销毁旧实例
      if (wavesurfer.value) {
        wavesurfer.value.destroy()
        wavesurfer.value = null
      }

      // 创建新实例
      console.log('尝试创建 WaveSurfer 实例...')
      wavesurfer.value = WaveSurfer.create({
        container: options.container, // 波形图的 HTML 容器元素
        width: '100%', // 波形图的宽度，'100%' 表示占满容器
        waveColor: '#6366f1', // 未播放部分的波形颜色（蓝紫色）
        progressColor: '#a78bfa', // 已播放部分的波形颜色（浅紫色）
        cursorColor: '#f472b6', // 播放指示光标的颜色（粉紫色）
        normalize: true, // 是否对音频数据进行标准化处理 (使波形高度更一致)
        minPxPerSec: 100, // 每秒音频至少显示的像素宽度 (控制缩放级别)
        fillParent: true, // 波形图是否填充其父容器的高度
        backend: 'MediaElement', // 使用的音频处理后端 ('MediaElement' 使用 HTML5 Audio/Video 元素)
        hideScrollbar: false, // 当波形宽度超过容器时是否隐藏滚动条

        height: 'auto', // 波形图的高度 (像素)
        autoCenter: true, // 播放时是否自动将当前播放位置居中显示
        interact: true, // 是否允许用户通过点击或拖动与波形图交互
        plugins: [
          options.timelineContainer &&
            TimelinePlugin.create({
              timeInterval: 0.1, // 时间轴刻度标记的最小间隔 (秒)
              primaryLabelInterval: 1, // 主要时间标签的间隔 (秒)
              style: {
                fontSize: '12px', // 时间轴标签的字体大小
              },
            }),
          HoverPlugin.create({
            lineColor: '#a78bfa', // 鼠标悬停时指示线的颜色（与主色调协调的蓝紫色）
            lineWidth: 2, // 鼠标悬停时指示线的宽度
            labelBackground: '#555', // 鼠标悬停时时间标签的背景色
            labelColor: '#fff', // 鼠标悬停时时间标签的文字颜色
            labelSize: '12px', // 鼠标悬停时时间标签的文字大小
            formatTimeCallback: formatTimeToString,
          }),
          regionsPluginInstance,
        ].filter(Boolean) as unknown as never[],
      })

      wavesurfer.value.setVolume(0)

      // 设置初始播放速度
      if (options.playbackRate) {
        wavesurfer.value.setPlaybackRate(options.playbackRate)
      }

      // 在成功创建实例后调用 useWaveformPlayerSync
      useWaveformPlayerSync(wavesurfer as import('vue').Ref<WaveSurfer | null>, status, play, pause, setPlaybackRate)

      // 事件监听
      wavesurfer.value.on('ready', () => {
        console.log('WaveSurfer 发出 "ready" 事件。')
        status.value = 'ready'
        console.log('WaveSurfer 状态更改为: 就绪 (通过 "ready" 事件)')
        const duration = wavesurfer.value?.getDuration() || 0
        options.onDurationAvailable?.(duration)
        options.onReady?.()
      })

      wavesurfer.value.on('error', (err: Error) => {
        console.error('WaveSurfer 发出 "error" 事件:', err)
        error.value = err
        status.value = 'error'
        console.log('WaveSurfer 状态更改为: 错误 (通过 "error" 事件)')
        options.onError?.(err)
      })

      // 播放结束事件监听，自动复位到开始
      wavesurfer.value.on('finish', () => {
        playerStore.updatePauseStatus()
      })

      // 监听播放状态变化
      wavesurfer.value.on('play', () => {
        if (playerStore.isLooping) {
          startLoopCheck()
        }
      })

      wavesurfer.value.on('pause', () => {
        stopLoopCheck()
      })

      // 只用于更新时间和循环检测
      wavesurfer.value.on('audioprocess', (currentTime) => {
        options.onSeek?.(currentTime)
        if (playerStore.isLooping && playerStore.loopEndTime !== null) {
          checkLoopPlayback(currentTime)
        }
      })

      // 监听用户交互事件
      wavesurfer.value.on('interaction', (time) => {
        playerStore.setSeekRequest(time)
      })

      // 监听循环状态变化
      watch(
        () => playerStore.isLooping,
        (newIsLooping) => {
          if (newIsLooping && wavesurfer.value?.isPlaying()) {
            startLoopCheck()
          } else {
            stopLoopCheck()
          }
        },
      )
    } catch (err) {
      console.error('WaveSurfer 初始化期间出错:', err)
      error.value = err as Error
      status.value = 'error'
      console.log('WaveSurfer 状态更改为: 错误 (在 initialize() 的 catch 块中)')
      options.onError?.(err as Error)
    }
  }

  const loadAudio = async (file: File) => {
    console.log('正在加载媒体文件:', file ? { name: file.name, size: file.size, type: file.type } : '空/未定义的文件')

    if (!wavesurfer.value) {
      const err = new Error('WaveSurfer not initialized before loadAudio')
      error.value = err
      status.value = 'error'
      console.log('WaveSurfer 状态更改为: 错误 (在 loadAudio() 中缺少实例)')
      throw err
    }

    try {
      status.value = 'loading'
      console.log('WaveSurfer 状态更改为: 正在加载 (在 loadAudio() 中)')
      error.value = null
      const url = URL.createObjectURL(file)
      console.log('尝试使用 Blob URL 调用 wavesurfer.load():', url)

      await wavesurfer.value.load(url)
      console.log('wavesurfer.load() 似乎已成功。')

      URL.revokeObjectURL(url)
    } catch (err) {
      console.error('loadAudio 期间出错:', err)
      error.value = err as Error
      status.value = 'error'
      console.log('WaveSurfer 状态更改为: 错误 (在 loadAudio() 的 catch 块中)')
      throw err
    }
  }

  const destroy = () => {
    if (wavesurfer.value) {
      wavesurfer.value.destroy()
      wavesurfer.value = null
    }
    status.value = 'idle'
    console.log('WaveSurfer 状态更改为: 空闲 (在 destroy() 中)')
    error.value = null
  }

  const play = () => {
    if (wavesurfer.value && status.value === 'ready') {
      wavesurfer.value.play()
      playerStore.updatePlayIngStatus()
    } else {
      console.warn(`无法播放: WaveSurfer 状态为 ${status.value}`)
    }
  }

  const pause = () => {
    if (wavesurfer.value && status.value === 'ready') {
      wavesurfer.value.pause()
      playerStore.updatePauseStatus()
    } else {
      console.warn(`无法暂停: WaveSurfer 状态为 ${status.value}`)
    }
  }

  const togglePlay = () => {
    if (wavesurfer.value && status.value === 'ready') {
      wavesurfer.value.playPause()
    } else {
      console.warn(`无法切换播放/暂停: WaveSurfer 状态为 ${status.value}`)
    }
  }

  const setPlaybackRate = (rate: number) => {
    if (wavesurfer.value && status.value === 'ready') {
      wavesurfer.value.setPlaybackRate(rate)
    }
  }

  onBeforeUnmount(() => {
    stopLoopCheck()
    destroy()
  })

  return {
    wavesurfer,
    status,
    error,
    initialize,
    loadAudio,
    destroy,
    togglePlay,
    play,
    pause,
    setPlaybackRate,
    regionsPluginInstance,
  }
}

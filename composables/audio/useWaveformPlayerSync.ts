import type WaveSurfer from 'wavesurfer.js'
import { usePlayerStore } from '~/stores/playerStore'

/**
 * 用于同步 WaveSurfer 实例与 playerStore 状态的 composable。
 * @param {unknown} wavesurfer - WaveSurfer 实例。
 * @param {import('vue').Ref<string>} status - WaveSurfer 状态。
 * @param {Function} play - WaveSurfer 播放方法。
 * @param {Function} pause - WaveSurfer 暂停方法。
 * @param {Function} setPlaybackRate - WaveSurfer 设置播放速率方法。
 */
export function useWaveformPlayerSync(
  wavesurfer: Ref<WaveSurfer | null>,
  status: { value: string },
  play: () => void,
  pause: () => void,
  setPlaybackRate: (rate: number) => void,
) {
  const playerStore = usePlayerStore()

  /**
   * 监听 playerStore.isPlaying，同步控制 WaveSurfer 播放/暂停。
   */
  watch(
    () => playerStore.isPlaying,
    (newIsPlaying) => {
      if (status.value !== 'ready') return
      if (newIsPlaying) {
        play()
      } else {
        pause()
      }
    },
  )

  /**
   * 监听 playerStore.playbackRate，同步设置 WaveSurfer 播放速率。
   */
  watch(
    () => playerStore.playbackRate,
    (newRate) => {
      if (status.value === 'ready' && wavesurfer.value) {
        setPlaybackRate(newRate)
      }
    },
  )

  /**
   * 监听 playerStore.seekRequest，主动同步到 WaveSurfer。
   * 目的是为了当点击进度条的时候同步 wavesurfer 的进度，但是这个实现不好，因为当点击进度条的时候，会触发两次，导致进度条闪烁
   * TODO: 在点击进度条的地方，更新进度条，然后更新 wavesurfer 的进度
   */
  watch(
    () => playerStore.seekRequest,
    (newReq, oldReq) => {
      if (
        status.value === 'ready' &&
        wavesurfer.value &&
        typeof wavesurfer.value.setTime === 'function' &&
        newReq &&
        (!oldReq || newReq.key !== oldReq.key)
      ) {
        wavesurfer.value.setTime(newReq.time)
      }
    },
  )
}

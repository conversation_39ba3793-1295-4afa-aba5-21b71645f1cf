import { useRafFn } from '@vueuse/core'
import type { useSubtitleStore } from '~/stores/subtitleStore'
import { findIndex } from 'lodash-es'

/**
 * 用于管理字幕合并高亮框的组合式函数
 * @param store - subtitleStore 实例，需包含 subtitles 响应式数组
 * @param scrollContainerRef - 可选的滚动容器 ref，用于修正高亮框在滚动时的错位问题
 * @returns 高亮框样式、显示状态、鼠标悬停/离开处理函数
 */
export function useMergeHighlight(store: ReturnType<typeof useSubtitleStore>, scrollContainerRef?: Ref<HTMLElement | null>) {
  // 当前悬停的合并候选字幕项的 uuid
  const hoveredMergeCandidateUuid = ref<string | null>(null)
  // 高亮框的样式对象（top/left/width/height）
  const highlightBoxStyle = ref<Record<string, string>>({})
  // 是否显示高亮框
  const showHighlightBox = ref(false)

  // 包裹 updateHighlightBox，节流到每帧只执行一次
  const { resume: runUpdateHighlightBox } = useRafFn(updateHighlightBox, { immediate: false })

  /**
   * 鼠标悬停在合并按钮时调用，设置当前候选 uuid 并更新高亮框
   * @param uuid - 当前悬停字幕项的 uuid
   */
  function handleMergeButtonHover(uuid: string) {
    hoveredMergeCandidateUuid.value = uuid
    nextTick(() => {
      runUpdateHighlightBox()
    })
  }

  /**
   * 鼠标离开合并按钮时调用，隐藏高亮框
   */
  function handleMergeButtonLeave() {
    hoveredMergeCandidateUuid.value = null
    showHighlightBox.value = false
  }

  /**
   * 根据当前悬停的字幕项，计算并设置高亮框的样式
   * 包括当前项和下一项的整体区域
   */
  function updateHighlightBox() {
    if (!hoveredMergeCandidateUuid.value) {
      showHighlightBox.value = false
      return
    }
    // 查找当前悬停字幕项的索引
    const currentIndex = findIndex(store.subtitles, (sub) => sub.uuid === hoveredMergeCandidateUuid.value)
    if (currentIndex === -1) {
      showHighlightBox.value = false
      return
    }
    // 获取当前字幕项的 DOM 元素
    const currentId = `subtitle-item-${store.subtitles[currentIndex].uuid}`
    const currentEl = document.getElementById(currentId)
    if (!currentEl) {
      showHighlightBox.value = false
      return
    }
    // 获取滚动容器的 scrollTop/scrollLeft
    let scrollTop = 0
    let scrollLeft = 0
    if (scrollContainerRef && scrollContainerRef.value) {
      scrollTop = scrollContainerRef.value.scrollTop
      scrollLeft = scrollContainerRef.value.scrollLeft
    }
    const top = currentEl.offsetTop - scrollTop
    const left = currentEl.offsetLeft - scrollLeft
    const width = currentEl.offsetWidth
    let height = currentEl.offsetHeight
    // 检查是否有下一项，若有则合并高亮区域
    if (currentIndex + 1 < store.subtitles.length) {
      const nextId = `subtitle-item-${store.subtitles[currentIndex + 1].uuid}`
      const nextEl = document.getElementById(nextId)
      if (nextEl) {
        // 计算当前项与下一项之间的间距
        const gap = nextEl.offsetTop - (currentEl.offsetTop + currentEl.offsetHeight)
        height += gap > 0 ? gap : 0
        height += nextEl.offsetHeight
      }
    }
    // 设置高亮框样式
    highlightBoxStyle.value = {
      top: top + 'px',
      left: left + 'px',
      width: width + 'px',
      height: height + 'px',
    }
    showHighlightBox.value = true
  }

  // 当字幕数据变化时，自动隐藏高亮框并重置悬停状态
  watch(
    () => store.subtitles.length,
    () => {
      showHighlightBox.value = false
      hoveredMergeCandidateUuid.value = null
    },
  )

  // 返回高亮框样式、显示状态及事件处理函数
  return {
    highlightBoxStyle,
    showHighlightBox,
    handleMergeButtonHover,
    handleMergeButtonLeave,
  }
}

import {
  SUPPORTED_AUDIO_FORMATS,
  SUPPORTED_MIME_TYPES,
  EXTENSION_TO_MIME_MAP,
  MIME_TYPE_NORMALIZATION_MAP,
  MAX_FILE_SIZE,
} from '~/common/constants'

export function useFileUpload() {
  // 响应式状态
  const uploadedFile = ref<File | null>(null)
  const fileInputRef = ref<HTMLInputElement>()
  const errorMessage = ref('')

  // 文件操作方法
  const triggerFileInput = () => {
    fileInputRef.value?.click()
  }

  const handleFileUpload = (event: Event) => {
    const target = event.target as HTMLInputElement
    const file = target.files?.[0]
    if (file) {
      // 验证文件大小（150MB限制）
      if (file.size > MAX_FILE_SIZE) {
        errorMessage.value = '文件大小超过150MB限制'
        return
      }

      // 获取文件扩展名
      const extension = file.name.split('.').pop()?.toLowerCase()

      if (!extension || !SUPPORTED_AUDIO_FORMATS.includes(extension)) {
        errorMessage.value = '不支持的音频格式，请使用 WAV、MP3、MP4、M4A 或 OGG 格式'
        return
      }

      if (file.type && !SUPPORTED_MIME_TYPES.includes(file.type)) {
        errorMessage.value = `检测到可能不兼容的音频格式: ${file.type}，建议使用 WAV 格式以获得最佳兼容性`
        // 不直接返回，允许用户继续尝试
      }

      uploadedFile.value = file
      errorMessage.value = ''

      // 显示格式建议
      if (extension === 'mp3') {
        console.warn('提示：如果遇到识别问题，建议转换为 WAV 格式')
      }

      // 获取文件的MIME类型，如果无法确定则根据扩展名设置
      let contentType = file.type
      if (!contentType) {
        const extension = file.name.split('.').pop()?.toLowerCase()
        contentType = EXTENSION_TO_MIME_MAP[extension || ''] || 'audio/wav'
      }

      // 标准化Content-Type，确保使用标准MIME类型
      contentType = MIME_TYPE_NORMALIZATION_MAP[contentType] || contentType

      console.log('📁 File info:', {
        name: file.name,
        size: file.size,
        type: file.type,
        finalContentType: contentType,
      })
    }
  }

  const removeFile = () => {
    uploadedFile.value = null
    errorMessage.value = ''
    if (fileInputRef.value) {
      fileInputRef.value.value = ''
    }
  }

  const formatFileSize = (bytes: number): string => {
    if (bytes === 0) return '0 Bytes'
    const k = 1024
    const sizes = ['Bytes', 'KB', 'MB', 'GB']
    const i = Math.floor(Math.log(bytes) / Math.log(k))
    return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i]
  }

  return {
    // 响应式状态
    uploadedFile,
    fileInputRef,
    errorMessage,

    // 方法
    triggerFileInput,
    handleFileUpload,
    removeFile,
    formatFileSize,
  }
}

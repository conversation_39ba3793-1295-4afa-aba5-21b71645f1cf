import { filter, map } from 'lodash-es'
import { useLrcStore } from '~/stores/lrcStore'
import { usePlayerStore } from '~/stores/playerStore'
import { useSubtitleStore } from '~/stores/subtitleStore'
import { type Statement } from '~/types/subtitle'
import type { LrcData } from '~/utils/formats/parsers/lrcParser'
import { lrcToSrt, subtitleToLrc } from '~/utils/formats/converters/subtitleConverter'
import { parseSrt } from '~/utils/formats/parsers/srtParser'
import { parseVtt } from '~/utils/formats/parsers/vttParser'
import { parseSrtTime } from '~/utils/processing/time/timeParser'
import { AUDIO_MIME_PREFIX, VIDEO_MIME_PREFIX } from '~/common/constants'

/**
 * TopBar 逻辑抽离
 */
export function useTopBarLogic() {
  const lrcStore = useLrcStore()
  const playerStore = usePlayerStore()
  const subtitleStore = useSubtitleStore()

  // 音频文件上传 input
  const audioFileInputRef = ref<HTMLInputElement | null>(null)
  // 在线歌词弹窗
  const isOnlineLrcModalOpen = ref(false)
  // vtt 文件上传 input
  const subtitleFileInputRef = ref<HTMLInputElement | null>(null)

  // 判断是否有有效的内容变更
  const hasValidChanges = computed(() => {
    // 检查是否有媒体文件
    if (!playerStore.mediaFile) return false

    const { type } = playerStore.mediaFile
    // 检查媒体文件格式
    if (!type.startsWith(AUDIO_MIME_PREFIX) && !type.startsWith(VIDEO_MIME_PREFIX)) return false

    // 检查是否有LRC内容
    if (!lrcStore.hasLrc) return false

    // 检查字幕内容是否有效
    const hasValidSubtitles = subtitleStore.subtitles.some(
      (subtitle) => subtitle.text.trim() !== '' || subtitle.translationText.trim() !== '',
    )

    if (!hasValidSubtitles) return false

    return true
  })

  // 触发音频文件上传
  const triggerAudioFileInput = () => {
    audioFileInputRef.value?.click()
  }
  // 触发合并字幕文件上传
  const triggerSubtitleFileInput = () => {
    subtitleFileInputRef.value?.click()
  }

  // 合并字幕文件上传处理
  const handleSubtitleFileUpload = async (event: Event) => {
    const file = (event.target as HTMLInputElement).files?.[0]
    if (!file) return
    const ext = file.name.split('.').pop()?.toLowerCase()
    if (ext === 'lrc') {
      await lrcStore.uploadLrc(file)
      const srtSubtitles = lrcToSrt(lrcStore.lrcLines)
      subtitleStore.setSubtitlesFromLrc(srtSubtitles)
    } else if (ext === 'srt') {
      const content = await file.text()
      const srtSubtitles = parseSrt(content)
      subtitleStore.setSubtitlesFromLrc(srtSubtitles)
      const lrcContent = subtitleToLrc(srtSubtitles)
      lrcStore.updateLrcContent('', lrcContent)
    } else if (ext === 'vtt') {
      const content = await file.text()
      const vttSubtitles = parseVtt(content)
      subtitleStore.setSubtitlesFromLrc(vttSubtitles)
      const lrcContent = subtitleToLrc(vttSubtitles)
      lrcStore.updateLrcContent('', lrcContent)
    }
    // 清空 input 以便连续上传同一文件
    ;(event.target as HTMLInputElement).value = ''
  }

  // 处理完成
  const handleFinish = () => {
    const subtitles = subtitleStore.subtitles

    // 获取音频信息
    const audioInfo = playerStore.mediaFile
    console.log('%c AT 🥝 audioInfo 🥝-56', 'font-size:13px; background:#124a7a; color:#568ebe;', audioInfo)

    // 过滤掉没有歌词的 srt
    const filteredSubtitles = filter(subtitles, (subtitle) => subtitle.text !== '')

    // 将 srt 的 statements 转换为 music 的 statements
    const statements: Statement[] = map(filteredSubtitles, (subtitle) => ({
      id: subtitle.uuid,
      chinese: subtitle.translationText,
      english: subtitle.text,
      start: Number((parseSrtTime(subtitle.startTime) || 0).toFixed(3)),
      end: Number((parseSrtTime(subtitle.endTime) || 0).toFixed(3)),
    }))
    console.log('%c AT 🥝 statements 🥝-68', 'font-size:13px; background:#fb3b73; color:#ff7fb7;', statements)
    const a = JSON.stringify(statements)
    console.log('%c AT 🥝 a 🥝-105', 'font-size:13px; background:#e9bc54; color:#ffff98;', a)

    // 缓存到 localStorage
    localStorage.setItem('subtitle-player-statements', JSON.stringify(statements))
    console.log('字幕数据和音频信息已缓存，请在播放器页面重新选择音频文件')
  }

  // 处理获取在线歌词
  const handleGetLrc = () => {
    isOnlineLrcModalOpen.value = true
  }

  // 处理应用在线歌词
  const handleApplyOnlineLrc = (lrc: LrcData) => {
    lrcStore.parseLrcContent(lrc.syncedLyrics)
    const srtSubtitles = lrcToSrt(lrcStore.lrcLines)
    subtitleStore.setSubtitlesFromLrc(srtSubtitles)
  }

  // 处理音频文件上传
  const handleAudioFileUpload = async (event: Event) => {
    const file = (event.target as HTMLInputElement).files?.[0]
    if (file) {
      playerStore.setMediaFile(file)
    }
    // 清空 input 以便连续上传同一文件
    ;(event.target as HTMLInputElement).value = ''
  }

  return {
    isOnlineLrcModalOpen,
    audioFileInputRef,
    hasValidChanges,
    triggerAudioFileInput,
    handleAudioFileUpload,
    handleFinish,
    handleGetLrc,
    handleApplyOnlineLrc,
    subtitleFileInputRef,
    triggerSubtitleFileInput,
    handleSubtitleFileUpload,
  }
}

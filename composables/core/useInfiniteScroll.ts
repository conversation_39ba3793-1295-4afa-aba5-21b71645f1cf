import { ref, onUnmounted, type Ref } from 'vue'

interface UseInfiniteScrollOptions {
  rootMargin?: string
  threshold?: number
  onIntersect: () => void
}

interface UseInfiniteScrollReturn {
  sentinelRef: Ref<HTMLElement | null>
  onSetupObserver: () => void
  onCleanupObserver: () => void
}

/**
 * 通用无限滚动 composable
 * 使用 IntersectionObserver 监听目标元素，触发加载更多回调
 */
export const useInfiniteScroll = (options: UseInfiniteScrollOptions): UseInfiniteScrollReturn => {
  const { rootMargin = '100px', threshold = 0.1, onIntersect } = options

  const sentinelRef = ref<HTMLElement | null>(null)
  let observer: IntersectionObserver | null = null

  const onSetupObserver = () => {
    if (!sentinelRef.value || import.meta.server) return

    // 如果已有observer，先清理
    if (observer) {
      observer.disconnect()
    }

    observer = new IntersectionObserver(
      (entries) => {
        entries.forEach((entry) => {
          if (entry.isIntersecting) {
            onIntersect()
          }
        })
      },
      {
        rootMargin,
        threshold,
      },
    )

    observer.observe(sentinelRef.value)
  }

  const onCleanupObserver = () => {
    if (observer) {
      observer.disconnect()
      observer = null
    }
  }

  // 组件卸载时自动清理
  onUnmounted(() => {
    onCleanupObserver()
  })

  return {
    sentinelRef,
    onSetupObserver,
    onCleanupObserver,
  }
}

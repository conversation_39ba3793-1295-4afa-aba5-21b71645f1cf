import { ref, computed, watch } from 'vue'
import { usePlayerStore } from '~/stores/playerStore'
import { type Statement } from '~/types/subtitle'
import { parseSrtTime } from '~/utils/processing/time/timeParser'

/**
 * 播放器页面逻辑
 */
export function usePlayerPageLogic() {
  const playerStore = usePlayerStore()

  // 状态
  const currentIndex = ref(0)
  const statements = ref<Statement[]>([])
  const audioFile = ref<File | null>(null)
  const isLoading = ref(false)
  const error = ref<string | null>(null)

  // 计算属性
  const currentStatement = computed(() => statements.value[currentIndex.value] || null)
  const isFirstStatement = computed(() => currentIndex.value === 0)
  const isLastStatement = computed(() => currentIndex.value === statements.value.length - 1)
  const hasData = computed(() => statements.value.length > 0)

  // 从 localStorage 加载数据
  const loadFromCache = () => {
    try {
      isLoading.value = true
      error.value = null

      // 加载 statements
      const cachedStatements = localStorage.getItem('subtitle-player-statements')
      if (cachedStatements) {
        statements.value = JSON.parse(cachedStatements)
      } else {
        error.value = '未找到字幕数据，请先在主页面点击完成按钮'
        return
      }
    } catch (err) {
      error.value = '加载缓存数据失败'
      console.error('加载缓存数据失败:', err)
    } finally {
      isLoading.value = false
    }
  }

  // 下一句
  const goToNext = () => {
    if (!isLastStatement.value) {
      currentIndex.value++
      // 自动播放新句子
      if (currentStatement.value) {
        playStatement(currentStatement.value)
      }
    }
  }

  // 上一句
  const goToPrevious = () => {
    if (!isFirstStatement.value) {
      currentIndex.value--
      // 自动播放新句子
      if (currentStatement.value) {
        playStatement(currentStatement.value)
      }
    }
  }

  // 重来
  const goToFirst = () => {
    currentIndex.value = 0
    // 自动播放第一句
    if (currentStatement.value) {
      playStatement(currentStatement.value)
    }
  }

  // 当前播放句子的结束时间
  const currentEndTime = ref<number | null>(null)

  // 播放指定句子
  const playStatement = (statement: Statement) => {
    const startTime = parseSrtTime(statement.start)
    const endTime = parseSrtTime(statement.end)

    if (startTime !== null && endTime !== null) {
      // 停止之前的循环播放
      playerStore.stopLoop()

      // 记录当前句子的结束时间
      currentEndTime.value = endTime

      // 跳转到开始时间并播放
      playerStore.setSeekRequest(startTime)
      playerStore.updatePlayIngStatus()
    }
  }

  // 重播当前句子
  const replayCurrent = () => {
    if (currentStatement.value) {
      playStatement(currentStatement.value)
    }
  }

  // 处理音频文件上传
  const handleAudioUpload = (file: File) => {
    try {
      playerStore.setMediaFile(file)
      console.log('音频文件已加载:', file.name)
    } catch (err) {
      error.value = '音频文件加载失败'
      console.error('音频文件加载失败:', err)
    }
  }

  // 监听播放时间，当到达句子结束时间时自动暂停
  watch(
    () => playerStore.currentTime,
    (currentTime) => {
      if (currentEndTime.value !== null && currentTime >= currentEndTime.value) {
        // 到达句子结束时间，暂停播放
        playerStore.updatePauseStatus()
        currentEndTime.value = null
      }
    },
  )

  return {
    // 状态
    currentIndex,
    statements,
    audioFile,
    isLoading,
    error,
    currentEndTime,

    // 计算属性
    currentStatement,
    isFirstStatement,
    isLastStatement,
    hasData,

    // 方法
    loadFromCache,
    goToNext,
    goToPrevious,
    goToFirst,
    replayCurrent,
    playStatement,
    handleAudioUpload,
  }
}

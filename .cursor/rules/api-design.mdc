---
description: 
globs: 
alwaysApply: false
---
# API 设计规则

该规则定义基于 tRPC 的 API 设计模式和服务端架构规范。

## 设计原则

- **类型安全**: 端到端的 TypeScript 类型安全
- **契约优先**: 基于 Zod schema 的接口定义
- **RESTful 理念**: 遵循资源导向的 API 设计
- **性能优先**: 支持流式响应和批量操作

## tRPC 架构

### 服务端结构
```
server/
├── trpc/
│   ├── routers/           # 路由定义
│   ├── context.ts         # 上下文配置
│   ├── index.ts           # 主路由聚合
│   └── middleware/        # 中间件
└── api/
    └── trpc/[trpc].ts     # Nuxt API 处理器
```

### 客户端集成
```typescript
// plugins/trpc.client.ts
import { createTRPCNuxtClient } from 'trpc-nuxt/client'

export default createTRPCNuxtClient({
  baseURL: '/api/trpc',
  httpBatchStreamTimeout: 100,
})
```

## API 路由设计

### 路由组织
- 按业务领域分组路由 (subtitle, audio, ai)
- 嵌套路由支持复杂的资源关系
- 统一的命名约定和版本管理

### 路由命名规范
```typescript
// 推荐的路由命名
export const appRouter = router({
  subtitle: subtitleRouter,     // 字幕相关 API
  audio: audioRouter,          // 音频处理 API
  ai: aiRouter,               // AI 服务 API
  user: userRouter,           // 用户管理 API
})
```

## 数据验证

### Zod Schema 设计
```typescript
// 输入验证
const createSubtitleInput = z.object({
  content: z.string().min(1).max(1000),
  startTime: z.number().positive(),
  endTime: z.number().positive(),
  language: z.enum(['zh', 'en', 'ja']).optional(),
})

// 输出类型
const subtitleOutput = z.object({
  id: z.string(),
  content: z.string(),
  startTime: z.number(),
  endTime: z.number(),
  createdAt: z.date(),
})
```

### 验证策略
- 输入数据的严格验证
- 输出数据的类型保证
- 错误信息的国际化支持

## 过程类型定义

### Query (查询)
```typescript
// 用于数据获取，无副作用
export const subtitleRouter = router({
  list: publicProcedure
    .input(z.object({
      page: z.number().default(1),
      limit: z.number().max(100).default(20),
    }))
    .query(({ input }) => {
      // 查询逻辑
    }),
})
```

### Mutation (变更)
```typescript
// 用于数据修改，有副作用
export const subtitleRouter = router({
  create: publicProcedure
    .input(createSubtitleInput)
    .mutation(({ input }) => {
      // 创建逻辑
    }),
})
```

### Subscription (订阅)
```typescript
// 用于实时数据流
export const subtitleRouter = router({
  watch: publicProcedure
    .input(z.object({ subtitleId: z.string() }))
    .subscription(({ input }) => {
      // 订阅逻辑
    }),
})
```

## 错误处理

### 错误类型定义
```typescript
import { TRPCError } from '@trpc/server'

// 标准化错误响应
throw new TRPCError({
  code: 'BAD_REQUEST',
  message: '请求参数无效',
  cause: validationError,
})
```

### 错误处理策略
- 客户端错误 (4xx): 输入验证、权限检查
- 服务端错误 (5xx): 系统异常、外部服务故障
- 业务错误: 自定义错误码和消息

## 性能优化

### 批量操作
```typescript
// 支持批量请求
export const subtitleRouter = router({
  batchCreate: publicProcedure
    .input(z.array(createSubtitleInput))
    .mutation(({ input }) => {
      // 批量创建逻辑
    }),
})
```

### 流式响应
```typescript
// 大数据的流式传输
export const aiRouter = router({
  generateStream: publicProcedure
    .input(generateInput)
    .subscription(async function* ({ input }) {
      // 流式生成逻辑
      for await (const chunk of generateChunks(input)) {
        yield chunk
      }
    }),
})
```

### 缓存策略
- HTTP 缓存头的合理设置
- 查询结果的内存缓存
- 分布式缓存的集成

## 认证与授权

### 上下文注入
```typescript
export const createTRPCContext = async ({ req, res }) => {
  const user = await getUserFromToken(req)
  
  return {
    user,
    db: prisma,
    // 其他上下文
  }
}
```

### 中间件保护
```typescript
const protectedProcedure = publicProcedure
  .use(async ({ ctx, next }) => {
    if (!ctx.user) {
      throw new TRPCError({ code: 'UNAUTHORIZED' })
    }
    return next({ ctx: { ...ctx, user: ctx.user } })
  })
```

## 开发规范

遵循 [全局技术规范](mdc:.cursor/rules/global-standards.mdc) 中的开发要求。

## 相关依赖

- 与 [types](mdc:types) 目录中的类型定义保持同步
- 集成 [utils](mdc:utils) 中的工具函数
- 遵循 [architecture](mdc:.cursor/rules/architecture.mdc) 定义的架构模式

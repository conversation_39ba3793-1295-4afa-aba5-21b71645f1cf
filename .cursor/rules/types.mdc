---
description: 
globs: 
alwaysApply: false
---
# types 目录规则

该目录用于存放项目的 TypeScript 类型定义，确保类型安全和代码可维护性。

## 设计原则

- **类型优先**: 先定义类型，再实现功能
- **严格模式**: 启用 TypeScript 严格模式检查
- **可复用性**: 类型定义可在多个模块间共享
- **向前兼容**: 类型变更考虑向前兼容性

## 类型组织

### 目录结构
```
types/
├── subtitle.ts           # 字幕相关类型
├── lrcTypes.ts          # LRC 歌词类型
├── audio.ts             # 音频处理类型
├── api.ts               # API 响应类型
├── user.ts              # 用户相关类型
└── common.ts            # 通用类型定义
```

## 关键类型定义

### 字幕类型
- [subtitle.ts](mdc:types/subtitle.ts): 定义字幕数据结构、编辑状态、验证规则等核心类型
- 支持多种字幕格式 (SRT, VTT, ASS)
- 包含时间轴、样式、元数据等属性

### 歌词类型
- [lrcTypes.ts](mdc:types/lrcTypes.ts): 定义 LRC 歌词格式的数据结构
- 支持增强 LRC 功能 (词级同步、翻译等)
- 兼容标准和扩展 LRC 格式

## 类型设计模式

### 基础类型模式
```typescript
// 基础实体类型
interface BaseEntity {
  id: string
  createdAt: Date
  updatedAt: Date
}

// 扩展实体类型
interface Subtitle extends BaseEntity {
  content: string
  startTime: number
  endTime: number
  language?: string
}
```

### 联合类型模式
```typescript
// 状态联合类型
type SubtitleStatus = 'draft' | 'editing' | 'published' | 'archived'

// 操作类型
type SubtitleAction = 
  | { type: 'CREATE'; payload: CreateSubtitleData }
  | { type: 'UPDATE'; payload: UpdateSubtitleData }
  | { type: 'DELETE'; payload: { id: string } }
```

### 泛型类型模式
```typescript
// API 响应泛型
interface ApiResponse<T> {
  data: T
  message: string
  success: boolean
  timestamp: Date
}

// 分页泛型
interface PaginatedResult<T> {
  items: T[]
  total: number
  page: number
  limit: number
}
```

### 条件类型模式
```typescript
// 根据条件选择类型
type EditableFields<T> = {
  readonly [K in keyof T]: T[K] extends Function ? never : T[K]
}

// 可选字段转换
type PartialUpdate<T> = Partial<Pick<T, keyof T>>
```

## 类型安全策略

### 严格类型检查
```typescript
// 启用严格模式
// tsconfig.json
{
  "compilerOptions": {
    "strict": true,
    "noImplicitAny": true,
    "strictNullChecks": true,
    "strictFunctionTypes": true
  }
}
```

### 类型保护
```typescript
// 类型保护函数
function isSubtitle(obj: unknown): obj is Subtitle {
  return (
    typeof obj === 'object' &&
    obj !== null &&
    'content' in obj &&
    'startTime' in obj &&
    'endTime' in obj
  )
}

// 断言函数
function assertIsNumber(value: unknown): asserts value is number {
  if (typeof value !== 'number') {
    throw new Error('Expected number')
  }
}
```

### 运行时验证
```typescript
// 结合 Zod 进行运行时验证
import { z } from 'zod'

const SubtitleSchema = z.object({
  id: z.string(),
  content: z.string().min(1),
  startTime: z.number().positive(),
  endTime: z.number().positive(),
})

type SubtitleFromSchema = z.infer<typeof SubtitleSchema>
```

## 模块化类型

### 类型导出
```typescript
// 主要类型导出
export type { Subtitle, SubtitleStatus, SubtitleAction }

// 工具类型导出
export type {
  CreateSubtitleData,
  UpdateSubtitleData,
  SubtitleFilter,
}

// 默认导出
export { SubtitleSchema } from './schemas'
```

### 类型扩展
```typescript
// 扩展第三方库类型
declare module 'wavesurfer.js' {
  interface WaveSurfer {
    customMethod(): void
  }
}

// 全局类型扩展
declare global {
  interface Window {
    customProperty: string
  }
}
```

## 性能优化

### 类型优化
```typescript
// 使用 const assertions 优化
const SUBTITLE_FORMATS = ['srt', 'vtt', 'lrc'] as const
type SubtitleFormat = typeof SUBTITLE_FORMATS[number]

// 使用 readonly 减少类型检查开销
interface ReadonlySubtitle {
  readonly id: string
  readonly content: string
  readonly startTime: number
  readonly endTime: number
}
```

### 延迟类型解析
```typescript
// 使用 import type 进行类型导入
import type { Subtitle } from './subtitle'
import type { ApiResponse } from './api'

// 条件类型的优化
type OptimizedType<T> = T extends string 
  ? string 
  : T extends number 
    ? number 
    : unknown
```

## 开发规范

### 命名约定
- 接口使用 PascalCase (如 `Subtitle`, `ApiResponse`)
- 类型别名使用 PascalCase (如 `SubtitleStatus`)
- 枚举使用 PascalCase (如 `SubtitleFormat`)
- 泛型参数使用单个大写字母 (如 `T`, `K`, `V`)

### 文档注释
```typescript
/**
 * 字幕数据结构
 * @interface Subtitle
 */
interface Subtitle {
  /** 字幕唯一标识符 */
  id: string
  
  /** 字幕文本内容 */
  content: string
  
  /** 开始时间 (秒) */
  startTime: number
  
  /** 结束时间 (秒) */
  endTime: number
}
```

## 开发规范

遵循 [全局技术规范](mdc:.cursor/rules/global-standards.mdc) 中的开发要求。

## 相关依赖

- 与 [api-design](mdc:.cursor/rules/api-design.mdc) 中的 API 类型保持同步
- 为 [components](mdc:components) 和 [composables](mdc:composables) 提供类型支持
- 集成 [utils](mdc:utils) 中的工具函数类型
- 遵循 [architecture](mdc:.cursor/rules/architecture.mdc) 定义的架构模式

---
description: 
globs: 
alwaysApply: false
---
# 字幕处理领域规则

该规则定义字幕编辑系统的业务逻辑、工作流程和领域模型。

## 领域模型

### 核心实体
```
Subtitle (字幕)
├── content: 文本内容
├── startTime: 开始时间
├── endTime: 结束时间
├── language: 语言标识
├── style: 样式配置
└── metadata: 元数据信息

Project (项目)
├── subtitles: 字幕集合
├── audioFile: 关联音频
├── settings: 项目设置
└── history: 编辑历史
```

### 值对象
```typescript
// 时间范围
interface TimeRange {
  start: number
  end: number
  duration: number
}

// 字幕样式
interface SubtitleStyle {
  fontSize: number
  fontFamily: string
  color: string
  backgroundColor?: string
  position: 'top' | 'center' | 'bottom'
}
```

## 业务规则

### 时间校验规则
- 开始时间必须小于结束时间
- 字幕最小持续时间: 0.5秒
- 字幕最大持续时间: 30秒
- 相邻字幕间隔最小: 0.1秒

### 内容校验规则
- 字幕内容不能为空
- 单条字幕最大字符数: 200
- 支持多行文本 (最多3行)
- 特殊字符的处理和转义

### 格式转换规则
- SRT ↔ LRC 格式互转
- 时间格式统一 (毫秒精度)
- 字符编码标准化 (UTF-8)
- 样式信息的保持和映射

## 工作流程

### 字幕创建流程
```mermaid
graph TD
    A[选择时间点] --> B[输入字幕内容]
    B --> C[验证格式]
    C --> D{验证通过?}
    D -->|是| E[创建字幕]
    D -->|否| F[显示错误]
    F --> B
    E --> G[更新时间轴]
    G --> H[保存变更]
```

### 字幕编辑流程
```mermaid
graph TD
    A[选择字幕] --> B[进入编辑模式]
    B --> C[修改内容/时间]
    C --> D[实时验证]
    D --> E{验证通过?}
    E -->|是| F[应用变更]
    E -->|否| G[显示提示]
    G --> C
    F --> H[更新状态]
    H --> I[自动保存]
```

### 批量操作流程
```mermaid
graph TD
    A[选择操作类型] --> B[选择目标字幕]
    B --> C[预览变更]
    C --> D[用户确认]
    D --> E{确认执行?}
    E -->|是| F[执行批量操作]
    E -->|否| G[取消操作]
    F --> H[更新所有字幕]
    H --> I[记录历史]
```

## 核心算法

### 时间同步算法
```typescript
/**
 * 字幕与音频的时间同步
 */
function syncSubtitleWithAudio(
  subtitle: Subtitle,
  audioCurrentTime: number,
  playbackRate: number = 1
): boolean {
  const adjustedTime = audioCurrentTime * playbackRate
  return adjustedTime >= subtitle.startTime && 
         adjustedTime <= subtitle.endTime
}
```

### 自动分割算法
```typescript
/**
 * 基于静音检测的自动分割
 */
function autoSplitSubtitle(
  audio: AudioBuffer,
  silenceThreshold: number = -40,
  minDuration: number = 0.5
): TimeRange[] {
  // 检测静音区间
  // 生成时间分割点
  // 返回推荐的字幕时间段
}
```

### 智能合并算法
```typescript
/**
 * 智能字幕合并
 */
function smartMergeSubtitles(
  subtitles: Subtitle[],
  maxDuration: number = 5,
  maxLength: number = 100
): Subtitle[] {
  // 分析字幕间隔和内容长度
  // 智能判断合并可行性
  // 执行合并并优化时间轴
}
```

## 状态管理模式

### 字幕状态机
```typescript
type SubtitleState = 
  | 'idle'       // 空闲状态
  | 'creating'   // 创建中
  | 'editing'    // 编辑中
  | 'validating' // 验证中
  | 'saving'     // 保存中
  | 'error'      // 错误状态

interface SubtitleStateMachine {
  current: SubtitleState
  context: SubtitleContext
  actions: SubtitleActions
}
```

### 历史记录管理
```typescript
interface EditHistory {
  id: string
  timestamp: Date
  action: 'create' | 'update' | 'delete' | 'move'
  before: Subtitle | null
  after: Subtitle | null
  metadata: {
    user?: string
    reason?: string
  }
}
```

## 性能优化策略

### 虚拟化渲染
- 大量字幕列表的虚拟滚动
- 视口内字幕的按需渲染
- 时间轴的分段加载

### 增量更新
- 仅更新变更的字幕项
- 批量更新的防抖处理
- 智能的重新渲染策略

### 缓存机制
- 字幕解析结果缓存
- 音频分析数据缓存
- 用户偏好设置缓存

## 错误处理

### 数据验证错误
```typescript
class SubtitleValidationError extends Error {
  constructor(
    public field: keyof Subtitle,
    public value: unknown,
    public constraint: string
  ) {
    super(`Invalid ${field}: ${constraint}`)
  }
}
```

### 时间冲突错误
```typescript
class TimeConflictError extends Error {
  constructor(
    public subtitle1: Subtitle,
    public subtitle2: Subtitle
  ) {
    super('Subtitle time ranges conflict')
  }
}
```

### 格式转换错误
```typescript
class FormatConversionError extends Error {
  constructor(
    public from: string,
    public to: string,
    public reason: string
  ) {
    super(`Cannot convert from ${from} to ${to}: ${reason}`)
  }
}
```

## 质量保证

### 自动化测试
- 时间轴逻辑的单元测试
- 格式转换的集成测试
- 用户交互的端到端测试

### 数据完整性
- 字幕数据的一致性检查
- 时间轴的连续性验证
- 格式兼容性测试

### 性能监控
- 大文件处理的性能指标
- 内存使用情况监控
- 用户操作响应时间

## 扩展性设计

### 插件架构
- 字幕格式解析器插件
- 自动化处理插件
- 自定义验证规则插件

### 国际化支持
- 多语言字幕处理
- 地区特定的格式要求
- 文本方向和排版支持

## 开发规范

遵循 [全局技术规范](mdc:.cursor/rules/global-standards.mdc) 中的开发要求。

## 相关依赖

- 使用 [types](mdc:types) 中定义的字幕类型
- 集成 [utils](mdc:utils) 中的字幕处理工具
- 与 [stores](mdc:stores) 中的字幕状态管理协作
- 遵循 [architecture](mdc:.cursor/rules/architecture.mdc) 定义的架构模式

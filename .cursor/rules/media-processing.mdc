---
description: 
globs: 
alwaysApply: false
---
# 媒体处理规则

该规则定义音视频处理、波形可视化和媒体交互的技术规范。

## 核心技术栈

### 音频处理
- **WaveSurfer.js**: 主要音频波形库

### 视频处理
- **Video.js**: 视频播放器框架

## 音频架构模式

### WaveSurfer 集成
```typescript
interface WaveSurferConfig {
  container: HTMLElement
  waveColor: string
  progressColor: string
  cursorColor: string
  height: number
  normalize: boolean
  backend: 'WebAudio' | 'MediaElement'
  interact: boolean
}

// 标准初始化模式
const wavesurfer = WaveSurfer.create(config)
```

### 音频处理管道
```
音频文件 → 解码 → AudioBuffer → 波形分析 → 可视化渲染
                     ↓
              特征提取 → 静音检测 → 自动分割建议
```

### 波形数据管理
```typescript
interface WaveformData {
  peaks: Float32Array[]    // 波形峰值数据
  length: number          // 总长度
  sampleRate: number      // 采样率
  duration: number        // 音频时长
  channels: number        // 声道数
}
```

## 交互模式设计

### 播放控制模式
```typescript
interface PlaybackController {
  play(): Promise<void>
  pause(): void
  seek(time: number): void
  setPlaybackRate(rate: number): void
  getCurrentTime(): number
  getDuration(): number
}
```

### 区域选择模式
```typescript
interface RegionManager {
  addRegion(options: RegionOptions): Region
  removeRegion(id: string): void
  getRegions(): Region[]
  clearRegions(): void
  onRegionUpdate(callback: RegionUpdateCallback): void
}

interface Region {
  id: string
  start: number
  end: number
  color: string
  loop: boolean
  drag: boolean
  resize: boolean
}
```

### 缩放和导航
```typescript
interface ZoomController {
  zoom(factor: number): void
  zoomIn(): void
  zoomOut(): void
  fitToContainer(): void
  scrollTo(time: number): void
  getZoomLevel(): number
}
```

## 性能优化策略

### 音频加载优化
```typescript
// 渐进式加载
const loadAudioProgressively = async (url: string, onProgress: ProgressCallback) => {
  const response = await fetch(url)
  const reader = response.body?.getReader()
  
  // 流式处理音频数据
  while (reader) {
    const { done, value } = await reader.read()
    if (done) break
    
    onProgress(value)
    // 边下载边解码
  }
}
```

### 波形渲染优化
```typescript
// 分层渲染策略
interface WaveformRenderer {
  renderOverview(): void      // 概览波形
  renderDetail(start: number, end: number): void  // 详细波形
  renderCursor(time: number): void     // 播放光标
  updateRegions(): void       // 区域更新
}

// 使用 Canvas 优化
const optimizeCanvasRendering = (canvas: HTMLCanvasElement) => {
  const ctx = canvas.getContext('2d')!
  
  // 启用硬件加速
  ctx.imageSmoothingEnabled = false
  
  // 使用 OffscreenCanvas 进行后台渲染
  const offscreen = new OffscreenCanvas(canvas.width, canvas.height)
  
  // 批量绘制减少重绘
  ctx.save()
  // 绘制操作
  ctx.restore()
}
```

### 内存管理
```typescript
// 音频缓冲管理
class AudioBufferManager {
  private buffers = new Map<string, AudioBuffer>()
  private maxSize = 100 * 1024 * 1024 // 100MB
  
  getBuffer(id: string): AudioBuffer | null {
    return this.buffers.get(id) || null
  }
  
  setBuffer(id: string, buffer: AudioBuffer): void {
    this.cleanup()
    this.buffers.set(id, buffer)
  }
  
  private cleanup(): void {
    // LRU 清理策略
    if (this.getCurrentSize() > this.maxSize) {
      // 清理最旧的缓冲
    }
  }
}
```

## 音频分析功能

### 频谱分析
```typescript
interface SpectrumAnalyzer {
  getFrequencyData(): Uint8Array
  getTimedomainData(): Uint8Array
  getSpectogram(start: number, end: number): Float32Array[]
}

// FFT 分析
const analyzeFrequency = (audioBuffer: AudioBuffer) => {
  const fftSize = 2048
  const analyser = audioContext.createAnalyser()
  analyser.fftSize = fftSize
  
  const frequencyData = new Uint8Array(analyser.frequencyBinCount)
  analyser.getByteFrequencyData(frequencyData)
  
  return frequencyData
}
```

### 静音检测
```typescript
interface SilenceDetector {
  detectSilence(
    audioBuffer: AudioBuffer,
    threshold: number,
    minDuration: number
  ): TimeRange[]
}

const detectSilenceRegions = (
  audioBuffer: AudioBuffer,
  threshold: number = -40,
  minDuration: number = 0.5
): TimeRange[] => {
  const channelData = audioBuffer.getChannelData(0)
  const sampleRate = audioBuffer.sampleRate
  const silenceRegions: TimeRange[] = []
  
  // 分析音频数据，检测静音区间
  let silenceStart = -1
  
  for (let i = 0; i < channelData.length; i++) {
    const amplitude = Math.abs(channelData[i])
    const dbLevel = 20 * Math.log10(amplitude)
    
    if (dbLevel < threshold) {
      if (silenceStart === -1) {
        silenceStart = i / sampleRate
      }
    } else {
      if (silenceStart !== -1) {
        const duration = i / sampleRate - silenceStart
        if (duration >= minDuration) {
          silenceRegions.push({
            start: silenceStart,
            end: i / sampleRate,
            duration
          })
        }
        silenceStart = -1
      }
    }
  }
  
  return silenceRegions
}
```

### 音量分析
```typescript
interface VolumeAnalyzer {
  getRMSLevel(start: number, end: number): number
  getPeakLevel(start: number, end: number): number
  getAverageLevel(): number
  getNormalizationFactor(): number
}
```

## 视频处理集成

### Video.js 配置
```typescript
interface VideoPlayerConfig {
  fluid: boolean
  responsive: boolean
  controls: boolean
  preload: 'auto' | 'metadata' | 'none'
  techOrder: string[]
  plugins: Record<string, any>
}

// 视频播放器初始化
const initVideoPlayer = (element: HTMLVideoElement, config: VideoPlayerConfig) => {
  return videojs(element, {
    fluid: true,
    responsive: true,
    controls: true,
    preload: 'metadata',
    techOrder: ['html5'],
    ...config
  })
}
```

### 视频字幕同步
```typescript
interface VideoSubtitleSync {
  addTextTrack(subtitles: Subtitle[]): void
  updateSubtitleTiming(subtitle: Subtitle): void
  showSubtitle(time: number): Subtitle | null
  hideSubtitles(): void
}

const syncVideoSubtitles = (player: any, subtitles: Subtitle[]) => {
  player.on('timeupdate', () => {
    const currentTime = player.currentTime()
    const activeSubtitle = findActiveSubtitle(subtitles, currentTime)
    
    if (activeSubtitle) {
      displaySubtitle(activeSubtitle)
    } else {
      hideSubtitle()
    }
  })
}
```

## 实时处理

### Web Workers 集成
```typescript
// 音频处理 Worker
class AudioProcessingWorker {
  private worker: Worker
  
  constructor() {
    this.worker = new Worker('/workers/audio-processor.js')
  }
  
  processAudio(audioData: ArrayBuffer): Promise<ProcessedAudioData> {
    return new Promise((resolve) => {
      this.worker.postMessage({ audioData })
      this.worker.onmessage = (event) => {
        resolve(event.data)
      }
    })
  }
}
```

### 实时音频分析
```typescript
interface RealtimeAnalyzer {
  startAnalysis(): void
  stopAnalysis(): void
  onVolumeChange(callback: (volume: number) => void): void
  onFrequencyChange(callback: (frequencies: Float32Array) => void): void
}
```

## 错误处理和降级

### 音频加载错误
```typescript
class AudioLoadError extends Error {
  constructor(
    public url: string,
    public reason: string
  ) {
    super(`Failed to load audio: ${url} - ${reason}`)
  }
}

// 降级策略
const loadAudioWithFallback = async (urls: string[]) => {
  for (const url of urls) {
    try {
      return await loadAudio(url)
    } catch (error) {
      console.warn(`Failed to load ${url}, trying next...`)
    }
  }
  throw new AudioLoadError(urls[0], 'All sources failed')
}
```

### 浏览器兼容性
```typescript
const checkBrowserSupport = () => {
  const support = {
    webAudio: !!(window.AudioContext || window.webkitAudioContext),
    mediaRecorder: !!window.MediaRecorder,
    webRTC: !!(navigator.mediaDevices && navigator.mediaDevices.getUserMedia)
  }
  
  return support
}
```

## 开发规范

遵循 [全局技术规范](mdc:.cursor/rules/global-standards.mdc) 中的开发要求。

## 相关依赖

- 集成 [composables](mdc:composables) 中的音频处理 hooks
- 与 [components](mdc:components) 中的播放器组件协作
- 使用 [types](mdc:types) 中定义的音频类型
- 遵循 [architecture](mdc:.cursor/rules/architecture.mdc) 定义的架构模式

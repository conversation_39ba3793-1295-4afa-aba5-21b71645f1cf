---
description: 
globs: 
alwaysApply: false
---
# composables 目录规则

该目录用于存放可复用的组合式逻辑（Vue 3 Composition API hooks），用于处理音频波形、字幕编辑、时间校验等核心业务逻辑。

## 设计原则

- **关注分离**: 每个 composable 专注于单一业务逻辑域
- **响应式优先**: 充分利用 Vue 3 的响应式系统
- **可组合性**: composables 之间可以相互组合使用
- **无副作用**: 纯函数式设计，避免隐式状态修改

## 主要内容

- 每个文件通常以 `use` 开头，表示一个独立的功能模块。
- 这些 hooks 负责与播放器、波形、字幕等功能的交互和状态管理。
- 提供响应式的数据和方法供组件使用

## 关键文件及用途

### 音频波形处理
- [useWaveSurfer.ts](mdc:composables/useWaveSurfer.ts)：封装 WaveSurfer 实例的创建与控制，负责音频波形的渲染与交互。
- [useWaveformRegions.ts](mdc:composables/useWaveformRegions.ts)：管理波形上的区域（regions），用于字幕区间的拖拽、同步等。
- [useWaveformPlayerSync.ts](mdc:composables/useWaveformPlayerSync.ts)：实现波形播放器与字幕、UI 的同步。

### 字幕编辑处理
- [useSubtitleEditing.ts](mdc:composables/useSubtitleEditing.ts)：处理字幕的编辑、合并、拆分等操作。
- [useSubtitleTimeValidation.ts](mdc:composables/useSubtitleTimeValidation.ts)：校验字幕时间的合法性。
- [useSubtitleActions.ts](mdc:composables/useSubtitleActions.ts)：封装字幕的常用操作（如添加、删除、合并等）。
- [useMergeHighlight.ts](mdc:composables/useMergeHighlight.ts)：处理字幕合并时的高亮显示。

## 架构模式

### 数据流
```
组件 → composable → store/utils → 底层服务
```

### 状态管理集成
- composables 通过 store 进行全局状态访问
- 本地状态使用 ref/reactive 管理
- 计算属性用于派生状态

### 错误处理
- 统一的错误处理机制
- 优雅降级策略
- 用户友好的错误提示

## 相关依赖

- 这些 hooks 通常会调用 [stores](mdc:stores) 进行全局状态管理。
- 调用 [utils](mdc:utils) 进行工具函数处理。
- 与 [components](mdc:components) 形成双向数据绑定。

## 开发规范

遵循 [全局技术规范](mdc:.cursor/rules/global-standards.mdc) 中的开发要求。

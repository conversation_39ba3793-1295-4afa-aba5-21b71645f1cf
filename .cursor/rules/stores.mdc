---
description: 
globs: 
alwaysApply: false
---
# stores 目录规则

该目录用于存放全局状态管理模块，基于 Pinia 组合式 API，用于管理播放器、字幕、LRC 文件等全局数据。

## 设计原则

- **组合式优先**: 使用 Pinia 组合式 API 模式
- **集中化状态**: 全局共享的状态统一在 store 中管理
- **模块化设计**: 按业务领域拆分不同的 store 模块
- **类型安全**: 充分利用 TypeScript 进行类型约束
- **持久化策略**: 关键状态通过 persistedstate 插件持久化

## 主要内容

- 每个 store 文件负责一类全局状态的管理与变更
- stores 使用组合式 API 模式定义响应式状态、计算属性和操作方法
- 集成 Pinia 的类型推导和开发工具支持

## 关键文件及用途

- [playerStore.ts](mdc:stores/playerStore.ts)：管理音频播放器的播放状态、进度、音量等
- [subtitleStore.ts](mdc:stores/subtitleStore.ts)：管理字幕数据、编辑历史、查找替换等核心逻辑
- [lrcStore.ts](mdc:stores/lrcStore.ts)：管理 LRC 歌词文件的解析、状态与同步

## 组合式 API 架构模式

### Store 定义模式
```typescript
// 推荐的组合式 API 模式
export const usePlayerStore = defineStore('player', () => {
  // 响应式状态
  const isPlaying = ref(false)
  const currentTime = ref(0)
  const volume = ref(1)
  const playbackRate = ref(1)
  
  // 计算属性
  const progress = computed(() => {
    return duration.value > 0 ? currentTime.value / duration.value : 0
  })
  
  const isAtEnd = computed(() => {
    return currentTime.value >= duration.value
  })
  
  // 操作方法
  function play() {
    isPlaying.value = true
  }
  
  function pause() {
    isPlaying.value = false
  }
  
  function seek(time: number) {
    currentTime.value = Math.max(0, Math.min(time, duration.value))
  }
  
  async function loadAudio(url: string) {
    try {
      // 加载逻辑
      isLoading.value = true
      // ...
    } catch (error) {
      handleError(error)
    } finally {
      isLoading.value = false
    }
  }
  
  // 返回公共 API
  return {
    // 状态
    isPlaying: readonly(isPlaying),
    currentTime,
    volume,
    playbackRate,
    
    // 计算属性
    progress,
    isAtEnd,
    
    // 方法
    play,
    pause,
    seek,
    loadAudio,
  }
})
```

### 状态组织模式
```typescript
// 状态分组
const state = () => ({
  // 播放控制状态
  playback: {
    isPlaying: ref(false),
    currentTime: ref(0),
    duration: ref(0),
  },
  
  // 音频设置状态
  settings: {
    volume: ref(1),
    playbackRate: ref(1),
    loop: ref(false),
  },
  
  // UI 状态
  ui: {
    isLoading: ref(false),
    error: ref<string | null>(null),
    showWaveform: ref(true),
  }
})
```

### 跨 Store 组合模式
```typescript
export const useSubtitleStore = defineStore('subtitle', () => {
  // 引用其他 store
  const playerStore = usePlayerStore()
  
  const subtitles = ref<Subtitle[]>([])
  const selectedSubtitle = ref<Subtitle | null>(null)
  
  // 与播放器同步的计算属性
  const currentSubtitle = computed(() => {
    const time = playerStore.currentTime
    return subtitles.value.find(sub => 
      time >= sub.startTime && time <= sub.endTime
    )
  })
  
  // 监听播放器状态变化
  watch(
    () => playerStore.currentTime,
    (newTime) => {
      syncWithPlayback(newTime)
    }
  )
  
  return {
    subtitles,
    selectedSubtitle,
    currentSubtitle,
    // ...
  }
})
```

## 持久化配置

### 组合式 API 持久化
```typescript
export const usePlayerStore = defineStore('player', () => {
  const volume = ref(1)
  const playbackRate = ref(1)
  const lastPosition = ref(0)
  
  return {
    volume,
    playbackRate,
    lastPosition,
  }
}, {
  persist: {
    key: 'player-settings',
    storage: persistedState.localStorage,
    pick: ['volume', 'playbackRate', 'lastPosition']
  }
})
```

### 选择性持久化
```typescript
export const useAppStore = defineStore('app', () => {
  // 需要持久化的状态
  const userPreferences = ref({
    theme: 'light',
    language: 'zh',
    autoSave: true
  })
  
  // 会话状态 (不持久化)
  const currentSession = ref({
    startTime: Date.now(),
    activeTab: 'subtitle'
  })
  
  return {
    userPreferences,
    currentSession,
  }
}, {
  persist: {
    pick: ['userPreferences']
  }
})
```

## 状态管理最佳实践

### 响应式数据模式
```typescript
// 推荐: 使用 ref 和 computed
const count = ref(0)
const doubleCount = computed(() => count.value * 2)

// 推荐: 使用 reactive 处理复杂对象
const state = reactive({
  user: null,
  permissions: [],
  settings: {}
})
```

### 异步操作模式
```typescript
export const useDataStore = defineStore('data', () => {
  const data = ref([])
  const loading = ref(false)
  const error = ref<string | null>(null)
  
  async function fetchData() {
    loading.value = true
    error.value = null
    
    try {
      const result = await api.getData()
      data.value = result
    } catch (err) {
      error.value = err.message
    } finally {
      loading.value = false
    }
  }
  
  return {
    data: readonly(data),
    loading: readonly(loading),
    error: readonly(error),
    fetchData,
  }
})
```

### 类型安全模式
```typescript
interface PlayerState {
  isPlaying: boolean
  currentTime: number
  volume: number
}

export const usePlayerStore = defineStore('player', (): PlayerState & {
  play: () => void
  pause: () => void
} => {
  const isPlaying = ref(false)
  const currentTime = ref(0)
  const volume = ref(1)
  
  function play() {
    isPlaying.value = true
  }
  
  function pause() {
    isPlaying.value = false
  }
  
  return {
    isPlaying,
    currentTime,
    volume,
    play,
    pause,
  }
})
```

## 错误边界处理

### 统一错误处理
```typescript
export const useErrorStore = defineStore('error', () => {
  const errors = ref<AppError[]>([])
  
  function addError(error: AppError) {
    errors.value.push({
      ...error,
      timestamp: Date.now(),
      id: generateId()
    })
  }
  
  function clearError(id: string) {
    const index = errors.value.findIndex(e => e.id === id)
    if (index > -1) {
      errors.value.splice(index, 1)
    }
  }
  
  return {
    errors: readonly(errors),
    addError,
    clearError,
  }
})
```

## 开发规范

### Store 命名约定
- Store 函数使用 `use` + 领域名 + `Store` 格式 (如 `usePlayerStore`)
- Store ID 使用 kebab-case 格式 (如 'player', 'subtitle-editor')
- 响应式变量使用 camelCase 格式

### 导出模式
```typescript
// 推荐的导出方式
export const usePlayerStore = defineStore('player', () => {
  // store 实现
})

// 类型导出 (如需要)
export type PlayerStore = ReturnType<typeof usePlayerStore>
```

遵循 [全局技术规范](mdc:.cursor/rules/global-standards.mdc) 中的开发要求。

## 相关依赖

- stores 通常被 [components](mdc:components) 和 [composables](mdc:composables) 调用，实现全局数据的统一管理
- 与 [server](mdc:server) 层进行数据交互
- 调用 [utils](mdc:utils) 进行数据处理
- 遵循 [architecture](mdc:.cursor/rules/architecture.mdc) 定义的架构模式

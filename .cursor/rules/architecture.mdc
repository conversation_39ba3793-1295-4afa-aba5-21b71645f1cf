---
description: 
globs: 
alwaysApply: false
---
# 项目架构规则

本文档描述字幕编辑器项目的整体架构设计和模块间关系。

## 架构概览

```
┌─────────────────────────────────────────────────────────────┐
│                    Nuxt 3 应用层                              │
├─────────────────┬─────────────────┬─────────────────────────┤
│     Pages       │   Layouts       │      Components         │
│   (路由页面)     │   (布局组件)     │     (UI组件)            │
├─────────────────┼─────────────────┴─────────────────────────┤
│  Composables    │              Stores                       │
│  (组合式逻辑)    │            (状态管理)                      │
├─────────────────┼─────────────────┬─────────────────────────┤
│     Utils       │     Types       │       Common            │
│   (工具函数)     │   (类型定义)     │      (公共资源)          │
├─────────────────┴─────────────────┴─────────────────────────┤
│                    Server 层                                │
│                 (tRPC + API)                               │
└─────────────────────────────────────────────────────────────┘
```

## 核心设计原则

### 1. 分层架构
- **表现层**: Pages + Layouts + Components
- **业务逻辑层**: Composables + Stores  
- **数据层**: Server + Utils + Types
- **基础设施层**: Plugins + Common

### 2. 依赖方向
- 高层模块不依赖低层模块
- 依赖抽象而非具体实现
- 单向数据流和事件传播

### 3. 模块化设计
- 按功能领域划分模块
- 高内聚、低耦合
- 清晰的接口边界

## 数据流架构

### 用户交互流
```
用户操作 → Components → Composables → Stores → Server API
```

### 状态同步流
```
Server → Stores → Composables → Components → DOM更新
```

### 音频处理流
```
音频文件 → WaveSurfer → useWaveSurfer → AudioPlayer → 波形展示
```

### 字幕编辑流
```
字幕数据 → subtitleStore → useSubtitleEditing → SubtitleEditor → 用户界面
```

## 关键架构组件

### 状态管理架构
- **集中式存储**: Pinia stores 管理全局状态
- **本地状态**: Composables 管理组件级状态
- **持久化**: 关键状态自动持久化到本地存储

### API 架构
- **类型安全**: tRPC 提供端到端类型安全
- **实时通信**: 支持流式响应和实时更新
- **错误处理**: 统一的错误处理和重试机制

### 组件架构
- **原子设计**: 从基础组件到复合组件的层次结构
- **属性驱动**: 通过 props 进行数据传递
- **事件通信**: 通过事件进行逆向通信

## 技术集成策略

### 音频处理集成
- WaveSurfer.js 作为核心音频引擎
- Web Audio API 进行高级音频处理
- Canvas 2D 用于自定义波形渲染

### AI 服务集成
- 统一的 AI 服务抽象层
- 支持多种 AI 提供商
- 异步处理和流式响应

### 文件处理集成
- 支持多种字幕格式 (SRT, LRC, VTT)
- 统一的解析和转换接口
- 批量处理和导入导出

## 性能架构

### 代码分割
- 页面级别的懒加载
- 组件级别的异步加载
- 第三方库的按需引入

### 数据优化
- 虚拟滚动处理大量字幕
- 音频缓冲和预加载
- 计算属性的合理使用

### 缓存策略
- HTTP 缓存配置
- 浏览器存储利用
- 内存缓存管理

## 开发规范

遵循 [全局技术规范](mdc:.cursor/rules/global-standards.mdc) 中的开发要求。

---
description: 
globs: 
alwaysApply: false
---
# 全局技术规范

本规范定义了整个项目的通用开发标准和技术要求，所有模块都应遵循这些规范。

## 核心技术栈

- **框架**: Nuxt 3 + Vue 3 + TypeScript
- **状态管理**: Pinia + 持久化插件
- **API层**: tRPC + Zod验证
- **UI**: nuxt UI + Tailwind CSS
- **音频处理**: WaveSurfer.js + Video.js

## 开发规范

### 代码组织
- 优先使用 `lodash-es` 进行数据处理和工具函数
- 所有常量定义在 [constants.ts](mdc:common/constants.ts) 中
- TypeScript类型定义统一放置在 [types](mdc:types) 目录
- 公共工具函数放置在 [utils](mdc:utils) 目录

### 命名约定
- 组合式函数以 `use` 开头 (如 `useWaveSurfer`)
- 状态管理文件以 `Store` 结尾 (如 `playerStore`)
- 组件使用PascalCase命名
- 文件名使用camelCase或kebab-case

### 依赖管理
- 优先使用项目已集成的依赖库
- 新增依赖需考虑bundle大小影响
- 音频/视频处理相关功能优先使用已集成的WaveSurfer.js和Video.js

### 性能要求
- 组件懒加载和代码分割
- 大数据列表使用虚拟滚动
- 音频处理采用Web Workers当适用时

## 引用此规范

其他规则文件应引用此规范而非重复定义技术要求：

```markdown
遵循 [全局技术规范](mdc:.cursor/rules/global-standards.mdc) 中的开发要求。
```

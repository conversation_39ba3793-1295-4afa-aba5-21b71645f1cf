---
description: 
globs: 
alwaysApply: false
---
# utils 目录规则

该目录用于存放全局可复用的工具函数，涵盖字幕、LRC、SRT、时间处理等通用逻辑。

## 设计原则

- **纯函数优先**: 工具函数应该是无副作用的纯函数
- **单一职责**: 每个工具函数专注于解决一个特定问题
- **类型安全**: 充分利用 TypeScript 进行参数和返回值约束
- **可测试性**: 函数设计便于单元测试

## 主要内容

- 每个文件聚焦于一类工具函数，供项目各处调用
- 工具函数优先使用 lodash-es，避免重复造轮子
- 提供类型安全的API接口

## 关键文件及用途

### 字幕处理工具
- [srtUtils.ts](mdc:utils/srtUtils.ts)：SRT 字幕相关的处理工具
- [srtConverter.ts](mdc:utils/srtConverter.ts)：LRC 与 SRT 格式互转工具

### 歌词处理工具
- [lrcParser.ts](mdc:utils/lrcParser.ts)：LRC 歌词文件的解析与格式转换

### 通用工具
- [timeUtils.ts](mdc:utils/timeUtils.ts)：时间格式转换、校验等通用工具

## 架构模式

### 功能分层
```
业务逻辑层 (composables/stores)
      ↓
工具函数层 (utils)
      ↓
第三方库层 (lodash-es等)
```

### 错误处理
- 统一的错误类型定义
- 优雅的错误返回机制
- 详细的错误信息提供

### 性能考虑
- 避免不必要的计算
- 合理使用缓存机制
- 大数据处理的优化策略

## lodash-es 集成策略

- 优先使用 lodash-es 提供的函数
- 自定义工具函数作为 lodash-es 的补充
- 保持 API 设计的一致性

示例：
```typescript
import { cloneDeep, uniqBy } from "lodash-es"

// 推荐: 使用 lodash-es
export const deduplicateSubtitles = (subtitles: Subtitle[]) => 
  uniqBy(subtitles, 'id')

// 补充: 业务特定的工具函数
export const formatSubtitleTime = (seconds: number) => {
  // 自定义格式化逻辑
}
```

## 相关依赖

- utils 目录下的函数常被 [composables](mdc:composables)、[stores](mdc:stores)、[components](mdc:components) 调用
- 与 [types](mdc:types) 目录中的类型定义紧密配合
- 为 [server](mdc:server) 层提供共享的处理逻辑

## 开发规范

遵循 [全局技术规范](mdc:.cursor/rules/global-standards.mdc) 中的开发要求。

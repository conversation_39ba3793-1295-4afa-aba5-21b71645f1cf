---
description: 
globs: 
alwaysApply: false
---
# pages 目录规则

该目录用于存放 Nuxt 3 页面组件，负责应用的路由层级和页面组织。

## 设计原则

- **基于文件的路由**: 利用 Nuxt 3 的自动路由生成
- **页面职责单一**: 每个页面专注于特定的业务场景
- **SEO 友好**: 支持服务端渲染和元数据配置
- **响应式布局**: 适配不同设备和屏幕尺寸

## 页面架构

### 路由结构
```
pages/
├── index.vue              # 首页/欢迎页
├── demo.vue              # 演示页面
├── navigation.vue        # 导航页面
├── smartsubtitles.vue    # 智能字幕页面
├── translate.vue         # 翻译功能页面
├── deepseek.vue          # DeepSeek AI 集成页面
├── summary-demo.vue      # 摘要演示页面
└── music-service.vue     # 音乐服务页面
```

## 关键页面及用途

### 核心功能页面
- [index.vue](mdc:pages/index.vue): 应用首页，提供功能导航和快速入口
- [smartsubtitles.vue](mdc:pages/smartsubtitles.vue): 智能字幕编辑的主要工作台
- [translate.vue](mdc:pages/translate.vue): 字幕翻译功能页面
- [music-service.vue](mdc:pages/music-service.vue): 音乐服务集成页面

### AI 集成页面
- [deepseek.vue](mdc:pages/deepseek.vue): DeepSeek AI 服务集成和配置
- [summary-demo.vue](mdc:pages/summary-demo.vue): AI 摘要功能演示

### 工具页面
- [demo.vue](mdc:pages/demo.vue): 功能演示和测试页面
- [navigation.vue](mdc:pages/navigation.vue): 应用导航和页面索引

## 页面组织模式

### 页面组件结构
```vue
<script setup lang="ts">
// 页面级别的逻辑
// 数据获取和状态管理
// 生命周期钩子
</script>

<template>
  <!-- 页面布局和内容 -->
</template>

<style scoped>
/* 页面特定样式 */
</style>
```

### 元数据配置
```typescript
definePageMeta({
  title: '页面标题',
  description: '页面描述',
  keywords: '相关关键词',
  layout: 'default'
})
```

## 数据获取策略

### 服务端渲染 (SSR)
- 首屏关键数据的预获取
- SEO 优化的内容渲染
- 初始状态的服务端注入

### 客户端水合 (Hydration)
- 交互功能的客户端激活
- 动态内容的异步加载
- 用户状态的恢复

### 数据缓存
- 页面级别的数据缓存
- 跨页面的状态共享
- 智能的缓存失效策略

## 布局集成

### 默认布局
- 使用 [default.vue](mdc:layouts/default.vue) 作为基础布局
- 包含通用的头部、底部和导航
- 响应式的侧边栏和内容区域

### 布局切换
- 根据页面需求选择合适的布局
- 支持动态布局切换
- 布局间的平滑过渡

## 路由管理

### 导航守卫
- 页面访问权限控制
- 路由跳转的拦截处理
- 用户认证状态检查

### 路由参数
- URL 参数的类型安全处理
- 查询字符串的解析和验证
- 路由状态的持久化

### 页面过渡
- 页面切换的动画效果
- 加载状态的用户反馈
- 错误页面的优雅处理

## 性能优化

### 代码分割
- 页面级别的异步加载
- 路由懒加载配置
- 预加载策略优化

### 资源管理
- 图片和媒体文件的优化
- CSS 和 JavaScript 的压缩
- 第三方库的按需加载

## 开发规范

遵循 [全局技术规范](mdc:.cursor/rules/global-standards.mdc) 中的开发要求。

## 相关依赖

- 页面组件使用 [components](mdc:components) 构建用户界面
- 通过 [composables](mdc:composables) 处理页面逻辑
- 与 [stores](mdc:stores) 进行状态交互
- 遵循 [architecture](mdc:.cursor/rules/architecture.mdc) 定义的架构模式

---
description: 
globs: 
alwaysApply: false
---
# components 目录规则

该目录用于存放所有 UI 组件，负责音频播放器、字幕编辑、歌词展示等界面渲染与交互。

## 设计原则

- **单一职责**: 每个组件专注于一个特定的UI功能模块
- **数据驱动**: 组件通过 props 接收数据，通过事件向上传递变更
- **组合优于继承**: 优先使用组合式API和组件组合
- **响应式设计**: 支持不同屏幕尺寸的自适应布局

## 主要内容

- 每个文件为一个独立的 Vue 组件，通常对应界面上的一个功能模块。
- 组件通过 props、事件与外部交互，部分组件会调用 composables 逻辑。

## 关键文件及用途

### 音频播放相关
- [AudioPlayer.vue](mdc:components/AudioPlayer.vue)：音频播放控制组件
- [AudioWaveform.vue](mdc:components/AudioWaveform.vue)：音频波形可视化与交互组件
- [VideoPlayer.vue](mdc:components/VideoPlayer.vue)：视频播放器组件

### 字幕编辑相关
- [SubtitleEditor.vue](mdc:components/SubtitleEditor.vue)：字幕编辑主界面
- [SubtitleItem.vue](mdc:components/SubtitleItem.vue)：单条字幕的编辑与展示
- [SubtitleList.vue](mdc:components/SubtitleList.vue)：字幕列表渲染与操作
- [SubtitleToolbar.vue](mdc:components/SubtitleToolbar.vue)：字幕编辑工具栏

### 歌词显示相关
- [LrcDisplay.vue](mdc:components/LrcDisplay.vue)：LRC 歌词展示与滚动组件

### 通用工具组件
- [TimeInput.vue](mdc:components/TimeInput.vue)：时间输入与校验组件
- [FindReplaceDialog.vue](mdc:components/FindReplaceDialog.vue)：查找与替换弹窗
- [TopBar.vue](mdc:components/TopBar.vue)：顶部导航栏
- [RightPane.vue](mdc:components/RightPane.vue)：右侧面板容器

## 架构关系

- 组件通常调用 [composables](mdc:composables) 目录下的 hooks 实现业务逻辑
- 组件间通过 props、事件通信，部分组件会与 [stores](mdc:stores) 进行状态交互
- UI组件基于 Nuxt UI 和自定义的 [ui](mdc:components/ui) 子目录组件

## 开发规范

遵循 [全局技术规范](mdc:.cursor/rules/global-standards.mdc) 中的开发要求。

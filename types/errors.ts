/**
 * 翻译相关错误类型定义
 */

/**
 * 相似度匹配失败错误
 */
export class SimilarityMatchError extends Error {
  constructor(
    public chunkIndex: number,
    public similarity: number,
    public threshold: number,
    message?: string,
  ) {
    super(message || `分块${chunkIndex}相似度匹配失败: ${(similarity * 100).toFixed(1)}% < ${(threshold * 100).toFixed(1)}%`)
    this.name = 'SimilarityMatchError'
  }
}

/**
 * 翻译质量不达标错误
 */
export class TranslationQualityError extends Error {
  constructor(
    public chunkIndex: number,
    public qualityIssues: string[],
    message?: string,
  ) {
    super(message || `分块${chunkIndex}翻译质量不达标: ${qualityIssues.join(', ')}`)
    this.name = 'TranslationQualityError'
  }
}

/**
 * 重试次数耗尽错误
 */
export class RetryExhaustedError extends Error {
  constructor(
    public chunkIndex: number,
    public attempts: number,
    public lastError: Error,
    message?: string,
  ) {
    super(message || `分块${chunkIndex}重试次数耗尽(${attempts}次): ${lastError.message}`)
    this.name = 'RetryExhaustedError'
  }
}

/**
 * 翻译任务超时错误
 */
export class TranslationTimeoutError extends Error {
  constructor(
    public chunkIndex: number,
    public timeout: number,
    message?: string,
  ) {
    super(message || `分块${chunkIndex}翻译超时: ${timeout}ms`)
    this.name = 'TranslationTimeoutError'
  }
}

/**
 * 批量翻译失败错误
 */
export class BatchTranslationError extends Error {
  constructor(
    public failedChunks: Array<{ index: number; error: Error }>,
    message?: string,
  ) {
    super(message || `批量翻译失败: ${failedChunks.length}个分块失败`)
    this.name = 'BatchTranslationError'
  }
}

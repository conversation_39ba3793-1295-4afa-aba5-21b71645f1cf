// 课程相关类型定义

// 课程包数据结构
export interface CoursePack {
  id: string
  order: number
  title: string
  description: string
  isFree: boolean
  cover: string
  creatorId: string
  shareLevel: string
  categoryId?: string
  createdAt: string
  updatedAt: string
}

// 课程数据结构
export interface Course {
  id: string
  title: string
  description: string
  video: string
  order: number
  coursePackId: string
  type: 'normal' | 'music'
  mediaUrl?: string
  createdAt: string
  updatedAt: string
}

// 语句数据结构
export interface Statement {
  id: string
  order: number
  chinese: string
  english: string
  soundmark: string
  type: string
  image?: Record<string, unknown>
  courseId: string
  sentenceId?: string
  startTime?: number
  endTime?: number
  createdAt: string
  updatedAt: string
}

// API 输入类型
export interface CourseDetailInput {
  courseId: string
  coursePackId: string
}

export interface CoursePackInput {
  coursePackId: string
}

export interface CourseCreateInput {
  coursePackId: string
  title?: string
}

// API 响应类型
export interface CourseDetailResponse {
  course: Course
  statements: Statement[]
}

export interface CoursePackResponse {
  coursePack: CoursePack
  courses: Course[]
}

export interface CourseCreateResponse {
  course: Course
}

// 管理员课程包列表项类型（参考管理员接口文档）
export interface AdminCoursePack {
  id: string
  title: string
  description: string
  isFree: boolean
  cover: string
  createdAt: string
  updatedAt: string
  gameId: string
  shareLevel: string
  categoryId?: string
  isPinned: boolean
  position: number
}

export interface GetAdminCoursePacksResponse {
  success: boolean
  message: string
  data: AdminCoursePack[]
}

// 通用API响应类型
export interface BaseResponse<T = any> {
  success: boolean
  message: string
  data?: T
  error?: string
}

// 批量创建数据类型
export interface BatchCreateData {
  coursePackId: string
  title: string
  cover: string
  usedDefaultCover: boolean
  courses: Array<{
    courseId: string
    title: string
    sentenceCount: number
    elementCount: number
  }>
}

// 批量处理课程数据类型
export interface ProcessAllCoursesData {
  coursePackId: string
  totalCourses: number
  totalProcessedCount: number
  results: Array<{
    courseId: string
    courseTitle: string
    processedCount: number
    success: boolean
    error?: string
  }>
}

// 批量拆分课程数据类型
export interface SplitAllCoursesData {
  coursePackId: string
  totalCourses: number
  totalSentenceCount: number
  totalElementCount: number
  results: Array<{
    courseId: string
    courseTitle: string
    sentenceCount: number
    elementCount: number
    success: boolean
    error?: string
  }>
}

// 学习内容类型
export interface LearningContent {
  overview: {
    chineseTranslation: string
    englishExplanation: string
    functionAndContext: string
  }
  vocabularyAnalysis: Array<{
    word: string
    chineseTranslation: string
    phonetic: string
    partOfSpeech: string
    basicMeaning: string
    contextualMeaning: string
    commonPhrases: string
    synonyms: string
    antonyms: string
    memorizationTip: string
    exampleSentence: string
  }>
  grammarAnalysis: {
    sentenceType: string
    sentenceComponents: Array<{
      component: string
      grammaticalExplanation: string
      meaningInSentence: string
    }>
    tenseAndMood: string
    keyGrammarPoints: string
    specialStructures: string
    wordOrder: string
    grammarRulesApplication: string
    commonErrors: string
  }
  relatedExamples: Array<{
    example: string
    explanation: string
  }>
  culturalAndPracticalKnowledge: {
    culturalElements: string
    backgroundInformation: string
    practicalApplication: string
  }
}

export interface Phonetic {
  uk: string
  us: string
}
export interface WordDetail {
  pos: string
  word: string
  phonetic: Phonetic
  definition: string
}

export interface Sentence {
  id: string
  content: string
  chinese: string
  learningContent: LearningContent | null
  courseId: string
  elements: Element[]
  position: number
  wordDetails: WordDetail[]
}

// 注意：避免与原有 Course 类型冲突，重命名为 AdminCourse
export interface AdminCourse {
  id: string
  title: string
  description: string
  video: string
  coursePackId: string
  sentences: Sentence[]
  gameId: string
  createdAt: string
  updatedAt: string
}

export interface CourseInPackResponse {
  id: string
  title: string
  description: string
  video: string
  order: number
  coursePackId: string
  type: string
  mediaUrl: string
}

export interface CoursePackWithCoursesResponse {
  id: string
  title: string
  description: string
  cover: string
  type: string
  coursePackCategory: null
  tags: any[]
  author: {
    type: string
    name: string
  }
  usageCount: number
  courses: CourseInPackResponse[]
  isOwned: boolean
}

// 管理员课程详情响应的语句结构
export interface AdminCourseStatement {
  id: string
  order: number
  chinese: string
  english: string
  soundmark: string
  type: string
  sentenceId: string
  image: {
    width: number
    height: number
    fileKey: string
    description: string
  } | null
  startTime: number
  endTime: number
}

// 管理员课程详情响应数据结构
export interface AdminCourseDetailData {
  id: string
  title: string
  description: string
  video: string
  order: number
  coursePackId: string
  type: string
  mediaUrl: string
  statements: AdminCourseStatement[]
  sentences: any[]
  statementId: string
}

// 管理员课程详情完整响应结构
export interface AdminCourseDetailResponse {
  success: boolean
  message: string
  data: AdminCourseDetailData
}

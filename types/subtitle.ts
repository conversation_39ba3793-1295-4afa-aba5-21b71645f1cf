export interface Subtitle {
  uuid: string
  id: number
  startTime: string
  endTime: string
  text: string
  translationText: string
}

// Statement 类型定义
export interface Statement {
  id: string
  chinese: string
  english: string
  start: number // 秒数，保留三位小数
  end: number // 秒数，保留三位小数
}

// 字幕筛选类型定义
export type SubtitleFilterType = 'all' | 'textEmpty' | 'textOnly' | 'translationOnly' | 'bothEmpty' | 'hasContent' | 'timestampError'
